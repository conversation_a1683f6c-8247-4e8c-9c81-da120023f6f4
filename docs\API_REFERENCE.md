# DacMux API Reference

## Overview

This document provides a comprehensive reference for all public APIs in the DacMux system. The APIs are organized by class and include detailed parameter descriptions, return values, and usage examples.

## DacControl Class

The DacControl class manages DAC output, signal routing, and LFO generation.

### Constructor

#### `DacControl()`
Initializes the DacControl instance with default routing and LFO parameters.

**Parameters**: None

**Example**:
```cpp
DacControl dacControl;
```

### Initialization Methods

#### `void init()`
Initializes the DAC subsystem and configures hardware.

**Parameters**: None  
**Returns**: void

**Description**: Configures the Teensy DAC for 12-bit resolution, initializes double buffers, and sets up the singleton pointer. Must be called before using any other DAC functions.

**Example**:
```cpp
dacControl.init();
```

#### `void lfoEngineInit()`
Initializes the LFO (Low Frequency Oscillator) engine.

**Parameters**: None  
**Returns**: void

**Description**: Sets up initial phase and frequency values for all 8 LFO channels. Each channel has a different frequency to create varied modulation.

**Example**:
```cpp
dacControl.lfoEngineInit();
```

### Runtime Control Methods

#### `void lfoEngineUpdate()`
Updates LFO engine - generates new waveform values.

**Parameters**: None  
**Returns**: void

**Description**: Calculates new sine wave values for all LFO channels based on their individual frequencies and current phase positions. Should be called regularly from the main loop for smooth modulation.

**Example**:
```cpp
// In main loop
dacControl.lfoEngineUpdate();
```

#### `void setDacValue(uint8_t channel, uint16_t value)`
Sets DAC output value for a specific channel.

**Parameters**:
- `channel`: Channel number (0-7)
- `value`: 12-bit DAC value (0-4095)

**Returns**: void

**Description**: Updates the next buffer with a new 12-bit DAC value. The value will be output when the state machine processes this channel.

**Example**:
```cpp
dacControl.setDacValue(0, 2048);  // Set channel 0 to mid-scale
```

#### `void assignSourceToOutput(uint8_t output, DacDataSource src)`
Assigns a signal source to a specific output channel.

**Parameters**:
- `output`: Output channel number (0-7)
- `src`: Signal source to assign (see DacDataSource enum)

**Returns**: void

**Description**: Configures the routing system to connect a signal source to an output channel. This can be changed dynamically at runtime.

**Example**:
```cpp
dacControl.assignSourceToOutput(0, DacControl::DAC_SRC_MIDI_CC);
dacControl.assignSourceToOutput(1, DacControl::DAC_SRC_LFO);
```

### Utility Methods

#### `uint16_t scaleMidiToDac(uint8_t value)`
Converts MIDI CC value to 12-bit DAC value.

**Parameters**:
- `value`: MIDI CC value (0-127)

**Returns**: Scaled 12-bit DAC value (0-4095)

**Description**: Scales a MIDI Control Change value to the full 12-bit DAC range using linear interpolation.

**Example**:
```cpp
uint8_t midiValue = 64;  // MIDI mid-point
uint16_t dacValue = dacControl.scaleMidiToDac(midiValue);  // Returns ~2048
```

#### `void swapBuffers()`
Swaps double buffers for smooth output transitions.

**Parameters**: None  
**Returns**: void

**Description**: Exchanges current and next DAC value buffers to enable smooth transitions between output values without glitches.

**Example**:
```cpp
dacControl.swapBuffers();
```

### Accessor Methods

#### `DacDataSource getOutputSource(uint8_t channel) const`
Gets the currently assigned source for an output channel.

**Parameters**:
- `channel`: Channel number (0-7)

**Returns**: Currently assigned signal source

**Example**:
```cpp
DacControl::DacDataSource src = dacControl.getOutputSource(0);
```

#### `DacSourceFunc getSourceFunc(DacDataSource src) const`
Gets the function pointer for a specific signal source.

**Parameters**:
- `src`: Signal source type

**Returns**: Function pointer to source value retrieval function

**Example**:
```cpp
DacControl::DacSourceFunc func = dacControl.getSourceFunc(DacControl::DAC_SRC_LFO);
uint16_t value = func(0);  // Get LFO value for channel 0
```

#### `uint16_t* getCurrentDacValues()`
Gets pointer to current DAC value buffer.

**Parameters**: None  
**Returns**: Pointer to current 8-element DAC value array

**Description**: Returns the buffer currently being used for DAC output. Used by the state machine to read values for hardware output.

#### `uint16_t* getNextDacValues()`
Gets pointer to next DAC value buffer.

**Parameters**: None  
**Returns**: Pointer to next 8-element DAC value array

**Description**: Returns the buffer being prepared for the next output cycle. Used internally for double-buffered operation.

### Sample-and-Hold Configuration Methods

#### `void setSampleFrequency(float frequency)`
Sets the sample frequency for frequency-based sample-and-hold triggering.

**Parameters**:
- `frequency`: Sample frequency in Hz (range: 0.1Hz to 20Hz)

**Returns**: void

**Description**: Configures the automatic sampling rate for the sample-and-hold functionality. Frequency is automatically clamped to the valid range.

**Example**:
```cpp
dacControl.setSampleFrequency(2.5f);  // Set 2.5Hz sampling rate
```

#### `void enableFrequencyTrigger(bool enable)`
Enables or disables frequency-based sample-and-hold triggering.

**Parameters**:
- `enable`: true to enable frequency-based triggering, false to disable

**Returns**: void

**Description**: When enabled, the system automatically samples LFO values at the configured sample frequency.

**Example**:
```cpp
dacControl.enableFrequencyTrigger(true);   // Enable automatic sampling
dacControl.enableFrequencyTrigger(false);  // Disable automatic sampling
```

#### `void enableExternalTrigger(bool enable)`
Enables or disables external hardware trigger for sample-and-hold.

**Parameters**:
- `enable`: true to enable external triggering, false to disable

**Returns**: void

**Description**: When enabled, rising edge signals on the configured trigger pin will cause sampling. The interrupt is automatically attached/detached.

**Example**:
```cpp
dacControl.enableExternalTrigger(true);   // Enable hardware trigger
dacControl.enableExternalTrigger(false); // Disable hardware trigger
```

#### `void triggerSample()`
Manually triggers a sample-and-hold operation.

**Parameters**: None
**Returns**: void

**Description**: Forces an immediate sampling of all LFO values for the sample-and-hold source. Can be called regardless of trigger mode settings.

**Example**:
```cpp
dacControl.triggerSample();  // Force immediate sampling
```

#### `float getSampleFrequency() const`
Gets the current sample frequency setting.

**Parameters**: None
**Returns**: Current sample frequency in Hz

**Description**: Returns the currently configured sample frequency for frequency-based triggering.

**Example**:
```cpp
float freq = dacControl.getSampleFrequency();
Serial.print("Sample frequency: ");
Serial.println(freq);
```

### Enumerations

#### `enum DacDataSource`
Enumeration of available signal sources for DAC output routing.

**Values**:
- `DAC_SRC_MIDI_CC`: MIDI Control Change values (CC 20-27 → channels 0-7)
- `DAC_SRC_LFO`: Low Frequency Oscillator output (sine wave)
- `DAC_SRC_1`: Static source 1 (configurable fixed value)
- `DAC_SRC_2`: Static source 2 (configurable fixed value)
- `DAC_SRC_SAMPLE_HOLD`: Sample-and-hold of LFO values with frequency/external triggering
- `DAC_SRC_NONE`: No output (0V, used for disabled channels)

## MuxControl Class

The MuxControl class provides CD4051 multiplexer control interface.

### Constructor

#### `MuxControl()`
Constructor - no initialization required.

**Parameters**: None

### Methods

#### `void init()`
Initializes multiplexer control pins.

**Parameters**: None  
**Returns**: void

**Description**: Configures all control pins as outputs and sets the inhibit pin to HIGH (disabled) as a safe default state.

**Example**:
```cpp
MuxControl muxControl;
muxControl.init();
```

#### `void selectChannel(uint8_t channel)`
Selects active multiplexer channel.

**Parameters**:
- `channel`: Channel number (0-7)

**Returns**: void

**Description**: Sets the S0, S1, S2 pins to select which of the 8 channels (Y0-Y7) is connected to the common X pin.

**Example**:
```cpp
muxControl.selectChannel(3);  // Connect X to Y3
```

#### `static int inhPin()`
Gets inhibit pin number for external access.

**Parameters**: None  
**Returns**: Pin number for the multiplexer inhibit control

**Description**: Provides access to the inhibit pin number for use by other classes that need to control the enable/disable state.

**Example**:
```cpp
digitalWrite(MuxControl::inhPin(), LOW);  // Enable multiplexer
```

## MidiHandler Class

The MidiHandler class processes USB MIDI input and Control Change mapping.

### Constructor

#### `MidiHandler()`
Constructor - initializes CC value storage and singleton pointer.

**Parameters**: None

### Methods

#### `void init()`
Initializes MIDI input system.

**Parameters**: None  
**Returns**: void

**Description**: Sets up the USB MIDI input handler and registers the Control Change callback function.

**Example**:
```cpp
MidiHandler midiHandler;
midiHandler.init();
```

#### `void update()`
Updates MIDI input processing.

**Parameters**: None  
**Returns**: void

**Description**: Polls for incoming USB MIDI messages and processes them. This function is non-blocking and should be called regularly from the main loop.

**Example**:
```cpp
// In main loop
midiHandler.update();
```

#### `uint8_t getCC(uint8_t idx) const`
Gets current Control Change value for a DAC channel.

**Parameters**:
- `idx`: DAC channel index (0-7)

**Returns**: MIDI CC value (0-127), or 0 if idx is out of range

**Description**: Returns the most recently received MIDI Control Change value for the specified DAC channel.

**Example**:
```cpp
uint8_t ccValue = midiHandler.getCC(0);  // Get CC20 value
```

#### `void controlChange(byte channel, byte control, byte value)`
Processes incoming MIDI Control Change messages.

**Parameters**:
- `channel`: MIDI channel (1-16, currently ignored)
- `control`: Control Change number (20-27 are processed)
- `value`: Control Change value (0-127)

**Returns**: void

**Description**: Handles Control Change messages in the range CC 20-27, mapping them to DAC channels 0-7 respectively.

**Note**: This method is typically called automatically by the MIDI system.

## StateMachine Class

The StateMachine class provides precision timing control for DAC/MUX operations.

### Constructor

#### `StateMachine(MuxControl& mux, DacControl& dac)`
Constructor - establishes references to control subsystems.

**Parameters**:
- `mux`: Reference to MuxControl instance
- `dac`: Reference to DacControl instance

### Methods

#### `void init()`
Initializes state machine to default state.

**Parameters**: None  
**Returns**: void

**Description**: Sets the state machine to IDLE state starting with channel 0.

**Example**:
```cpp
StateMachine stateMachine(muxControl, dacControl);
stateMachine.init();
```

#### `void update()`
Main state machine update function.

**Parameters**: None  
**Returns**: void

**Description**: Advances the state machine through its timing sequence. This function should be called frequently from the main loop to maintain accurate timing.

**Example**:
```cpp
// In main loop
stateMachine.update();
```

#### `void refreshDacOutputs()`
Direct access to DAC output refresh cycle.

**Parameters**: None  
**Returns**: void

**Description**: Provides direct access to the core state machine functionality for advanced use cases.

### Enumerations

#### `enum MuxState`
State machine states for DAC/MUX output timing control.

**Values**:
- `IDLE`: Ready to select new channel and set DAC voltage
- `SETTLING`: Waiting for DAC output voltage to stabilize
- `SAMPLING`: Waiting for sample-and-hold capacitor to charge

## TestFramework Class

The TestFramework class provides real-time accuracy monitoring and validation.

### Constructor

#### `TestFramework(DacControl& dac)`
Constructor - establishes reference to DAC control system.

**Parameters**:
- `dac`: Reference to DacControl instance

### Methods

#### `void init()`
Initializes testing framework and serial communication.

**Parameters**: None  
**Returns**: void

**Description**: Sets up serial communication at 115200 baud, configures ADC resolution to 12 bits, and displays initialization messages.

**Example**:
```cpp
TestFramework testFramework(dacControl);
testFramework.init();
```

#### `void update()`
Main update function for continuous testing.

**Parameters**: None  
**Returns**: void

**Description**: Non-blocking function that should be called regularly from the main loop. Manages the testing cycle and periodic reporting.

**Example**:
```cpp
// In main loop
testFramework.update();
```

#### `void runTesting()`
Executes one testing cycle.

**Parameters**: None  
**Returns**: void

**Description**: Checks if enough time has elapsed since the last report and triggers a new test result printout if the report interval has passed.

#### `void printTestResults()`
Prints current test results to serial output.

**Parameters**: None  
**Returns**: void

**Description**: Outputs the current test measurements in a formatted table showing target DAC values, measured ADC values, and calculated errors.

## Global Functions and Variables

### Main Sketch Functions

#### `void setup()`
Arduino setup function - initializes all system components.

**Description**: Initializes all subsystems in the correct order and sets up default signal routing assignments.

#### `void loop()`
Arduino main loop - runs continuously to update all system components.

**Description**: Coordinates all system updates in a non-blocking manner including LFO updates, signal routing, state machine operation, testing, and MIDI processing.

### Global Instances

The main sketch creates global instances of all system components:

```cpp
extern DacControl dacControl;
extern MuxControl muxControl;
extern StateMachine stateMachine;
extern MidiHandler midiHandler;
extern TestFramework testFramework;
```

These instances can be accessed from other parts of the code when needed.

## Usage Patterns

### Basic System Setup

```cpp
#include "DacMux.h"

DacControl dacControl;
MuxControl muxControl;
StateMachine stateMachine(muxControl, dacControl);
MidiHandler midiHandler;
TestFramework testFramework(dacControl);

void setup() {
    // Initialize all components
    muxControl.init();
    dacControl.init();
    midiHandler.init();
    stateMachine.init();
    testFramework.init();
    dacControl.lfoEngineInit();
    
    // Configure routing
    dacControl.assignSourceToOutput(0, DacControl::DAC_SRC_MIDI_CC);
    dacControl.assignSourceToOutput(1, DacControl::DAC_SRC_LFO);
}

void loop() {
    // Update all systems
    dacControl.lfoEngineUpdate();
    
    // Update DAC values from sources
    for (uint8_t i = 0; i < 8; i++) {
        DacControl::DacDataSource src = dacControl.getOutputSource(i);
        uint16_t value = dacControl.getSourceFunc(src)(i);
        dacControl.setDacValue(i, value);
    }
    
    stateMachine.update();
    testFramework.update();
    midiHandler.update();
}
```

### Dynamic Routing Changes

```cpp
// Change channel 0 from MIDI to LFO
dacControl.assignSourceToOutput(0, DacControl::DAC_SRC_LFO);

// Disable channel 7
dacControl.assignSourceToOutput(7, DacControl::DAC_SRC_NONE);

// Set channel 3 to static source
dacControl.assignSourceToOutput(3, DacControl::DAC_SRC_1);
```

### Manual DAC Control

```cpp
// Set specific voltages
dacControl.setDacValue(0, 0);     // 0V
dacControl.setDacValue(1, 1024);  // ~0.8V
dacControl.setDacValue(2, 2048);  // ~1.6V
dacControl.setDacValue(3, 4095);  // ~3.3V
```

### MIDI Integration

```cpp
// Get current MIDI CC values
for (int i = 0; i < 8; i++) {
    uint8_t ccValue = midiHandler.getCC(i);
    uint16_t dacValue = dacControl.scaleMidiToDac(ccValue);
    dacControl.setDacValue(i, dacValue);
}
```

### Sample-and-Hold Usage

```cpp
// Configure sample-and-hold system
dacControl.setSampleFrequency(1.5f);         // 1.5Hz automatic sampling
dacControl.enableFrequencyTrigger(true);     // Enable frequency-based triggering
dacControl.enableExternalTrigger(true);      // Enable hardware trigger on pin 10

// Assign sample-and-hold to channels
dacControl.assignSourceToOutput(0, DacControl::DAC_SRC_SAMPLE_HOLD);
dacControl.assignSourceToOutput(1, DacControl::DAC_SRC_SAMPLE_HOLD);

// Manual triggering example
if (someCondition) {
    dacControl.triggerSample();  // Force sampling when needed
}

// Check current configuration
float currentFreq = dacControl.getSampleFrequency();
Serial.print("Current sample rate: ");
Serial.println(currentFreq);
```
