#ifndef STATEMACHINE_H
#define STATEMACHINE_H

#include <Arduino.h>

// Forward declarations to avoid circular includes
class MuxControl;
class DacControl;

/**
 * @class StateMachine
 * @brief Precision timing controller for DAC/Multiplexer output sequencing
 *
 * The StateMachine class coordinates the complex timing requirements of the
 * DAC multiplexer system. It manages the precise sequence of operations needed
 * to output analog values to multiple channels through a single DAC and
 * multiplexer with sample-and-hold circuits.
 *
 * Operation Sequence (per channel):
 * 1. IDLE: Select channel and set DAC voltage
 * 2. SETTLING: Wait for DAC output to stabilize (150μs default)
 * 3. SAMPLING: Enable MUX and wait for S&H capacitor to charge (66μs default)
 * 4. Return to IDLE for next channel
 *
 * The state machine cycles through all 8 channels continuously, providing
 * approximately 4.6kHz update rate per channel (37kHz total). Timing is
 * critical for accurate analog output - too short and the DAC/capacitors
 * won't settle properly, too long and the update rate becomes sluggish.
 *
 * Sample-and-Hold Integration:
 * The system works with external sample-and-hold circuits on each output
 * channel. The multiplexer inhibit pin controls when the S&H circuits
 * sample the DAC output. This allows all channels to maintain their
 * voltages independently while the DAC switches between channels.
 *
 * @note Timing constants are optimized for Teensy 3.6 and typical S&H circuits
 * @note The state machine is non-blocking and designed for real-time operation
 * @note All timing is based on microsecond precision using micros()
 *
 * Usage Example:
 * @code
 * MuxControl mux;
 * DacControl dac;
 * StateMachine sm(mux, dac);
 * sm.init();
 * // In main loop:
 * sm.update();
 * @endcode
 */
class StateMachine {
public:
    /**
     * @enum MuxState
     * @brief State machine states for DAC/MUX output timing control
     *
     * The state machine uses these states to coordinate the precise timing
     * required for accurate analog output through the multiplexed system.
     */
    enum MuxState {
        IDLE,      ///< Ready to select new channel and set DAC voltage
        SETTLING,  ///< Waiting for DAC output voltage to stabilize
        SAMPLING   ///< Waiting for sample-and-hold capacitor to charge
    };

    /**
     * @brief Constructor - establishes references to control subsystems
     *
     * @param mux Reference to MuxControl instance for channel selection
     * @param dac Reference to DacControl instance for voltage output
     */
    StateMachine(MuxControl& mux, DacControl& dac);

    /**
     * @brief Initialize state machine to default state
     *
     * Sets the state machine to IDLE state starting with channel 0.
     * Must be called before using update() function.
     */
    void init();

    /**
     * @brief Main state machine update function
     *
     * Advances the state machine through its timing sequence. This function
     * should be called frequently from the main loop to maintain accurate
     * timing. The function is non-blocking and will only advance states
     * when the appropriate time intervals have elapsed.
     *
     * State Transitions:
     * - IDLE → SETTLING: Immediately after channel/DAC setup
     * - SETTLING → SAMPLING: After SETTLE_DURATION_US microseconds
     * - SAMPLING → IDLE: After SAMPLE_DURATION_US microseconds (next channel)
     *
     * @note Must be called frequently (>100kHz) for accurate timing
     * @note Function execution time is minimal when waiting for timing
     */
    void update();

    /**
     * @brief Direct access to DAC output refresh cycle
     *
     * Provides direct access to the core state machine functionality for
     * advanced use cases or integration with test frameworks. Normally
     * the update() function should be used instead.
     *
     * @note This is the same function called by update()
     * @note Provided for compatibility and advanced integration scenarios
     */
    void refreshDacOutputs();

private:
    /**
     * @brief Reference to multiplexer control system
     *
     * Used for channel selection and inhibit control during the
     * state machine operation sequence.
     */
    MuxControl& muxControl;

    /**
     * @brief Reference to DAC control system
     *
     * Used to retrieve current DAC values and coordinate buffer
     * management during output operations.
     */
    DacControl& dacControl;

    /**
     * @brief Current state machine state
     *
     * Tracks the current position in the timing sequence (IDLE,
     * SETTLING, or SAMPLING).
     */
    MuxState currentState;

    /**
     * @brief Currently selected output channel
     *
     * Tracks which of the 8 channels (0-7) is currently being processed.
     * Automatically cycles through all channels continuously.
     */
    int currentChannel;

    /**
     * @brief Timestamp of last state change
     *
     * Used for precise timing control. Stores the microsecond timestamp
     * when the current state was entered, enabling accurate duration
     * measurements for timing-critical operations.
     */
    unsigned long stateChangeTime;

    /**
     * @brief DAC settling time in microseconds
     *
     * Time allowed for the DAC output to stabilize after changing to a
     * new voltage level. This accounts for DAC internal settling time
     * and any external filtering or buffering circuits.
     *
     * @note 150μs provides conservative settling for high accuracy
     * @note Reduce for faster update rates, increase for better accuracy
     */
    static constexpr unsigned long SETTLE_DURATION_US = 150;

    /**
     * @brief Sample-and-hold charging time in microseconds
     *
     * Time allowed for the sample-and-hold capacitor to charge to the
     * DAC output voltage. This depends on the S&H circuit design,
     * particularly the charging resistor and hold capacitor values.
     *
     * @note 66μs works well with typical S&H circuits
     * @note Increase if using larger hold capacitors or higher impedance
     */
    static constexpr unsigned long SAMPLE_DURATION_US = 66;
};

#endif // STATEMACHINE_H