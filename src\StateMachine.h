#ifndef STATEMACHINE_H
#define STATEMACHINE_H

#include <Arduino.h>

/**
 * @class StateMachine
 * @brief Encapsulates DAC/MUX output logic, timing, and channel sequencing.
 *
 * Usage:
 *   - Create a single instance of StateMachine at the appropriate scope.
 *   - Use public methods to initialize and update the state machine.
 *   - All state and timing constants are encapsulated.
 */
class StateMachine {
public:
    enum MuxState {
        IDLE,      // Ready to select a new channel
        SETTLING,  // Waiting for the DAC output to stabilize
        SAMPLING   // Waiting for the hold capacitor to charge
    };

    StateMachine(MuxControl& mux, DacControl& dac);

    // Initialization
    void init();

    // Main update loop
    void update();

    // (Optional) Expose refresh for test framework or advanced use
    void refreshDacOutputs();

private:
    // References to subsystem objects
    MuxControl& muxControl;
    DacControl& dacControl;

    // State machine state (was StateMachineState)
    MuxState currentState;
    int currentChannel;
    unsigned long stateChangeTime;

    // Timing constants
    static constexpr unsigned long SETTLE_DURATION_US = 150;
    static constexpr unsigned long SAMPLE_DURATION_US = 66;
};

#endif // STATEMACHINE_H