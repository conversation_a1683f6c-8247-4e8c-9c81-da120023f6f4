// MuxControl.cpp
// Implements MuxControl class: encapsulates CD4051 multiplexer pin control and channel selection

#include "MuxControl.h"

MuxControl::MuxControl() {
    // Nothing to initialize in constructor; pins are static constexpr
}

void MuxControl::init() {
    pinMode(MUX_S0_PIN, OUTPUT);
    pinMode(MUX_S1_PIN, OUTPUT);
    pinMode(MUX_S2_PIN, OUTPUT);
    pinMode(MUX_INH_PIN, OUTPUT);
    digitalWrite(MUX_INH_PIN, HIGH);
}

void MuxControl::selectChannel(uint8_t channel) {
    digitalWrite(MUX_S0_PIN, channel & 0x01);
    digitalWrite(MUX_S1_PIN, (channel >> 1) & 0x01);
    digitalWrite(MUX_S2_PIN, (channel >> 2) & 0x01);
}