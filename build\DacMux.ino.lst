
D:\CodePCB\Code\teensy\DacMux\build/DacMux.ino.elf:     file format elf32-littlearm


Disassembly of section .text:

00000000 <_VectorsFlash>:
}

void updateDacValues()
{
    // Map each MIDI CC value (0–127) to DAC output (0–4095) in real time
    for (int i = 0; i < 8; i++)
       0:	f8 ff 02 20 99 01 00 00 79 0d 00 00 35 0d 00 00     ... ....y...5...
    return (uint16_t)(((uint32_t)value * 4095) / 127);
      10:	35 0d 00 00 35 0d 00 00 35 0d 00 00 35 0d 00 00     5...5...5...5...
    for (int i = 0; i < 8; i++)
      20:	35 0d 00 00 35 0d 00 00 35 0d 00 00 79 0d 00 00     5...5...5...y...
    {
        dacValues[i] = scaleMidiToDac(midiCCValues[i]);
    }
}
      30:	79 0d 00 00 35 0d 00 00 49 26 00 00 4d 26 00 00     y...5...I&..M&..
{
	uint8_t sign=0;
	size_t count=0;

	if (isnan(number)) return print("nan");
    	if (isinf(number)) return print("inf");
      40:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
    	if (number > 4294967040.0f) return print("ovf");  // constant determined empirically
      50:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
    	if (number <-4294967040.0f) return print("ovf");  // constant determined empirically
      60:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
	
	// Handle negative numbers
	if (number < 0.0) {
      70:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
	uint8_t sign=0;
      80:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
		number = -number;
	}

	// Round correctly so that print(1.999, 2) prints as "2.00"
	double rounding = 0.5;
	for (uint8_t i=0; i<digits; ++i) {
      90:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
		rounding *= 0.1;
      a0:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
	}
	number += rounding;
      b0:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...

	// Extract the integer part of the number and print it
	unsigned long int_part = (unsigned long)number;
      c0:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
	double remainder = number - (double)int_part;
      d0:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
      e0:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
	count += printNumber(int_part, 10, sign);

	// Print the decimal point, but only if there are digits beyond
	if (digits > 0) {
		uint8_t n, buf[16], count=1;
		buf[0] = '.';
      f0:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...

		// Extract digits from the remainder one at a time
		if (digits > sizeof(buf) - 1) digits = sizeof(buf) - 1;

		while (digits-- > 0) {
     100:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
			remainder *= 10.0;
     110:	79 0d 00 00 bd 14 00 00 79 0d 00 00 79 0d 00 00     y.......y...y...
			n = (uint8_t)(remainder);
			buf[count++] = '0' + n;
     120:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
			remainder -= n; 
     130:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
			buf[count++] = '0' + n;
     140:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
		}
		count += write(buf, count);
	}
	return count;
}
     150:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
     160:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
		number = -number;
     170:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
	number += rounding;
     180:	79 0d 00 00 79 0d 00 00 79 0d 00 00 79 0d 00 00     y...y...y...y...
	unsigned long int_part = (unsigned long)number;
     190:	79 0d 00 00 79 0d 00 00                             y...y...

00000198 <ResetHandler>:
}
     198:	4b5a      	ldr	r3, [pc, #360]	; (304 <ResetHandler+0x16c>)
     19a:	f24c 5220 	movw	r2, #50464	; 0xc520
     19e:	b510      	push	{r4, lr}
     1a0:	81da      	strh	r2, [r3, #14]
     1a2:	f64d 1228 	movw	r2, #55592	; 0xd928
     1a6:	81da      	strh	r2, [r3, #14]
     1a8:	bf00      	nop
     1aa:	bf00      	nop
     1ac:	f000 fde8 	bl	d80 <startup_early_hook>
     1b0:	4b55      	ldr	r3, [pc, #340]	; (308 <ResetHandler+0x170>)
     1b2:	f04f 6230 	mov.w	r2, #184549376	; 0xb000000
     1b6:	631a      	str	r2, [r3, #48]	; 0x30
     1b8:	4a54      	ldr	r2, [pc, #336]	; (30c <ResetHandler+0x174>)
     1ba:	639a      	str	r2, [r3, #56]	; 0x38
     1bc:	4a54      	ldr	r2, [pc, #336]	; (310 <ResetHandler+0x178>)
     1be:	63da      	str	r2, [r3, #60]	; 0x3c
     1c0:	f04f 23e0 	mov.w	r3, #3758153728	; 0xe000e000
     1c4:	f44f 0270 	mov.w	r2, #15728640	; 0xf00000
	SIM_SCGC4 = SIM_SCGC4_USBOTG | 0xF0000030;
	SIM_SCGC5 = 0x00003F82;		// clocks active to all GPIO
	SIM_SCGC6 = SIM_SCGC6_ADC0 | SIM_SCGC6_TPM0 | SIM_SCGC6_TPM1 | SIM_SCGC6_TPM2 | SIM_SCGC6_FTFL;
#endif
#if defined(__MK64FX512__) || defined(__MK66FX1M0__)
	SCB_CPACR = 0x00F00000;
     1c8:	f8c3 2d88 	str.w	r2, [r3, #3464]	; 0xd88
#endif
#if defined(KINETISK) && !defined(__MK66FX1M0__)
	// If the RTC oscillator isn't enabled, get it started early.
	// But don't do this early on Teensy 3.6 - RTC_CR depends on 3.3V+VBAT
	// which may be ~0.4V "behind" 3.3V if the power ramps up slowly.
	if (!(RTC_CR & RTC_CR_OSCE)) {
     1cc:	4a51      	ldr	r2, [pc, #324]	; (314 <ResetHandler+0x17c>)
     1ce:	6913      	ldr	r3, [r2, #16]
     1d0:	f413 7380 	ands.w	r3, r3, #256	; 0x100
     1d4:	d103      	bne.n	1de <ResetHandler+0x46>
		RTC_SR = 0;
     1d6:	6153      	str	r3, [r2, #20]
		RTC_CR = RTC_CR_SC16P | RTC_CR_SC4P | RTC_CR_OSCE;
     1d8:	f44f 53a8 	mov.w	r3, #5376	; 0x1500
     1dc:	6113      	str	r3, [r2, #16]
	}
#endif
	// release I/O pins hold, if we woke up from VLLS mode
	if (PMC_REGSC & PMC_REGSC_ACKISO) PMC_REGSC |= PMC_REGSC_ACKISO;
     1de:	4b4e      	ldr	r3, [pc, #312]	; (318 <ResetHandler+0x180>)
     1e0:	789a      	ldrb	r2, [r3, #2]
     1e2:	0711      	lsls	r1, r2, #28
     1e4:	d503      	bpl.n	1ee <ResetHandler+0x56>
     1e6:	789a      	ldrb	r2, [r3, #2]
     1e8:	f042 0208 	orr.w	r2, r2, #8
     1ec:	709a      	strb	r2, [r3, #2]
    // since this is a write once register, make it visible to all F_CPU's
    // so we can into other sleep modes in the future at any speed
#if defined(__MK66FX1M0__)
	SMC_PMPROT = SMC_PMPROT_AHSRUN | SMC_PMPROT_AVLP | SMC_PMPROT_ALLS | SMC_PMPROT_AVLLS;
#else
	SMC_PMPROT = SMC_PMPROT_AVLP | SMC_PMPROT_ALLS | SMC_PMPROT_AVLLS;
     1ee:	4b4b      	ldr	r3, [pc, #300]	; (31c <ResetHandler+0x184>)
#endif
    
	// TODO: do this while the PLL is waiting to lock....
	while (dest < &_edata) *dest++ = *src++;
     1f0:	484b      	ldr	r0, [pc, #300]	; (320 <ResetHandler+0x188>)
	SMC_PMPROT = SMC_PMPROT_AVLP | SMC_PMPROT_ALLS | SMC_PMPROT_AVLLS;
     1f2:	222a      	movs	r2, #42	; 0x2a
     1f4:	701a      	strb	r2, [r3, #0]
	while (dest < &_edata) *dest++ = *src++;
     1f6:	4b4b      	ldr	r3, [pc, #300]	; (324 <ResetHandler+0x18c>)
     1f8:	1cda      	adds	r2, r3, #3
     1fa:	1a12      	subs	r2, r2, r0
     1fc:	1ec1      	subs	r1, r0, #3
     1fe:	f022 0203 	bic.w	r2, r2, #3
     202:	4299      	cmp	r1, r3
     204:	bf88      	it	hi
     206:	2200      	movhi	r2, #0
     208:	4947      	ldr	r1, [pc, #284]	; (328 <ResetHandler+0x190>)
     20a:	f000 fcbf 	bl	b8c <memcpy>
	dest = &_sbss;
	while (dest < &_ebss) *dest++ = 0;
     20e:	4b47      	ldr	r3, [pc, #284]	; (32c <ResetHandler+0x194>)
     210:	4847      	ldr	r0, [pc, #284]	; (330 <ResetHandler+0x198>)
     212:	1cda      	adds	r2, r3, #3
     214:	1a12      	subs	r2, r2, r0
     216:	1ec1      	subs	r1, r0, #3
     218:	f022 0203 	bic.w	r2, r2, #3
     21c:	4299      	cmp	r1, r3
     21e:	bf88      	it	hi
     220:	2200      	movhi	r2, #0
     222:	2100      	movs	r1, #0
     224:	f002 fa54 	bl	26d0 <memset>

	// default all interrupts to medium priority level
	for (i=0; i < NVIC_NUM_INTERRUPTS + 16; i++) _VectorsRam[i] = _VectorsFlash[i];
     228:	4942      	ldr	r1, [pc, #264]	; (334 <ResetHandler+0x19c>)
     22a:	4843      	ldr	r0, [pc, #268]	; (338 <ResetHandler+0x1a0>)
     22c:	f44f 72cc 	mov.w	r2, #408	; 0x198
     230:	f000 fcac 	bl	b8c <memcpy>
     234:	4b41      	ldr	r3, [pc, #260]	; (33c <ResetHandler+0x1a4>)
	for (i=0; i < NVIC_NUM_INTERRUPTS; i++) NVIC_SET_PRIORITY(i, 128);
     236:	4a42      	ldr	r2, [pc, #264]	; (340 <ResetHandler+0x1a8>)
     238:	2180      	movs	r1, #128	; 0x80
     23a:	f803 1b01 	strb.w	r1, [r3], #1
     23e:	4293      	cmp	r3, r2
     240:	d1fb      	bne.n	23a <ResetHandler+0xa2>
	SCB_VTOR = (uint32_t)_VectorsRam;	// use vector table in RAM
     242:	f04f 23e0 	mov.w	r3, #3758153728	; 0xe000e000
     246:	4a3c      	ldr	r2, [pc, #240]	; (338 <ResetHandler+0x1a0>)
     248:	f8c3 2d08 	str.w	r2, [r3, #3336]	; 0xd08
	//  C6[PLLS] bit is written to 0
	//  C2[LP] bit is written to 1
#else
    #if defined(KINETISK)
    // enable capacitors for crystal
    OSC0_CR = OSC_SC8P | OSC_SC2P | OSC_ERCLKEN;
     24c:	4b3d      	ldr	r3, [pc, #244]	; (344 <ResetHandler+0x1ac>)
     24e:	228a      	movs	r2, #138	; 0x8a
     250:	701a      	strb	r2, [r3, #0]
    #elif defined(KINETISL)
    // enable capacitors for crystal
    OSC0_CR = OSC_SC8P | OSC_SC2P | OSC_ERCLKEN;
    #endif
	// enable osc, 8-32 MHz range, low power mode
	MCG_C2 = MCG_C2_RANGE0(2) | MCG_C2_EREFS;
     252:	f5a3 5380 	sub.w	r3, r3, #4096	; 0x1000
     256:	2224      	movs	r2, #36	; 0x24
     258:	705a      	strb	r2, [r3, #1]
	// switch to crystal as clock source, FLL input = 16 MHz / 512
	MCG_C1 =  MCG_C1_CLKS(2) | MCG_C1_FRDIV(4);
     25a:	22a0      	movs	r2, #160	; 0xa0
     25c:	701a      	strb	r2, [r3, #0]
	// wait for crystal oscillator to begin
	while ((MCG_S & MCG_S_OSCINIT0) == 0) ;
     25e:	799a      	ldrb	r2, [r3, #6]
     260:	0792      	lsls	r2, r2, #30
     262:	d5fc      	bpl.n	25e <ResetHandler+0xc6>
	// wait for FLL to use oscillator
	while ((MCG_S & MCG_S_IREFST) != 0) ;
     264:	799a      	ldrb	r2, [r3, #6]
     266:	06d4      	lsls	r4, r2, #27
     268:	d4fc      	bmi.n	264 <ResetHandler+0xcc>
	// wait for MCGOUT to use oscillator
	while ((MCG_S & MCG_S_CLKST_MASK) != MCG_S_CLKST(2)) ;
     26a:	4b37      	ldr	r3, [pc, #220]	; (348 <ResetHandler+0x1b0>)
     26c:	799a      	ldrb	r2, [r3, #6]
     26e:	f002 020c 	and.w	r2, r2, #12
     272:	2a08      	cmp	r2, #8
     274:	d1fa      	bne.n	26c <ResetHandler+0xd4>
    #endif
   #else
    #if F_CPU == 72000000
	MCG_C5 = MCG_C5_PRDIV0(5);		 // config PLL input for 16 MHz Crystal / 6 = 2.667 Hz
    #else
	MCG_C5 = MCG_C5_PRDIV0(3);		 // config PLL input for 16 MHz Crystal / 4 = 4 MHz
     276:	2203      	movs	r2, #3
     278:	711a      	strb	r2, [r3, #4]
    #if F_CPU == 168000000
	MCG_C6 = MCG_C6_PLLS | MCG_C6_VDIV0(18); // config PLL for 168 MHz output
    #elif F_CPU == 144000000
	MCG_C6 = MCG_C6_PLLS | MCG_C6_VDIV0(12); // config PLL for 144 MHz output
    #elif F_CPU == 120000000
	MCG_C6 = MCG_C6_PLLS | MCG_C6_VDIV0(6); // config PLL for 120 MHz output
     27a:	2246      	movs	r2, #70	; 0x46
     27c:	715a      	strb	r2, [r3, #5]
    #error "This clock speed isn't supported..."
    #endif
   #endif

	// wait for PLL to start using xtal as its input
	while (!(MCG_S & MCG_S_PLLST)) ;
     27e:	799a      	ldrb	r2, [r3, #6]
     280:	0690      	lsls	r0, r2, #26
     282:	d5fc      	bpl.n	27e <ResetHandler+0xe6>
	// wait for PLL to lock
	while (!(MCG_S & MCG_S_LOCK0)) ;
     284:	4a30      	ldr	r2, [pc, #192]	; (348 <ResetHandler+0x1b0>)
     286:	7993      	ldrb	r3, [r2, #6]
     288:	0659      	lsls	r1, r3, #25
     28a:	d5fc      	bpl.n	286 <ResetHandler+0xee>
	#endif
	SIM_CLKDIV2 = SIM_CLKDIV2_USBDIV(2);
#elif F_CPU == 120000000
	// config divisors: 120 MHz core, 60 MHz bus, 24 MHz flash, USB = 128 * 2 / 5
	#if F_BUS == 60000000
	SIM_CLKDIV1 = SIM_CLKDIV1_OUTDIV1(0) | SIM_CLKDIV1_OUTDIV2(1) | SIM_CLKDIV1_OUTDIV4(4);
     28c:	4b1e      	ldr	r3, [pc, #120]	; (308 <ResetHandler+0x170>)
     28e:	f04f 7182 	mov.w	r1, #17039360	; 0x1040000
     292:	6459      	str	r1, [r3, #68]	; 0x44
	#elif F_BUS == 120000000
	SIM_CLKDIV1 = SIM_CLKDIV1_OUTDIV1(0) | SIM_CLKDIV1_OUTDIV2(0) | SIM_CLKDIV1_OUTDIV4(4);
	#else
	#error "This F_CPU & F_BUS combination is not supported"
	#endif
	SIM_CLKDIV2 = SIM_CLKDIV2_USBDIV(4) | SIM_CLKDIV2_USBFRAC;
     294:	2109      	movs	r1, #9
     296:	6499      	str	r1, [r3, #72]	; 0x48
#error "Error, F_CPU must be 256, 240, 216, 192, 180, 168, 144, 120, 96, 72, 48, 24, 16, 8, 4, or 2 MHz"
#endif

#if F_CPU > 16000000
	// switch to PLL as clock source, FLL input = 16 MHz / 512
	MCG_C1 = MCG_C1_CLKS(0) | MCG_C1_FRDIV(4);
     298:	2120      	movs	r1, #32
     29a:	7011      	strb	r1, [r2, #0]
	// wait for PLL clock to be used
	while ((MCG_S & MCG_S_CLKST_MASK) != MCG_S_CLKST(3)) ;
     29c:	7991      	ldrb	r1, [r2, #6]
     29e:	f001 010c 	and.w	r1, r1, #12
     2a2:	290c      	cmp	r1, #12
     2a4:	d1fa      	bne.n	29c <ResetHandler+0x104>
	#if F_CPU == 256000000 || F_CPU == 216000000 || F_CPU == 180000000
	// USB uses IRC48
	SIM_SOPT2 = SIM_SOPT2_USBSRC | SIM_SOPT2_IRC48SEL | SIM_SOPT2_TRACECLKSEL | SIM_SOPT2_CLKOUTSEL(6);
	#else
	// USB uses PLL clock
	SIM_SOPT2 = SIM_SOPT2_USBSRC | SIM_SOPT2_PLLFLLSEL | SIM_SOPT2_TRACECLKSEL | SIM_SOPT2_CLKOUTSEL(6);
     2a6:	4a29      	ldr	r2, [pc, #164]	; (34c <ResetHandler+0x1b4>)
     2a8:	605a      	str	r2, [r3, #4]
		RTC_CR = RTC_CR_SC16P | RTC_CR_SC4P | RTC_CR_OSCE;
	}
#endif

	// initialize the SysTick counter
	SYST_RVR = (F_CPU / 1000) - 1;
     2aa:	f04f 23e0 	mov.w	r3, #3758153728	; 0xe000e000
     2ae:	4a28      	ldr	r2, [pc, #160]	; (350 <ResetHandler+0x1b8>)
     2b0:	615a      	str	r2, [r3, #20]
	SYST_CVR = 0;
     2b2:	2200      	movs	r2, #0
     2b4:	619a      	str	r2, [r3, #24]
	SYST_CSR = SYST_CSR_CLKSOURCE | SYST_CSR_TICKINT | SYST_CSR_ENABLE;
     2b6:	2207      	movs	r2, #7
     2b8:	611a      	str	r2, [r3, #16]
	SCB_SHPR3 = 0x20200000;  // Systick = priority 32
     2ba:	4a26      	ldr	r2, [pc, #152]	; (354 <ResetHandler+0x1bc>)
     2bc:	f8c3 2d20 	str.w	r2, [r3, #3360]	; 0xd20

	//init_pins();
	__enable_irq();
     2c0:	b662      	cpsie	i

	_init_Teensyduino_internal_();
     2c2:	f000 fdc5 	bl	e50 <_init_Teensyduino_internal_>

#if defined(KINETISK)
	// RTC initialization
	if (RTC_SR & RTC_SR_TIF) {
     2c6:	4b13      	ldr	r3, [pc, #76]	; (314 <ResetHandler+0x17c>)
     2c8:	695b      	ldr	r3, [r3, #20]
     2ca:	07da      	lsls	r2, r3, #31
     2cc:	d505      	bpl.n	2da <ResetHandler+0x142>
		// compiled-in time will be stale.  Write a special
		// flag into the VBAT register file indicating the
		// RTC is set with known-stale time and should be
		// updated when fresh time is known.
		#if ARDUINO >= 10600
		rtc_set((uint32_t)&__rtc_localtime);
     2ce:	4822      	ldr	r0, [pc, #136]	; (358 <ResetHandler+0x1c0>)
     2d0:	f000 fdb2 	bl	e38 <rtc_set>
		#else
		rtc_set(TIME_T);
		#endif
		*(uint32_t *)0x4003E01C = 0x5A94C3A5;
     2d4:	4b21      	ldr	r3, [pc, #132]	; (35c <ResetHandler+0x1c4>)
     2d6:	4a22      	ldr	r2, [pc, #136]	; (360 <ResetHandler+0x1c8>)
     2d8:	61da      	str	r2, [r3, #28]
	}
	if ((RCM_SRS0 & RCM_SRS0_PIN) && (*(uint32_t *)0x4003E01C == 0x5A94C3A5)) {
     2da:	4b22      	ldr	r3, [pc, #136]	; (364 <ResetHandler+0x1cc>)
     2dc:	781b      	ldrb	r3, [r3, #0]
     2de:	065b      	lsls	r3, r3, #25
     2e0:	d509      	bpl.n	2f6 <ResetHandler+0x15e>
     2e2:	4c1e      	ldr	r4, [pc, #120]	; (35c <ResetHandler+0x1c4>)
     2e4:	4b1e      	ldr	r3, [pc, #120]	; (360 <ResetHandler+0x1c8>)
     2e6:	69e2      	ldr	r2, [r4, #28]
     2e8:	429a      	cmp	r2, r3
     2ea:	d104      	bne.n	2f6 <ResetHandler+0x15e>
		// Our compiled-in time will be very fresh, so set
		// the RTC with this, and clear the VBAT resister file
		// data so we don't mess with the time after it's been
		// set well.
		#if ARDUINO >= 10600
		rtc_set((uint32_t)&__rtc_localtime);
     2ec:	481a      	ldr	r0, [pc, #104]	; (358 <ResetHandler+0x1c0>)
     2ee:	f000 fda3 	bl	e38 <rtc_set>
		#else
		rtc_set(TIME_T);
		#endif
		*(uint32_t *)0x4003E01C = 0;
     2f2:	2300      	movs	r3, #0
     2f4:	61e3      	str	r3, [r4, #28]
	}
#endif

	startup_late_hook();
     2f6:	f000 fd49 	bl	d8c <startup_late_hook>
	__libc_init_array();
     2fa:	f002 fa3b 	bl	2774 <__libc_init_array>

	main();
     2fe:	f000 fc3d 	bl	b7c <main>
	
	while (1) ;
     302:	e7fe      	b.n	302 <ResetHandler+0x16a>
     304:	40052000 	.word	0x40052000
     308:	40048000 	.word	0x40048000
     30c:	00043f82 	.word	0x00043f82
     310:	2b000001 	.word	0x2b000001
     314:	4003d000 	.word	0x4003d000
     318:	4007d000 	.word	0x4007d000
     31c:	4007e000 	.word	0x4007e000
     320:	1fff0c08 	.word	0x1fff0c08
     324:	1fff0d30 	.word	0x1fff0d30
     328:	00002c30 	.word	0x00002c30
     32c:	1fff11f8 	.word	0x1fff11f8
     330:	1fff0d30 	.word	0x1fff0d30
     334:	00000000 	.word	0x00000000
     338:	1fff0200 	.word	0x1fff0200
     33c:	e000e400 	.word	0xe000e400
     340:	e000e456 	.word	0xe000e456
     344:	40065000 	.word	0x40065000
     348:	40064000 	.word	0x40064000
     34c:	000510c0 	.word	0x000510c0
     350:	0001d4bf 	.word	0x0001d4bf
     354:	20200000 	.word	0x20200000
     358:	6869e23a 	.word	0x6869e23a
     35c:	4003e000 	.word	0x4003e000
     360:	5a94c3a5 	.word	0x5a94c3a5
     364:	4007f000 	.word	0x4007f000
     368:	ffffffff 	.word	0xffffffff
     36c:	ffffffff 	.word	0xffffffff
     370:	ffffffff 	.word	0xffffffff
     374:	ffffffff 	.word	0xffffffff
     378:	ffffffff 	.word	0xffffffff
     37c:	ffffffff 	.word	0xffffffff
     380:	ffffffff 	.word	0xffffffff
     384:	ffffffff 	.word	0xffffffff
     388:	ffffffff 	.word	0xffffffff
     38c:	ffffffff 	.word	0xffffffff
     390:	ffffffff 	.word	0xffffffff
     394:	ffffffff 	.word	0xffffffff
     398:	ffffffff 	.word	0xffffffff
     39c:	ffffffff 	.word	0xffffffff
     3a0:	ffffffff 	.word	0xffffffff
     3a4:	ffffffff 	.word	0xffffffff
     3a8:	ffffffff 	.word	0xffffffff
     3ac:	ffffffff 	.word	0xffffffff
     3b0:	ffffffff 	.word	0xffffffff
     3b4:	ffffffff 	.word	0xffffffff
     3b8:	ffffffff 	.word	0xffffffff
     3bc:	ffffffff 	.word	0xffffffff
     3c0:	ffffffff 	.word	0xffffffff
     3c4:	ffffffff 	.word	0xffffffff
     3c8:	ffffffff 	.word	0xffffffff
     3cc:	ffffffff 	.word	0xffffffff
     3d0:	ffffffff 	.word	0xffffffff
     3d4:	ffffffff 	.word	0xffffffff
     3d8:	ffffffff 	.word	0xffffffff
     3dc:	ffffffff 	.word	0xffffffff
     3e0:	ffffffff 	.word	0xffffffff
     3e4:	ffffffff 	.word	0xffffffff
     3e8:	ffffffff 	.word	0xffffffff
     3ec:	ffffffff 	.word	0xffffffff
     3f0:	ffffffff 	.word	0xffffffff
     3f4:	ffffffff 	.word	0xffffffff
     3f8:	ffffffff 	.word	0xffffffff
     3fc:	ffffffff 	.word	0xffffffff

00000400 <flashconfigbytes>:
     400:	ffffffff ffffffff ffffffff fffff9de     ................

00000410 <register_tm_clones>:
     410:	4805      	ldr	r0, [pc, #20]	; (428 <register_tm_clones+0x18>)
     412:	4b06      	ldr	r3, [pc, #24]	; (42c <register_tm_clones+0x1c>)
     414:	1a1b      	subs	r3, r3, r0
     416:	0fd9      	lsrs	r1, r3, #31
     418:	eb01 01a3 	add.w	r1, r1, r3, asr #2
     41c:	1049      	asrs	r1, r1, #1
     41e:	d002      	beq.n	426 <register_tm_clones+0x16>
     420:	4b03      	ldr	r3, [pc, #12]	; (430 <register_tm_clones+0x20>)
     422:	b103      	cbz	r3, 426 <register_tm_clones+0x16>
     424:	4718      	bx	r3
     426:	4770      	bx	lr
     428:	1fff0d30 	.word	0x1fff0d30
     42c:	1fff0d30 	.word	0x1fff0d30
     430:	00000000 	.word	0x00000000

00000434 <frame_dummy>:
     434:	b508      	push	{r3, lr}
     436:	4b05      	ldr	r3, [pc, #20]	; (44c <frame_dummy+0x18>)
     438:	b11b      	cbz	r3, 442 <frame_dummy+0xe>
     43a:	4905      	ldr	r1, [pc, #20]	; (450 <frame_dummy+0x1c>)
     43c:	4805      	ldr	r0, [pc, #20]	; (454 <frame_dummy+0x20>)
     43e:	f3af 8000 	nop.w
     442:	e8bd 4008 	ldmia.w	sp!, {r3, lr}
     446:	f7ff bfe3 	b.w	410 <register_tm_clones>
     44a:	bf00      	nop
     44c:	00000000 	.word	0x00000000
     450:	1fff0d30 	.word	0x1fff0d30
     454:	00002c30 	.word	0x00002c30

00000458 <myControlChange(unsigned char, unsigned char, unsigned char)>:
    if (control >= 20 && control <= 27) {
     458:	3914      	subs	r1, #20
     45a:	b2c9      	uxtb	r1, r1
     45c:	2907      	cmp	r1, #7
        midiCCValues[idx] = value;
     45e:	bf9c      	itt	ls
     460:	4b01      	ldrls	r3, [pc, #4]	; (468 <myControlChange(unsigned char, unsigned char, unsigned char)+0x10>)
     462:	545a      	strbls	r2, [r3, r1]
}
     464:	4770      	bx	lr
     466:	bf00      	nop
     468:	1fff0d74 	.word	0x1fff0d74

0000046c <setup>:
{
     46c:	e92d 43f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, lr}
    pinMode(MUX_S0_PIN, OUTPUT);
     470:	2101      	movs	r1, #1
{
     472:	b083      	sub	sp, #12
    pinMode(MUX_S0_PIN, OUTPUT);
     474:	2006      	movs	r0, #6
     476:	f000 ff09 	bl	128c <pinMode>
    pinMode(MUX_S1_PIN, OUTPUT);
     47a:	2101      	movs	r1, #1
     47c:	2007      	movs	r0, #7
     47e:	f000 ff05 	bl	128c <pinMode>
    pinMode(MUX_S2_PIN, OUTPUT);
     482:	2101      	movs	r1, #1
     484:	2008      	movs	r0, #8
     486:	f000 ff01 	bl	128c <pinMode>
    pinMode(MUX_INH_PIN, OUTPUT);
     48a:	2101      	movs	r1, #1
     48c:	2005      	movs	r0, #5
     48e:	f000 fefd 	bl	128c <pinMode>
    digitalWrite(MUX_INH_PIN, HIGH);
     492:	2101      	movs	r1, #1
     494:	2005      	movs	r0, #5
     496:	f000 fecf 	bl	1238 <digitalWrite>
class usb_serial_class : public Stream
{
public:
	constexpr usb_serial_class() {}
        void begin(long) {
		uint32_t millis_begin = systick_millis_count;
     49a:	4d2e      	ldr	r5, [pc, #184]	; (554 <setup+0xe8>)
     49c:	4c2e      	ldr	r4, [pc, #184]	; (558 <setup+0xec>)
        uint8_t numbits(void) { return usb_cdc_line_coding[1] >> 16; }
        uint8_t dtr(void) { return (usb_cdc_line_rtsdtr & USB_SERIAL_DTR) ? 1 : 0; }
        uint8_t rts(void) { return (usb_cdc_line_rtsdtr & USB_SERIAL_RTS) ? 1 : 0; }
        operator bool() {
		yield();
		return usb_configuration && (usb_cdc_line_rtsdtr & USB_SERIAL_DTR) &&
     49e:	4f2f      	ldr	r7, [pc, #188]	; (55c <setup+0xf0>)
		((uint32_t)(systick_millis_count - usb_cdc_line_rtsdtr_millis) >= 15);
     4a0:	f8df 90c8 	ldr.w	r9, [pc, #200]	; 56c <setup+0x100>

void pinMode(uint8_t pin, uint8_t mode);
void init_pins(void);
void analogWrite(uint8_t pin, int val);
uint32_t analogWriteRes(uint32_t bits);
static inline uint32_t analogWriteResolution(uint32_t bits) { return analogWriteRes(bits); }
     4a4:	200c      	movs	r0, #12
     4a6:	f000 feb9 	bl	121c <analogWriteRes>
        dacValues[i] = 0;
     4aa:	4b2d      	ldr	r3, [pc, #180]	; (560 <setup+0xf4>)
		// type: 0xA0  AfterTouchPoly
                usb_midi_handleVelocityChange = fptr;
        }
        void setHandleControlChange(void (*fptr)(uint8_t channel, uint8_t control, uint8_t value)) {
		// type: 0xB0  ControlChange
                usb_midi_handleControlChange = fptr;
     4ac:	492d      	ldr	r1, [pc, #180]	; (564 <setup+0xf8>)
     4ae:	482e      	ldr	r0, [pc, #184]	; (568 <setup+0xfc>)
		uint32_t millis_begin = systick_millis_count;
     4b0:	682e      	ldr	r6, [r5, #0]
     4b2:	6008      	str	r0, [r1, #0]
     4b4:	2200      	movs	r2, #0
     4b6:	601a      	str	r2, [r3, #0]
     4b8:	605a      	str	r2, [r3, #4]
				if (elapsed > 750) break;
     4ba:	f240 28ee 	movw	r8, #750	; 0x2ee
		yield();
     4be:	f002 f829 	bl	2514 <yield>
		return usb_configuration && (usb_cdc_line_rtsdtr & USB_SERIAL_DTR) &&
     4c2:	7823      	ldrb	r3, [r4, #0]
     4c4:	2b00      	cmp	r3, #0
     4c6:	d031      	beq.n	52c <setup+0xc0>
     4c8:	783b      	ldrb	r3, [r7, #0]
     4ca:	07da      	lsls	r2, r3, #31
     4cc:	d52e      	bpl.n	52c <setup+0xc0>
		((uint32_t)(systick_millis_count - usb_cdc_line_rtsdtr_millis) >= 15);
     4ce:	682b      	ldr	r3, [r5, #0]
     4d0:	f8d9 2000 	ldr.w	r2, [r9]
     4d4:	1a9b      	subs	r3, r3, r2
		return usb_configuration && (usb_cdc_line_rtsdtr & USB_SERIAL_DTR) &&
     4d6:	2b0e      	cmp	r3, #14
     4d8:	d928      	bls.n	52c <setup+0xc0>
     4da:	4e20      	ldr	r6, [pc, #128]	; (55c <setup+0xf0>)
		((uint32_t)(systick_millis_count - usb_cdc_line_rtsdtr_millis) >= 15);
     4dc:	4f23      	ldr	r7, [pc, #140]	; (56c <setup+0x100>)
		yield();
     4de:	f002 f819 	bl	2514 <yield>
		return usb_configuration && (usb_cdc_line_rtsdtr & USB_SERIAL_DTR) &&
     4e2:	7823      	ldrb	r3, [r4, #0]
     4e4:	b363      	cbz	r3, 540 <setup+0xd4>
     4e6:	7833      	ldrb	r3, [r6, #0]
     4e8:	07db      	lsls	r3, r3, #31
     4ea:	d529      	bpl.n	540 <setup+0xd4>
		((uint32_t)(systick_millis_count - usb_cdc_line_rtsdtr_millis) >= 15);
     4ec:	682b      	ldr	r3, [r5, #0]
     4ee:	683a      	ldr	r2, [r7, #0]
     4f0:	1a9b      	subs	r3, r3, r2
		return usb_configuration && (usb_cdc_line_rtsdtr & USB_SERIAL_DTR) &&
     4f2:	2b0e      	cmp	r3, #14
     4f4:	d924      	bls.n	540 <setup+0xd4>
void _init_Teensyduino_internal_(void);

int analogRead(uint8_t pin);
void analogReference(uint8_t type);
void analogReadRes(unsigned int bits);
static inline void analogReadResolution(unsigned int bits) { analogReadRes(bits); }
     4f6:	200c      	movs	r0, #12
     4f8:	f000 fa5c 	bl	9b4 <analogReadRes>
        virtual size_t write(const uint8_t *buffer, size_t size) { return usb_serial_write(buffer, size); }
     4fc:	2120      	movs	r1, #32
     4fe:	481c      	ldr	r0, [pc, #112]	; (570 <setup+0x104>)
     500:	f001 ff02 	bl	2308 <usb_serial_write>
	size_t print(double n, int digits = 2)		{ return printFloat(n, digits); }
	size_t print(const Printable &obj)		{ return obj.printTo(*this); }
	size_t println(void);
	size_t println(const String &s)			{ return print(s) + println(); }
	size_t println(char c)				{ return print(c) + println(); }
	size_t println(const char s[])			{ return print(s) + println(); }
     504:	481b      	ldr	r0, [pc, #108]	; (574 <setup+0x108>)
     506:	f000 f987 	bl	818 <Print::println()>
     50a:	2121      	movs	r1, #33	; 0x21
     50c:	481a      	ldr	r0, [pc, #104]	; (578 <setup+0x10c>)
     50e:	f001 fefb 	bl	2308 <usb_serial_write>
     512:	4818      	ldr	r0, [pc, #96]	; (574 <setup+0x108>)
     514:	f000 f980 	bl	818 <Print::println()>
     518:	2121      	movs	r1, #33	; 0x21
     51a:	4818      	ldr	r0, [pc, #96]	; (57c <setup+0x110>)
     51c:	f001 fef4 	bl	2308 <usb_serial_write>
     520:	4814      	ldr	r0, [pc, #80]	; (574 <setup+0x108>)
}
     522:	b003      	add	sp, #12
     524:	e8bd 43f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, lr}
     528:	f000 b976 	b.w	818 <Print::println()>
			uint32_t elapsed = systick_millis_count - millis_begin;
     52c:	682b      	ldr	r3, [r5, #0]
			if (usb_configuration) {
     52e:	7822      	ldrb	r2, [r4, #0]
			uint32_t elapsed = systick_millis_count - millis_begin;
     530:	1b9b      	subs	r3, r3, r6
			if (usb_configuration) {
     532:	b162      	cbz	r2, 54e <setup+0xe2>
				if (elapsed > 2000) break;
     534:	f5b3 6ffa 	cmp.w	r3, #2000	; 0x7d0
     538:	d8cf      	bhi.n	4da <setup+0x6e>
			yield();
     53a:	f001 ffeb 	bl	2514 <yield>
     53e:	e7be      	b.n	4be <setup+0x52>
	// do not remove this "redundant" code without
	// carefully verifying the case mentioned here:
	//
	// https://forum.pjrc.com/threads/17469-millis%28%29-on-teensy-3?p=104924&viewfull=1#post104924
	//
	volatile uint32_t ret = systick_millis_count; // single aligned 32 bit is atomic
     540:	682b      	ldr	r3, [r5, #0]
     542:	9301      	str	r3, [sp, #4]
	return ret;
     544:	9b01      	ldr	r3, [sp, #4]
        while (!Serial && millis() < 2000)
     546:	f5b3 6ffa 	cmp.w	r3, #2000	; 0x7d0
     54a:	d3c8      	bcc.n	4de <setup+0x72>
     54c:	e7d3      	b.n	4f6 <setup+0x8a>
				if (elapsed > 750) break;
     54e:	4543      	cmp	r3, r8
     550:	d9f3      	bls.n	53a <setup+0xce>
     552:	e7c2      	b.n	4da <setup+0x6e>
     554:	1fff0d88 	.word	0x1fff0d88
     558:	1fff0e79 	.word	0x1fff0e79
     55c:	1fff1024 	.word	0x1fff1024
     560:	1fff0d50 	.word	0x1fff0d50
     564:	1fff0ea4 	.word	0x1fff0ea4
     568:	00000459 	.word	0x00000459
     56c:	1fff1028 	.word	0x1fff1028
     570:	00002894 	.word	0x00002894
     574:	1fff0c0c 	.word	0x1fff0c0c
     578:	000028b8 	.word	0x000028b8
     57c:	000028dc 	.word	0x000028dc

00000580 <refreshDacOutputs()>:

void refreshDacOutputs()
{ 
     580:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
    unsigned long currentTime = micros();

    switch (currentState)
     582:	4d30      	ldr	r5, [pc, #192]	; (644 <refreshDacOutputs()+0xc4>)
    unsigned long currentTime = micros();
     584:	f000 febe 	bl	1304 <micros>
    switch (currentState)
     588:	782b      	ldrb	r3, [r5, #0]
     58a:	2b01      	cmp	r3, #1
    unsigned long currentTime = micros();
     58c:	4604      	mov	r4, r0
    switch (currentState)
     58e:	d039      	beq.n	604 <refreshDacOutputs()+0x84>
     590:	2b02      	cmp	r3, #2
     592:	d01e      	beq.n	5d2 <refreshDacOutputs()+0x52>
     594:	b103      	cbz	r3, 598 <refreshDacOutputs()+0x18>
                // 10. Transition back to IDLE for the next cycle
                currentState = IDLE;
            }
            break;
    }
}
     596:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
            selectMuxChannel(currentChannel);
     598:	4f2b      	ldr	r7, [pc, #172]	; (648 <refreshDacOutputs()+0xc8>)
     59a:	783e      	ldrb	r6, [r7, #0]
// Sets the S0, S1, and S2 pins to select the desired channel (0-7)
void selectMuxChannel(byte channel)
{
    // The select pins are a 3-bit binary representation of the channel number.
    // S0 is the LSB (Least Significant Bit).
    digitalWrite(MUX_S0_PIN, (channel & 1));      // Bit 0
     59c:	2006      	movs	r0, #6
     59e:	f006 0101 	and.w	r1, r6, #1
     5a2:	f000 fe49 	bl	1238 <digitalWrite>
    digitalWrite(MUX_S1_PIN, (channel >> 1) & 1); // Bit 1
     5a6:	f3c6 0140 	ubfx	r1, r6, #1, #1
     5aa:	2007      	movs	r0, #7
     5ac:	f000 fe44 	bl	1238 <digitalWrite>
    digitalWrite(MUX_S2_PIN, (channel >> 2) & 1); // Bit 2
     5b0:	f3c6 0180 	ubfx	r1, r6, #2, #1
     5b4:	2008      	movs	r0, #8
     5b6:	f000 fe3f 	bl	1238 <digitalWrite>
            analogWrite(A21, dacValues[currentChannel]);
     5ba:	683a      	ldr	r2, [r7, #0]
     5bc:	4b23      	ldr	r3, [pc, #140]	; (64c <refreshDacOutputs()+0xcc>)
     5be:	2042      	movs	r0, #66	; 0x42
     5c0:	f833 1012 	ldrh.w	r1, [r3, r2, lsl #1]
     5c4:	f000 fca6 	bl	f14 <analogWrite>
            stateChangeTime = currentTime;
     5c8:	4a21      	ldr	r2, [pc, #132]	; (650 <refreshDacOutputs()+0xd0>)
            currentState = SETTLING;
     5ca:	2301      	movs	r3, #1
            stateChangeTime = currentTime;
     5cc:	6014      	str	r4, [r2, #0]
            currentState = SETTLING;
     5ce:	702b      	strb	r3, [r5, #0]
}
     5d0:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
            if (currentTime - stateChangeTime >= SAMPLE_DURATION_US)
     5d2:	4b1f      	ldr	r3, [pc, #124]	; (650 <refreshDacOutputs()+0xd0>)
     5d4:	6818      	ldr	r0, [r3, #0]
     5d6:	1a20      	subs	r0, r4, r0
     5d8:	2841      	cmp	r0, #65	; 0x41
     5da:	d9dc      	bls.n	596 <refreshDacOutputs()+0x16>
                if (ENABLE_TESTING && currentChannel < 4) {
     5dc:	4c1a      	ldr	r4, [pc, #104]	; (648 <refreshDacOutputs()+0xc8>)
                digitalWrite(MUX_INH_PIN, HIGH);
     5de:	2101      	movs	r1, #1
     5e0:	2005      	movs	r0, #5
     5e2:	f000 fe29 	bl	1238 <digitalWrite>
                if (ENABLE_TESTING && currentChannel < 4) {
     5e6:	6823      	ldr	r3, [r4, #0]
     5e8:	2b03      	cmp	r3, #3
     5ea:	dd18      	ble.n	61e <refreshDacOutputs()+0x9e>
                currentChannel = (currentChannel + 1) % 8;
     5ec:	3301      	adds	r3, #1
     5ee:	425a      	negs	r2, r3
     5f0:	f002 0207 	and.w	r2, r2, #7
     5f4:	f003 0307 	and.w	r3, r3, #7
     5f8:	bf58      	it	pl
     5fa:	4253      	negpl	r3, r2
                currentState = IDLE;
     5fc:	2200      	movs	r2, #0
                currentChannel = (currentChannel + 1) % 8;
     5fe:	6023      	str	r3, [r4, #0]
                currentState = IDLE;
     600:	702a      	strb	r2, [r5, #0]
}
     602:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
            if (currentTime - stateChangeTime >= SETTLE_DURATION_US)
     604:	4e12      	ldr	r6, [pc, #72]	; (650 <refreshDacOutputs()+0xd0>)
     606:	6833      	ldr	r3, [r6, #0]
     608:	1ac3      	subs	r3, r0, r3
     60a:	2b95      	cmp	r3, #149	; 0x95
     60c:	d9c3      	bls.n	596 <refreshDacOutputs()+0x16>
                digitalWrite(MUX_INH_PIN, LOW);
     60e:	2100      	movs	r1, #0
     610:	2005      	movs	r0, #5
     612:	f000 fe11 	bl	1238 <digitalWrite>
                currentState = SAMPLING;
     616:	2302      	movs	r3, #2
                stateChangeTime = currentTime;
     618:	6034      	str	r4, [r6, #0]
                currentState = SAMPLING;
     61a:	702b      	strb	r3, [r5, #0]
}
     61c:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
                    measuredValues[currentChannel] = analogRead(TEST_PINS[currentChannel]);
     61e:	4a0d      	ldr	r2, [pc, #52]	; (654 <refreshDacOutputs()+0xd4>)
     620:	f812 0023 	ldrb.w	r0, [r2, r3, lsl #2]
     624:	f000 f9fc 	bl	a20 <analogRead>
     628:	6823      	ldr	r3, [r4, #0]
     62a:	4a0b      	ldr	r2, [pc, #44]	; (658 <refreshDacOutputs()+0xd8>)
                    errorValues[currentChannel] = dacValues[currentChannel] - measuredValues[currentChannel];
     62c:	490b      	ldr	r1, [pc, #44]	; (65c <refreshDacOutputs()+0xdc>)
                    measuredValues[currentChannel] = analogRead(TEST_PINS[currentChannel]);
     62e:	b280      	uxth	r0, r0
     630:	f822 0013 	strh.w	r0, [r2, r3, lsl #1]
                    errorValues[currentChannel] = dacValues[currentChannel] - measuredValues[currentChannel];
     634:	4a05      	ldr	r2, [pc, #20]	; (64c <refreshDacOutputs()+0xcc>)
     636:	f832 2013 	ldrh.w	r2, [r2, r3, lsl #1]
     63a:	1a12      	subs	r2, r2, r0
     63c:	f821 2013 	strh.w	r2, [r1, r3, lsl #1]
     640:	e7d4      	b.n	5ec <refreshDacOutputs()+0x6c>
     642:	bf00      	nop
     644:	1fff0d4c 	.word	0x1fff0d4c
     648:	1fff0d48 	.word	0x1fff0d48
     64c:	1fff0d50 	.word	0x1fff0d50
     650:	1fff0d7c 	.word	0x1fff0d7c
     654:	0000292c 	.word	0x0000292c
     658:	1fff0d6c 	.word	0x1fff0d6c
     65c:	1fff0d60 	.word	0x1fff0d60

00000660 <printTestResults()>:
{
     660:	e92d 47f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, lr}
        virtual size_t write(const uint8_t *buffer, size_t size) { return usb_serial_write(buffer, size); }
     664:	2103      	movs	r1, #3
     666:	481f      	ldr	r0, [pc, #124]	; (6e4 <printTestResults()+0x84>)
     668:	f8df 8094 	ldr.w	r8, [pc, #148]	; 700 <printTestResults()+0xa0>
     66c:	4f1e      	ldr	r7, [pc, #120]	; (6e8 <printTestResults()+0x88>)
     66e:	4e1f      	ldr	r6, [pc, #124]	; (6ec <printTestResults()+0x8c>)
     670:	f8df a090 	ldr.w	sl, [pc, #144]	; 704 <printTestResults()+0xa4>
	size_t print(int n)				{ return print((long)n); }
     674:	4c1e      	ldr	r4, [pc, #120]	; (6f0 <printTestResults()+0x90>)
     676:	f8df 9090 	ldr.w	r9, [pc, #144]	; 708 <printTestResults()+0xa8>
     67a:	f001 fe45 	bl	2308 <usb_serial_write>
	size_t println(const char s[])			{ return print(s) + println(); }
     67e:	481c      	ldr	r0, [pc, #112]	; (6f0 <printTestResults()+0x90>)
     680:	f000 f8ca 	bl	818 <Print::println()>
    for (int i = 0; i < 4; i++)
     684:	2500      	movs	r5, #0
     686:	2103      	movs	r1, #3
     688:	4650      	mov	r0, sl
     68a:	f001 fe3d 	bl	2308 <usb_serial_write>
	size_t print(int n)				{ return print((long)n); }
     68e:	4629      	mov	r1, r5
     690:	4620      	mov	r0, r4
     692:	f000 f8b9 	bl	808 <Print::print(long)>
     696:	2102      	movs	r1, #2
     698:	4648      	mov	r0, r9
     69a:	f001 fe35 	bl	2308 <usb_serial_write>
     69e:	2105      	movs	r1, #5
     6a0:	4814      	ldr	r0, [pc, #80]	; (6f4 <printTestResults()+0x94>)
     6a2:	f001 fe31 	bl	2308 <usb_serial_write>
     6a6:	f838 1b02 	ldrh.w	r1, [r8], #2
     6aa:	4620      	mov	r0, r4
     6ac:	f000 f8ac 	bl	808 <Print::print(long)>
     6b0:	2109      	movs	r1, #9
     6b2:	4811      	ldr	r0, [pc, #68]	; (6f8 <printTestResults()+0x98>)
     6b4:	f001 fe28 	bl	2308 <usb_serial_write>
     6b8:	f837 1b02 	ldrh.w	r1, [r7], #2
     6bc:	4620      	mov	r0, r4
     6be:	f000 f8a3 	bl	808 <Print::print(long)>
     6c2:	2108      	movs	r1, #8
     6c4:	480d      	ldr	r0, [pc, #52]	; (6fc <printTestResults()+0x9c>)
     6c6:	f001 fe1f 	bl	2308 <usb_serial_write>
     6ca:	f936 1b02 	ldrsh.w	r1, [r6], #2
     6ce:	4620      	mov	r0, r4
     6d0:	f000 f89a 	bl	808 <Print::print(long)>
     6d4:	3501      	adds	r5, #1
	size_t println(const __FlashStringHelper *f)	{ return print(f) + println(); }

	size_t println(uint8_t b)			{ return print(b) + println(); }
	size_t println(int n)				{ return print(n) + println(); }
     6d6:	4620      	mov	r0, r4
     6d8:	f000 f89e 	bl	818 <Print::println()>
     6dc:	2d04      	cmp	r5, #4
     6de:	d1d2      	bne.n	686 <printTestResults()+0x26>
}
     6e0:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
     6e4:	00002900 	.word	0x00002900
     6e8:	1fff0d6c 	.word	0x1fff0d6c
     6ec:	1fff0d60 	.word	0x1fff0d60
     6f0:	1fff0c0c 	.word	0x1fff0c0c
     6f4:	0000290c 	.word	0x0000290c
     6f8:	00002914 	.word	0x00002914
     6fc:	00002920 	.word	0x00002920
     700:	1fff0d50 	.word	0x1fff0d50
     704:	00002904 	.word	0x00002904
     708:	00002908 	.word	0x00002908

0000070c <loop>:
{
     70c:	b510      	push	{r4, lr}
     70e:	4918      	ldr	r1, [pc, #96]	; (770 <loop+0x64>)
     710:	4818      	ldr	r0, [pc, #96]	; (774 <loop+0x68>)
    return (uint16_t)(((uint32_t)value * 4095) / 127);
     712:	4c19      	ldr	r4, [pc, #100]	; (778 <loop+0x6c>)
{
     714:	b082      	sub	sp, #8
     716:	f101 0c08 	add.w	ip, r1, #8
    return (uint16_t)(((uint32_t)value * 4095) / 127);
     71a:	f811 3f01 	ldrb.w	r3, [r1, #1]!
     71e:	ebc3 3303 	rsb	r3, r3, r3, lsl #12
     722:	fba4 e203 	umull	lr, r2, r4, r3
     726:	1a9b      	subs	r3, r3, r2
     728:	eb02 0253 	add.w	r2, r2, r3, lsr #1
     72c:	0992      	lsrs	r2, r2, #6
    for (int i = 0; i < 8; i++)
     72e:	4561      	cmp	r1, ip
    return (uint16_t)(((uint32_t)value * 4095) / 127);
     730:	f820 2f02 	strh.w	r2, [r0, #2]!
    for (int i = 0; i < 8; i++)
     734:	d1f1      	bne.n	71a <loop+0xe>
    refreshDacOutputs();
     736:	f7ff ff23 	bl	580 <refreshDacOutputs()>
	volatile uint32_t ret = systick_millis_count; // single aligned 32 bit is atomic
     73a:	4910      	ldr	r1, [pc, #64]	; (77c <loop+0x70>)
    if (millis() - lastReportTime >= REPORT_INTERVAL_MS)
     73c:	4a10      	ldr	r2, [pc, #64]	; (780 <loop+0x74>)
     73e:	680b      	ldr	r3, [r1, #0]
     740:	9300      	str	r3, [sp, #0]
	return ret;
     742:	9b00      	ldr	r3, [sp, #0]
     744:	6810      	ldr	r0, [r2, #0]
     746:	1a1b      	subs	r3, r3, r0
     748:	2bf9      	cmp	r3, #249	; 0xf9
     74a:	d805      	bhi.n	758 <loop+0x4c>
		return usb_midi_read(channel);
     74c:	2000      	movs	r0, #0
}
     74e:	b002      	add	sp, #8
     750:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
     754:	f001 bb54 	b.w	1e00 <usb_midi_read>
	volatile uint32_t ret = systick_millis_count; // single aligned 32 bit is atomic
     758:	680b      	ldr	r3, [r1, #0]
     75a:	9301      	str	r3, [sp, #4]
	return ret;
     75c:	9b01      	ldr	r3, [sp, #4]
        lastReportTime = millis();
     75e:	6013      	str	r3, [r2, #0]
        printTestResults();
     760:	f7ff ff7e 	bl	660 <printTestResults()>
     764:	2000      	movs	r0, #0
}
     766:	b002      	add	sp, #8
     768:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
     76c:	f001 bb48 	b.w	1e00 <usb_midi_read>
     770:	1fff0d73 	.word	0x1fff0d73
     774:	1fff0d4e 	.word	0x1fff0d4e
     778:	02040811 	.word	0x02040811
     77c:	1fff0d88 	.word	0x1fff0d88
     780:	1fff0d68 	.word	0x1fff0d68

00000784 <Print::printNumber(unsigned long, unsigned char, unsigned char) [clone .part.0]>:
size_t Print::printNumber(unsigned long n, uint8_t base, uint8_t sign)
     784:	b570      	push	{r4, r5, r6, lr}
		base = 10;
     786:	2a01      	cmp	r2, #1
size_t Print::printNumber(unsigned long n, uint8_t base, uint8_t sign)
     788:	b08a      	sub	sp, #40	; 0x28
		base = 10;
     78a:	bf08      	it	eq
     78c:	220a      	moveq	r2, #10
	if (n == 0) {
     78e:	b3a1      	cbz	r1, 7fa <Print::printNumber(unsigned long, unsigned char, unsigned char) [clone .part.0]+0x76>
		i = sizeof(buf) - 1;
     790:	f04f 0e21 	mov.w	lr, #33	; 0x21
     794:	e002      	b.n	79c <Print::printNumber(unsigned long, unsigned char, unsigned char) [clone .part.0]+0x18>
			i--;
     796:	fa5f fe86 	uxtb.w	lr, r6
			n /= base;
     79a:	4629      	mov	r1, r5
			digit = n % base;
     79c:	fbb1 f5f2 	udiv	r5, r1, r2
     7a0:	fb02 1415 	mls	r4, r2, r5, r1
     7a4:	fa5f fc84 	uxtb.w	ip, r4
			buf[i] = ((digit < 10) ? '0' + digit : 'A' + digit - 10);
     7a8:	2c09      	cmp	r4, #9
     7aa:	f10c 0430 	add.w	r4, ip, #48	; 0x30
     7ae:	bf98      	it	ls
     7b0:	fa5f fc84 	uxtbls.w	ip, r4
     7b4:	f10e 0428 	add.w	r4, lr, #40	; 0x28
     7b8:	bf88      	it	hi
     7ba:	f10c 0c37 	addhi.w	ip, ip, #55	; 0x37
     7be:	446c      	add	r4, sp
     7c0:	bf88      	it	hi
     7c2:	fa5f fc8c 	uxtbhi.w	ip, ip
			if (n == 0) break;
     7c6:	4291      	cmp	r1, r2
			i--;
     7c8:	f10e 36ff 	add.w	r6, lr, #4294967295
			buf[i] = ((digit < 10) ? '0' + digit : 'A' + digit - 10);
     7cc:	f804 cc24 	strb.w	ip, [r4, #-36]
			if (n == 0) break;
     7d0:	d2e1      	bcs.n	796 <Print::printNumber(unsigned long, unsigned char, unsigned char) [clone .part.0]+0x12>
	if (sign) {
     7d2:	b14b      	cbz	r3, 7e8 <Print::printNumber(unsigned long, unsigned char, unsigned char) [clone .part.0]+0x64>
		i--;
     7d4:	f10e 3eff 	add.w	lr, lr, #4294967295
     7d8:	fa5f fe8e 	uxtb.w	lr, lr
		buf[i] = '-';
     7dc:	f10e 0328 	add.w	r3, lr, #40	; 0x28
     7e0:	446b      	add	r3, sp
     7e2:	222d      	movs	r2, #45	; 0x2d
     7e4:	f803 2c24 	strb.w	r2, [r3, #-36]
	return write(buf + i, sizeof(buf) - i);
     7e8:	6803      	ldr	r3, [r0, #0]
     7ea:	a901      	add	r1, sp, #4
     7ec:	685b      	ldr	r3, [r3, #4]
     7ee:	f1ce 0222 	rsb	r2, lr, #34	; 0x22
     7f2:	4471      	add	r1, lr
     7f4:	4798      	blx	r3
}
     7f6:	b00a      	add	sp, #40	; 0x28
     7f8:	bd70      	pop	{r4, r5, r6, pc}
		buf[sizeof(buf) - 1] = '0';
     7fa:	2230      	movs	r2, #48	; 0x30
     7fc:	f88d 2025 	strb.w	r2, [sp, #37]	; 0x25
		i = sizeof(buf) - 1;
     800:	f04f 0e21 	mov.w	lr, #33	; 0x21
     804:	e7e5      	b.n	7d2 <Print::printNumber(unsigned long, unsigned char, unsigned char) [clone .part.0]+0x4e>
     806:	bf00      	nop

00000808 <Print::print(long)>:
	if (n < 0) {
     808:	2900      	cmp	r1, #0
		n = -n;
     80a:	bfba      	itte	lt
     80c:	4249      	neglt	r1, r1
		sign = '-';
     80e:	232d      	movlt	r3, #45	; 0x2d
	uint8_t sign=0;
     810:	2300      	movge	r3, #0
	if (base == 0) {
     812:	220a      	movs	r2, #10
     814:	f7ff bfb6 	b.w	784 <Print::printNumber(unsigned long, unsigned char, unsigned char) [clone .part.0]>

00000818 <Print::println()>:
{
     818:	b500      	push	{lr}
	return write(buf, 2);
     81a:	6803      	ldr	r3, [r0, #0]
{
     81c:	b083      	sub	sp, #12
	uint8_t buf[2]={'\r', '\n'};
     81e:	f640 220d 	movw	r2, #2573	; 0xa0d
     822:	f8ad 2004 	strh.w	r2, [sp, #4]
	return write(buf, 2);
     826:	685b      	ldr	r3, [r3, #4]
     828:	2202      	movs	r2, #2
     82a:	a901      	add	r1, sp, #4
     82c:	4798      	blx	r3
}
     82e:	b003      	add	sp, #12
     830:	f85d fb04 	ldr.w	pc, [sp], #4

00000834 <wait_for_cal>:
	}
	calibrating = 1;
}

static void wait_for_cal(void)
{
     834:	b538      	push	{r3, r4, r5, lr}
	uint16_t sum;

	//serial_print("wait_for_cal\n");
#if defined(HAS_KINETIS_ADC0) && defined(HAS_KINETIS_ADC1)
	while ((ADC0_SC3 & ADC_SC3_CAL) || (ADC1_SC3 & ADC_SC3_CAL)) {
     836:	4c2a      	ldr	r4, [pc, #168]	; (8e0 <wait_for_cal+0xac>)
     838:	4d2a      	ldr	r5, [pc, #168]	; (8e4 <wait_for_cal+0xb0>)
     83a:	e001      	b.n	840 <wait_for_cal+0xc>
		yield(); // wait
     83c:	f001 fe6a 	bl	2514 <yield>
	while ((ADC0_SC3 & ADC_SC3_CAL) || (ADC1_SC3 & ADC_SC3_CAL)) {
     840:	6a63      	ldr	r3, [r4, #36]	; 0x24
     842:	061b      	lsls	r3, r3, #24
     844:	d4fa      	bmi.n	83c <wait_for_cal+0x8>
     846:	6a6b      	ldr	r3, [r5, #36]	; 0x24
     848:	f013 0380 	ands.w	r3, r3, #128	; 0x80
     84c:	d1f6      	bne.n	83c <wait_for_cal+0x8>
#elif defined(HAS_KINETIS_ADC0)
	while (ADC0_SC3 & ADC_SC3_CAL) {
		yield(); // wait
	}
#endif
	__disable_irq();
     84e:	b672      	cpsid	i
	if (calibrating) {
     850:	4925      	ldr	r1, [pc, #148]	; (8e8 <wait_for_cal+0xb4>)
     852:	780a      	ldrb	r2, [r1, #0]
     854:	2a00      	cmp	r2, #0
     856:	d040      	beq.n	8da <wait_for_cal+0xa6>
		//serial_print("\n");
		sum = ADC0_CLPS + ADC0_CLP4 + ADC0_CLP3 + ADC0_CLP2 + ADC0_CLP1 + ADC0_CLP0;
     858:	6ba2      	ldr	r2, [r4, #56]	; 0x38
     85a:	6be0      	ldr	r0, [r4, #60]	; 0x3c
     85c:	4402      	add	r2, r0
     85e:	6c20      	ldr	r0, [r4, #64]	; 0x40
     860:	4402      	add	r2, r0
     862:	6c60      	ldr	r0, [r4, #68]	; 0x44
     864:	4402      	add	r2, r0
     866:	6ca0      	ldr	r0, [r4, #72]	; 0x48
     868:	4402      	add	r2, r0
     86a:	6ce0      	ldr	r0, [r4, #76]	; 0x4c
     86c:	4402      	add	r2, r0
		sum = (sum / 2) | 0x8000;
     86e:	f3c2 024e 	ubfx	r2, r2, #1, #15
		ADC0_PG = sum;
     872:	f442 4200 	orr.w	r2, r2, #32768	; 0x8000
     876:	62e2      	str	r2, [r4, #44]	; 0x2c
		//serial_print("ADC0_PG = ");
		//serial_phex16(sum);
		//serial_print("\n");
		sum = ADC0_CLMS + ADC0_CLM4 + ADC0_CLM3 + ADC0_CLM2 + ADC0_CLM1 + ADC0_CLM0;
     878:	6da2      	ldr	r2, [r4, #88]	; 0x58
     87a:	6de0      	ldr	r0, [r4, #92]	; 0x5c
     87c:	4402      	add	r2, r0
     87e:	6e20      	ldr	r0, [r4, #96]	; 0x60
     880:	4402      	add	r2, r0
     882:	6e60      	ldr	r0, [r4, #100]	; 0x64
     884:	4402      	add	r2, r0
     886:	6ea0      	ldr	r0, [r4, #104]	; 0x68
     888:	4402      	add	r2, r0
     88a:	6ee0      	ldr	r0, [r4, #108]	; 0x6c
     88c:	4402      	add	r2, r0
		sum = (sum / 2) | 0x8000;
     88e:	f3c2 024e 	ubfx	r2, r2, #1, #15
		ADC0_MG = sum;
     892:	f442 4200 	orr.w	r2, r2, #32768	; 0x8000
     896:	6322      	str	r2, [r4, #48]	; 0x30
		//serial_print("ADC0_MG = ");
		//serial_phex16(sum);
		//serial_print("\n");
#ifdef HAS_KINETIS_ADC1
		sum = ADC1_CLPS + ADC1_CLP4 + ADC1_CLP3 + ADC1_CLP2 + ADC1_CLP1 + ADC1_CLP0;
     898:	6baa      	ldr	r2, [r5, #56]	; 0x38
     89a:	6be8      	ldr	r0, [r5, #60]	; 0x3c
     89c:	4402      	add	r2, r0
     89e:	6c28      	ldr	r0, [r5, #64]	; 0x40
     8a0:	4402      	add	r2, r0
     8a2:	6c68      	ldr	r0, [r5, #68]	; 0x44
     8a4:	4402      	add	r2, r0
     8a6:	6ca8      	ldr	r0, [r5, #72]	; 0x48
     8a8:	4402      	add	r2, r0
     8aa:	6ce8      	ldr	r0, [r5, #76]	; 0x4c
     8ac:	4402      	add	r2, r0
		sum = (sum / 2) | 0x8000;
     8ae:	f3c2 024e 	ubfx	r2, r2, #1, #15
		ADC1_PG = sum;
     8b2:	f442 4200 	orr.w	r2, r2, #32768	; 0x8000
     8b6:	62ea      	str	r2, [r5, #44]	; 0x2c
		sum = ADC1_CLMS + ADC1_CLM4 + ADC1_CLM3 + ADC1_CLM2 + ADC1_CLM1 + ADC1_CLM0;
     8b8:	6daa      	ldr	r2, [r5, #88]	; 0x58
     8ba:	6de8      	ldr	r0, [r5, #92]	; 0x5c
     8bc:	4402      	add	r2, r0
     8be:	6e28      	ldr	r0, [r5, #96]	; 0x60
     8c0:	4402      	add	r2, r0
     8c2:	6e68      	ldr	r0, [r5, #100]	; 0x64
     8c4:	4402      	add	r2, r0
     8c6:	6ea8      	ldr	r0, [r5, #104]	; 0x68
     8c8:	4402      	add	r2, r0
     8ca:	6ee8      	ldr	r0, [r5, #108]	; 0x6c
     8cc:	4402      	add	r2, r0
		sum = (sum / 2) | 0x8000;
     8ce:	f3c2 024e 	ubfx	r2, r2, #1, #15
		ADC1_MG = sum;
     8d2:	f442 4200 	orr.w	r2, r2, #32768	; 0x8000
     8d6:	632a      	str	r2, [r5, #48]	; 0x30
#endif
		calibrating = 0;
     8d8:	700b      	strb	r3, [r1, #0]
	}
	__enable_irq();
     8da:	b662      	cpsie	i
}
     8dc:	bd38      	pop	{r3, r4, r5, pc}
     8de:	bf00      	nop
     8e0:	4003b000 	.word	0x4003b000
     8e4:	400bb000 	.word	0x400bb000
     8e8:	1fff0d84 	.word	0x1fff0d84

000008ec <analog_init>:
	VREF_TRM = 0x60;
     8ec:	4b2a      	ldr	r3, [pc, #168]	; (998 <analog_init+0xac>)
	if (analog_config_bits == 8) {
     8ee:	4a2b      	ldr	r2, [pc, #172]	; (99c <analog_init+0xb0>)
	VREF_TRM = 0x60;
     8f0:	2060      	movs	r0, #96	; 0x60
	VREF_SC = 0xE1;		// enable 1.2 volt ref
     8f2:	21e1      	movs	r1, #225	; 0xe1
	VREF_TRM = 0x60;
     8f4:	7018      	strb	r0, [r3, #0]
	VREF_SC = 0xE1;		// enable 1.2 volt ref
     8f6:	7059      	strb	r1, [r3, #1]
	if (analog_config_bits == 8) {
     8f8:	7813      	ldrb	r3, [r2, #0]
		ADC0_CFG1 = ADC_CFG1_8BIT + ADC_CFG1_MODE(0);
     8fa:	4829      	ldr	r0, [pc, #164]	; (9a0 <analog_init+0xb4>)
	if (analog_config_bits == 8) {
     8fc:	2b08      	cmp	r3, #8
     8fe:	d034      	beq.n	96a <analog_init+0x7e>
	} else if (analog_config_bits == 10) {
     900:	2b0a      	cmp	r3, #10
     902:	d020      	beq.n	946 <analog_init+0x5a>
	} else if (analog_config_bits == 12) {
     904:	2b0c      	cmp	r3, #12
		ADC1_CFG1 = ADC_CFG1_12BIT + ADC_CFG1_MODE(1) + ADC_CFG1_ADLSMP;
     906:	4b27      	ldr	r3, [pc, #156]	; (9a4 <analog_init+0xb8>)
		ADC0_CFG1 = ADC_CFG1_12BIT + ADC_CFG1_MODE(1) + ADC_CFG1_ADLSMP;
     908:	bf0c      	ite	eq
     90a:	2135      	moveq	r1, #53	; 0x35
		ADC0_CFG1 = ADC_CFG1_16BIT + ADC_CFG1_MODE(3) + ADC_CFG1_ADLSMP;
     90c:	215d      	movne	r1, #93	; 0x5d
		ADC0_CFG2 = ADC_CFG2_MUXSEL + ADC_CFG2_ADLSTS(2);
     90e:	2212      	movs	r2, #18
		ADC0_CFG1 = ADC_CFG1_16BIT + ADC_CFG1_MODE(3) + ADC_CFG1_ADLSMP;
     910:	6081      	str	r1, [r0, #8]
		ADC0_CFG2 = ADC_CFG2_MUXSEL + ADC_CFG2_ADLSTS(2);
     912:	60c2      	str	r2, [r0, #12]
		ADC1_CFG1 = ADC_CFG1_16BIT + ADC_CFG1_MODE(3) + ADC_CFG1_ADLSMP;
     914:	6099      	str	r1, [r3, #8]
		ADC1_CFG2 = ADC_CFG2_MUXSEL + ADC_CFG2_ADLSTS(2);
     916:	60da      	str	r2, [r3, #12]
	if (analog_reference_internal) {
     918:	4b23      	ldr	r3, [pc, #140]	; (9a8 <analog_init+0xbc>)
		ADC0_SC2 = ADC_SC2_REFSEL(1); // 1.2V ref
     91a:	4921      	ldr	r1, [pc, #132]	; (9a0 <analog_init+0xb4>)
	if (analog_reference_internal) {
     91c:	781b      	ldrb	r3, [r3, #0]
		ADC1_SC2 = ADC_SC2_REFSEL(1); // 1.2V ref
     91e:	4a21      	ldr	r2, [pc, #132]	; (9a4 <analog_init+0xb8>)
	if (analog_reference_internal) {
     920:	b1cb      	cbz	r3, 956 <analog_init+0x6a>
		ADC0_SC2 = ADC_SC2_REFSEL(1); // 1.2V ref
     922:	2301      	movs	r3, #1
     924:	620b      	str	r3, [r1, #32]
		ADC1_SC2 = ADC_SC2_REFSEL(1); // 1.2V ref
     926:	6213      	str	r3, [r2, #32]
	num = analog_num_average;
     928:	4b20      	ldr	r3, [pc, #128]	; (9ac <analog_init+0xc0>)
		ADC0_SC3 = ADC_SC3_CAL;  // begin cal
     92a:	491d      	ldr	r1, [pc, #116]	; (9a0 <analog_init+0xb4>)
	num = analog_num_average;
     92c:	781b      	ldrb	r3, [r3, #0]
		ADC1_SC3 = ADC_SC3_CAL;  // begin cal
     92e:	4a1d      	ldr	r2, [pc, #116]	; (9a4 <analog_init+0xb8>)
	if (num <= 1) {
     930:	2b01      	cmp	r3, #1
     932:	d913      	bls.n	95c <analog_init+0x70>
	} else if (num <= 4) {
     934:	2b04      	cmp	r3, #4
     936:	d81b      	bhi.n	970 <analog_init+0x84>
		ADC0_SC3 = ADC_SC3_CAL + ADC_SC3_AVGE + ADC_SC3_AVGS(0);
     938:	2384      	movs	r3, #132	; 0x84
     93a:	624b      	str	r3, [r1, #36]	; 0x24
		ADC1_SC3 = ADC_SC3_CAL + ADC_SC3_AVGE + ADC_SC3_AVGS(0);
     93c:	6253      	str	r3, [r2, #36]	; 0x24
	calibrating = 1;
     93e:	4b1c      	ldr	r3, [pc, #112]	; (9b0 <analog_init+0xc4>)
     940:	2201      	movs	r2, #1
     942:	701a      	strb	r2, [r3, #0]
}
     944:	4770      	bx	lr
		ADC1_CFG1 = ADC_CFG1_10BIT + ADC_CFG1_MODE(2) + ADC_CFG1_ADLSMP;
     946:	4b17      	ldr	r3, [pc, #92]	; (9a4 <analog_init+0xb8>)
		ADC0_CFG1 = ADC_CFG1_10BIT + ADC_CFG1_MODE(2) + ADC_CFG1_ADLSMP;
     948:	2139      	movs	r1, #57	; 0x39
		ADC0_CFG2 = ADC_CFG2_MUXSEL + ADC_CFG2_ADLSTS(3);
     94a:	2213      	movs	r2, #19
		ADC0_CFG1 = ADC_CFG1_10BIT + ADC_CFG1_MODE(2) + ADC_CFG1_ADLSMP;
     94c:	6081      	str	r1, [r0, #8]
		ADC0_CFG2 = ADC_CFG2_MUXSEL + ADC_CFG2_ADLSTS(3);
     94e:	60c2      	str	r2, [r0, #12]
		ADC1_CFG1 = ADC_CFG1_10BIT + ADC_CFG1_MODE(2) + ADC_CFG1_ADLSMP;
     950:	6099      	str	r1, [r3, #8]
		ADC1_CFG2 = ADC_CFG2_MUXSEL + ADC_CFG2_ADLSTS(3);
     952:	60da      	str	r2, [r3, #12]
     954:	e7e0      	b.n	918 <analog_init+0x2c>
		ADC0_SC2 = ADC_SC2_REFSEL(0); // vcc/ext ref
     956:	620b      	str	r3, [r1, #32]
		ADC1_SC2 = ADC_SC2_REFSEL(0); // vcc/ext ref
     958:	6213      	str	r3, [r2, #32]
     95a:	e7e5      	b.n	928 <analog_init+0x3c>
		ADC0_SC3 = ADC_SC3_CAL;  // begin cal
     95c:	2380      	movs	r3, #128	; 0x80
     95e:	624b      	str	r3, [r1, #36]	; 0x24
		ADC1_SC3 = ADC_SC3_CAL;  // begin cal
     960:	6253      	str	r3, [r2, #36]	; 0x24
	calibrating = 1;
     962:	4b13      	ldr	r3, [pc, #76]	; (9b0 <analog_init+0xc4>)
     964:	2201      	movs	r2, #1
     966:	701a      	strb	r2, [r3, #0]
}
     968:	4770      	bx	lr
		ADC1_CFG1 = ADC_CFG1_8BIT + ADC_CFG1_MODE(0);
     96a:	4b0e      	ldr	r3, [pc, #56]	; (9a4 <analog_init+0xb8>)
		ADC0_CFG1 = ADC_CFG1_8BIT + ADC_CFG1_MODE(0);
     96c:	2121      	movs	r1, #33	; 0x21
     96e:	e7ec      	b.n	94a <analog_init+0x5e>
	} else if (num <= 8) {
     970:	2b08      	cmp	r3, #8
     972:	d806      	bhi.n	982 <analog_init+0x96>
		ADC0_SC3 = ADC_SC3_CAL + ADC_SC3_AVGE + ADC_SC3_AVGS(1);
     974:	2385      	movs	r3, #133	; 0x85
     976:	624b      	str	r3, [r1, #36]	; 0x24
		ADC1_SC3 = ADC_SC3_CAL + ADC_SC3_AVGE + ADC_SC3_AVGS(1);
     978:	6253      	str	r3, [r2, #36]	; 0x24
	calibrating = 1;
     97a:	4b0d      	ldr	r3, [pc, #52]	; (9b0 <analog_init+0xc4>)
     97c:	2201      	movs	r2, #1
     97e:	701a      	strb	r2, [r3, #0]
}
     980:	4770      	bx	lr
	} else if (num <= 16) {
     982:	2b10      	cmp	r3, #16
		ADC0_SC3 = ADC_SC3_CAL + ADC_SC3_AVGE + ADC_SC3_AVGS(2);
     984:	bf94      	ite	ls
     986:	2386      	movls	r3, #134	; 0x86
		ADC0_SC3 = ADC_SC3_CAL + ADC_SC3_AVGE + ADC_SC3_AVGS(3);
     988:	2387      	movhi	r3, #135	; 0x87
     98a:	624b      	str	r3, [r1, #36]	; 0x24
		ADC1_SC3 = ADC_SC3_CAL + ADC_SC3_AVGE + ADC_SC3_AVGS(3);
     98c:	6253      	str	r3, [r2, #36]	; 0x24
	calibrating = 1;
     98e:	4b08      	ldr	r3, [pc, #32]	; (9b0 <analog_init+0xc4>)
     990:	2201      	movs	r2, #1
     992:	701a      	strb	r2, [r3, #0]
}
     994:	4770      	bx	lr
     996:	bf00      	nop
     998:	40074000 	.word	0x40074000
     99c:	1fff0c08 	.word	0x1fff0c08
     9a0:	4003b000 	.word	0x4003b000
     9a4:	400bb000 	.word	0x400bb000
     9a8:	1fff0d82 	.word	0x1fff0d82
     9ac:	1fff0c09 	.word	0x1fff0c09
     9b0:	1fff0d84 	.word	0x1fff0d84

000009b4 <analogReadRes>:

void analogReadRes(unsigned int bits)
{
	unsigned int config;

	if (bits >= 13) {
     9b4:	280c      	cmp	r0, #12
{
     9b6:	b410      	push	{r4}
	if (bits >= 13) {
     9b8:	d918      	bls.n	9ec <analogReadRes+0x38>
		if (bits > 16) bits = 16;
     9ba:	2310      	movs	r3, #16
     9bc:	4298      	cmp	r0, r3
     9be:	bf28      	it	cs
     9c0:	4618      	movcs	r0, r3
		config = 16;
     9c2:	4619      	mov	r1, r3
		config = 10;
	} else {
		config = 8;
	}
	analog_right_shift = config - bits;
	if (config != analog_config_bits) {
     9c4:	4a11      	ldr	r2, [pc, #68]	; (a0c <analogReadRes+0x58>)
	analog_right_shift = config - bits;
     9c6:	4c12      	ldr	r4, [pc, #72]	; (a10 <analogReadRes+0x5c>)
     9c8:	1a18      	subs	r0, r3, r0
     9ca:	7020      	strb	r0, [r4, #0]
	if (config != analog_config_bits) {
     9cc:	7810      	ldrb	r0, [r2, #0]
     9ce:	4288      	cmp	r0, r1
     9d0:	d016      	beq.n	a00 <analogReadRes+0x4c>
		analog_config_bits = config;
		if (calibrating) {
     9d2:	4910      	ldr	r1, [pc, #64]	; (a14 <analogReadRes+0x60>)
		analog_config_bits = config;
     9d4:	7013      	strb	r3, [r2, #0]
		if (calibrating) {
     9d6:	780b      	ldrb	r3, [r1, #0]
     9d8:	b123      	cbz	r3, 9e4 <analogReadRes+0x30>
			ADC0_SC3 = 0; // cancel cal
     9da:	490f      	ldr	r1, [pc, #60]	; (a18 <analogReadRes+0x64>)
			#ifdef HAS_KINETIS_ADC1
			ADC1_SC3 = 0;
     9dc:	4a0f      	ldr	r2, [pc, #60]	; (a1c <analogReadRes+0x68>)
			ADC0_SC3 = 0; // cancel cal
     9de:	2300      	movs	r3, #0
     9e0:	624b      	str	r3, [r1, #36]	; 0x24
			ADC1_SC3 = 0;
     9e2:	6253      	str	r3, [r2, #36]	; 0x24
			#endif
		}
		analog_init();
	}
}
     9e4:	f85d 4b04 	ldr.w	r4, [sp], #4
		analog_init();
     9e8:	f7ff bf80 	b.w	8ec <analog_init>
	} else if (bits >= 11) {
     9ec:	280a      	cmp	r0, #10
     9ee:	d902      	bls.n	9f6 <analogReadRes+0x42>
     9f0:	230c      	movs	r3, #12
		config = 12;
     9f2:	4619      	mov	r1, r3
     9f4:	e7e6      	b.n	9c4 <analogReadRes+0x10>
	} else if (bits >= 9) {
     9f6:	2808      	cmp	r0, #8
     9f8:	d905      	bls.n	a06 <analogReadRes+0x52>
     9fa:	230a      	movs	r3, #10
		config = 10;
     9fc:	4619      	mov	r1, r3
     9fe:	e7e1      	b.n	9c4 <analogReadRes+0x10>
}
     a00:	f85d 4b04 	ldr.w	r4, [sp], #4
     a04:	4770      	bx	lr
	} else if (bits >= 9) {
     a06:	2308      	movs	r3, #8
		config = 8;
     a08:	4619      	mov	r1, r3
     a0a:	e7db      	b.n	9c4 <analogReadRes+0x10>
     a0c:	1fff0c08 	.word	0x1fff0c08
     a10:	1fff0d83 	.word	0x1fff0d83
     a14:	1fff0d84 	.word	0x1fff0d84
     a18:	4003b000 	.word	0x4003b000
     a1c:	400bb000 	.word	0x400bb000

00000a20 <analogRead>:
	uint8_t channel;

	//serial_phex(pin);
	//serial_print(" ");

	if (pin >= sizeof(pin2sc1a)) return 0;
     a20:	2847      	cmp	r0, #71	; 0x47
     a22:	d829      	bhi.n	a78 <analogRead+0x58>
	channel = pin2sc1a[pin];
     a24:	4b30      	ldr	r3, [pc, #192]	; (ae8 <analogRead+0xc8>)
{
     a26:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
	channel = pin2sc1a[pin];
     a2a:	5c1e      	ldrb	r6, [r3, r0]
	if (channel == 255) return 0;
     a2c:	2eff      	cmp	r6, #255	; 0xff
     a2e:	d053      	beq.n	ad8 <analogRead+0xb8>

	if (calibrating) wait_for_cal();
     a30:	4b2e      	ldr	r3, [pc, #184]	; (aec <analogRead+0xcc>)
     a32:	781b      	ldrb	r3, [r3, #0]
     a34:	bb13      	cbnz	r3, a7c <analogRead+0x5c>

#ifdef HAS_KINETIS_ADC1
	if (channel & 0x80) goto beginADC1;
     a36:	0634      	lsls	r4, r6, #24
     a38:	d424      	bmi.n	a84 <analogRead+0x64>
#endif

	// This interrupt disable stuff is meant to allow use of
	// analogRead() in both main program and interrupts.
	__disable_irq();
     a3a:	b672      	cpsid	i
     a3c:	4c2c      	ldr	r4, [pc, #176]	; (af0 <analogRead+0xd0>)
		channel &= 0x3F;
	} else {
		ADC0_CFG2 |= ADC_CFG2_MUXSEL;
	}
#endif
	ADC0_SC1A = channel;
     a3e:	4d2d      	ldr	r5, [pc, #180]	; (af4 <analogRead+0xd4>)
	analogReadBusyADC0 = 1;
     a40:	2701      	movs	r7, #1
	ADC0_SC1A = channel;
     a42:	602e      	str	r6, [r5, #0]
	analogReadBusyADC0 = 1;
     a44:	7027      	strb	r7, [r4, #0]
	__enable_irq();
     a46:	b662      	cpsie	i
	while (1) {
		__disable_irq();
     a48:	b672      	cpsid	i
		if ((ADC0_SC1A & ADC_SC1_COCO)) {
     a4a:	682b      	ldr	r3, [r5, #0]
     a4c:	0618      	lsls	r0, r3, #24
     a4e:	d409      	bmi.n	a64 <analogRead+0x44>
			return result;
		}
		// detect if analogRead was used from an interrupt
		// if so, our analogRead got canceled, so it must
		// be restarted.
		if (!analogReadBusyADC0) goto startADC0;
     a50:	7823      	ldrb	r3, [r4, #0]
     a52:	2b00      	cmp	r3, #0
     a54:	d0f5      	beq.n	a42 <analogRead+0x22>
		__enable_irq();
     a56:	b662      	cpsie	i
		yield(); // TODO: what happens if yield-called code uses analogRead()
     a58:	f001 fd5c 	bl	2514 <yield>
		__disable_irq();
     a5c:	b672      	cpsid	i
		if ((ADC0_SC1A & ADC_SC1_COCO)) {
     a5e:	682b      	ldr	r3, [r5, #0]
     a60:	0619      	lsls	r1, r3, #24
     a62:	d5f5      	bpl.n	a50 <analogRead+0x30>
			result = ADC0_RA;
     a64:	4a23      	ldr	r2, [pc, #140]	; (af4 <analogRead+0xd4>)
			analogReadBusyADC0 = 0;
     a66:	2300      	movs	r3, #0
			result = ADC0_RA;
     a68:	6910      	ldr	r0, [r2, #16]
			analogReadBusyADC0 = 0;
     a6a:	7023      	strb	r3, [r4, #0]
			__enable_irq();
     a6c:	b662      	cpsie	i
			result >>= analog_right_shift;
     a6e:	4b22      	ldr	r3, [pc, #136]	; (af8 <analogRead+0xd8>)
     a70:	781b      	ldrb	r3, [r3, #0]
     a72:	4118      	asrs	r0, r3
		if (!analogReadBusyADC1) goto startADC1;
		__enable_irq();
		yield();
	}
#endif
}
     a74:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
	if (pin >= sizeof(pin2sc1a)) return 0;
     a78:	2000      	movs	r0, #0
}
     a7a:	4770      	bx	lr
	if (calibrating) wait_for_cal();
     a7c:	f7ff feda 	bl	834 <wait_for_cal>
	if (channel & 0x80) goto beginADC1;
     a80:	0634      	lsls	r4, r6, #24
     a82:	d5da      	bpl.n	a3a <analogRead+0x1a>
	__disable_irq();
     a84:	b672      	cpsid	i
     a86:	4d1d      	ldr	r5, [pc, #116]	; (afc <analogRead+0xdc>)
		ADC1_CFG2 |= ADC_CFG2_MUXSEL;
     a88:	4c1d      	ldr	r4, [pc, #116]	; (b00 <analogRead+0xe0>)
     a8a:	f006 0840 	and.w	r8, r6, #64	; 0x40
	analogReadBusyADC1 = 1;
     a8e:	2701      	movs	r7, #1
	ADC1_SC1A = channel & 0x3F;
     a90:	f006 063f 	and.w	r6, r6, #63	; 0x3f
		ADC1_CFG2 &= ~ADC_CFG2_MUXSEL;
     a94:	68e3      	ldr	r3, [r4, #12]
	if (channel & 0x40) {
     a96:	f1b8 0f00 	cmp.w	r8, #0
     a9a:	d020      	beq.n	ade <analogRead+0xbe>
		ADC1_CFG2 &= ~ADC_CFG2_MUXSEL;
     a9c:	f023 0310 	bic.w	r3, r3, #16
     aa0:	60e3      	str	r3, [r4, #12]
	ADC1_SC1A = channel & 0x3F;
     aa2:	6026      	str	r6, [r4, #0]
	analogReadBusyADC1 = 1;
     aa4:	702f      	strb	r7, [r5, #0]
	__enable_irq();
     aa6:	b662      	cpsie	i
		__disable_irq();
     aa8:	b672      	cpsid	i
		if ((ADC1_SC1A & ADC_SC1_COCO)) {
     aaa:	6823      	ldr	r3, [r4, #0]
     aac:	061a      	lsls	r2, r3, #24
     aae:	d409      	bmi.n	ac4 <analogRead+0xa4>
		if (!analogReadBusyADC1) goto startADC1;
     ab0:	782b      	ldrb	r3, [r5, #0]
     ab2:	2b00      	cmp	r3, #0
     ab4:	d0ee      	beq.n	a94 <analogRead+0x74>
		__enable_irq();
     ab6:	b662      	cpsie	i
		yield();
     ab8:	f001 fd2c 	bl	2514 <yield>
		__disable_irq();
     abc:	b672      	cpsid	i
		if ((ADC1_SC1A & ADC_SC1_COCO)) {
     abe:	6823      	ldr	r3, [r4, #0]
     ac0:	061b      	lsls	r3, r3, #24
     ac2:	d5f5      	bpl.n	ab0 <analogRead+0x90>
			result = ADC1_RA;
     ac4:	4a0e      	ldr	r2, [pc, #56]	; (b00 <analogRead+0xe0>)
			analogReadBusyADC1 = 0;
     ac6:	2300      	movs	r3, #0
			result = ADC1_RA;
     ac8:	6910      	ldr	r0, [r2, #16]
			analogReadBusyADC1 = 0;
     aca:	702b      	strb	r3, [r5, #0]
			__enable_irq();
     acc:	b662      	cpsie	i
			result >>= analog_right_shift;
     ace:	4b0a      	ldr	r3, [pc, #40]	; (af8 <analogRead+0xd8>)
     ad0:	781b      	ldrb	r3, [r3, #0]
     ad2:	4118      	asrs	r0, r3
}
     ad4:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
	if (pin >= sizeof(pin2sc1a)) return 0;
     ad8:	2000      	movs	r0, #0
}
     ada:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
		ADC1_CFG2 |= ADC_CFG2_MUXSEL;
     ade:	f043 0310 	orr.w	r3, r3, #16
     ae2:	60e3      	str	r3, [r4, #12]
     ae4:	e7dd      	b.n	aa2 <analogRead+0x82>
     ae6:	bf00      	nop
     ae8:	0000293c 	.word	0x0000293c
     aec:	1fff0d84 	.word	0x1fff0d84
     af0:	1fff0d80 	.word	0x1fff0d80
     af4:	4003b000 	.word	0x4003b000
     af8:	1fff0d83 	.word	0x1fff0d83
     afc:	1fff0d81 	.word	0x1fff0d81
     b00:	400bb000 	.word	0x400bb000

00000b04 <analogWriteDAC0>:
typedef int16_t __attribute__((__may_alias__)) aliased_int16_t;

void analogWriteDAC0(int val)
{
#if defined(__MK20DX256__) || defined(__MK64FX512__) || defined(__MK66FX1M0__)
	SIM_SCGC2 |= SIM_SCGC2_DAC0;
     b04:	4a0b      	ldr	r2, [pc, #44]	; (b34 <analogWriteDAC0+0x30>)
	if (analog_reference_internal) {
     b06:	490c      	ldr	r1, [pc, #48]	; (b38 <analogWriteDAC0+0x34>)
	SIM_SCGC2 |= SIM_SCGC2_DAC0;
     b08:	6ad3      	ldr	r3, [r2, #44]	; 0x2c
     b0a:	f443 5380 	orr.w	r3, r3, #4096	; 0x1000
     b0e:	62d3      	str	r3, [r2, #44]	; 0x2c
	if (analog_reference_internal) {
     b10:	780b      	ldrb	r3, [r1, #0]
     b12:	b14b      	cbz	r3, b28 <analogWriteDAC0+0x24>
		DAC0_C0 = DAC_C0_DACEN;  // 1.2V ref is DACREF_1
     b14:	4b09      	ldr	r3, [pc, #36]	; (b3c <analogWriteDAC0+0x38>)
     b16:	2280      	movs	r2, #128	; 0x80
     b18:	f883 2021 	strb.w	r2, [r3, #33]	; 0x21
	} else {
		DAC0_C0 = DAC_C0_DACEN | DAC_C0_DACRFS; // 3.3V VDDA is DACREF_2
	}
	__asm__ ("usat    %[value], #12, %[value]\n\t" : [value] "+r" (val));  // 0 <= val <= 4095

	*(volatile aliased_int16_t *)&(DAC0_DAT0L) = val;
     b1c:	4b07      	ldr	r3, [pc, #28]	; (b3c <analogWriteDAC0+0x38>)
	__asm__ ("usat    %[value], #12, %[value]\n\t" : [value] "+r" (val));  // 0 <= val <= 4095
     b1e:	f380 000c 	usat	r0, #12, r0
	*(volatile aliased_int16_t *)&(DAC0_DAT0L) = val;
     b22:	b200      	sxth	r0, r0
     b24:	8018      	strh	r0, [r3, #0]
	if (val < 0) val = 0;
	else if (val > 4095) val = 4095;

	*(volatile aliased_int16_t *)&(DAC0_DAT0L) = val;
#endif
}
     b26:	4770      	bx	lr
		DAC0_C0 = DAC_C0_DACEN | DAC_C0_DACRFS; // 3.3V VDDA is DACREF_2
     b28:	4b04      	ldr	r3, [pc, #16]	; (b3c <analogWriteDAC0+0x38>)
     b2a:	22c0      	movs	r2, #192	; 0xc0
     b2c:	f883 2021 	strb.w	r2, [r3, #33]	; 0x21
     b30:	e7f4      	b.n	b1c <analogWriteDAC0+0x18>
     b32:	bf00      	nop
     b34:	40048000 	.word	0x40048000
     b38:	1fff0d82 	.word	0x1fff0d82
     b3c:	400cc000 	.word	0x400cc000

00000b40 <analogWriteDAC1>:


#if defined(__MK64FX512__) || defined(__MK66FX1M0__)
void analogWriteDAC1(int val)
{
	SIM_SCGC2 |= SIM_SCGC2_DAC1;
     b40:	4a0b      	ldr	r2, [pc, #44]	; (b70 <analogWriteDAC1+0x30>)
	if (analog_reference_internal) {
     b42:	490c      	ldr	r1, [pc, #48]	; (b74 <analogWriteDAC1+0x34>)
	SIM_SCGC2 |= SIM_SCGC2_DAC1;
     b44:	6ad3      	ldr	r3, [r2, #44]	; 0x2c
     b46:	f443 5300 	orr.w	r3, r3, #8192	; 0x2000
     b4a:	62d3      	str	r3, [r2, #44]	; 0x2c
	if (analog_reference_internal) {
     b4c:	780b      	ldrb	r3, [r1, #0]
     b4e:	b14b      	cbz	r3, b64 <analogWriteDAC1+0x24>
		DAC1_C0 = DAC_C0_DACEN;  // 1.2V ref is DACREF_1
     b50:	4b09      	ldr	r3, [pc, #36]	; (b78 <analogWriteDAC1+0x38>)
     b52:	2280      	movs	r2, #128	; 0x80
     b54:	f883 2021 	strb.w	r2, [r3, #33]	; 0x21
	} else {
		DAC1_C0 = DAC_C0_DACEN | DAC_C0_DACRFS; // 3.3V VDDA is DACREF_2
	}
	__asm__ ("usat    %[value], #12, %[value]\n\t" : [value] "+r" (val));  // 0 <= val <= 4095

	*(volatile aliased_int16_t *)&(DAC1_DAT0L) = val;
     b58:	4b07      	ldr	r3, [pc, #28]	; (b78 <analogWriteDAC1+0x38>)
	__asm__ ("usat    %[value], #12, %[value]\n\t" : [value] "+r" (val));  // 0 <= val <= 4095
     b5a:	f380 000c 	usat	r0, #12, r0
	*(volatile aliased_int16_t *)&(DAC1_DAT0L) = val;
     b5e:	b200      	sxth	r0, r0
     b60:	8018      	strh	r0, [r3, #0]
}
     b62:	4770      	bx	lr
		DAC1_C0 = DAC_C0_DACEN | DAC_C0_DACRFS; // 3.3V VDDA is DACREF_2
     b64:	4b04      	ldr	r3, [pc, #16]	; (b78 <analogWriteDAC1+0x38>)
     b66:	22c0      	movs	r2, #192	; 0xc0
     b68:	f883 2021 	strb.w	r2, [r3, #33]	; 0x21
     b6c:	e7f4      	b.n	b58 <analogWriteDAC1+0x18>
     b6e:	bf00      	nop
     b70:	40048000 	.word	0x40048000
     b74:	1fff0d82 	.word	0x1fff0d82
     b78:	400cd000 	.word	0x400cd000

00000b7c <main>:
 */

#include <Arduino.h>

extern "C" int main(void)
{
     b7c:	b508      	push	{r3, lr}
	}


#else
	// Arduino's main() function just calls setup() and loop()....
	setup();
     b7e:	f7ff fc75 	bl	46c <setup>
	while (1) {
		loop();
     b82:	f7ff fdc3 	bl	70c <loop>
		yield();
     b86:	f001 fcc5 	bl	2514 <yield>
	while (1) {
     b8a:	e7fa      	b.n	b82 <main+0x6>

00000b8c <memcpy>:
#ifdef __ARM_FEATURE_UNALIGNED
	/* In case of UNALIGNED access supported, ip is not used in
	   function body.  */
	mov	ip, r0
#else
	push	{r0}
     b8c:	b401      	push	{r0}
#endif
	orr	r3, r1, r0
     b8e:	ea41 0300 	orr.w	r3, r1, r0
	ands	r3, r3, #3
     b92:	f013 0303 	ands.w	r3, r3, #3
	bne	.Lmisaligned_copy
     b96:	d16f      	bne.n	c78 <memcpy+0xec>

.Lbig_block:
	subs	r2, __OPT_BIG_BLOCK_SIZE
     b98:	3a40      	subs	r2, #64	; 0x40
	blo	.Lmid_block
     b9a:	d341      	bcc.n	c20 <memcpy+0x94>
.Lbig_block_loop:
	BEGIN_UNROLL_BIG_BLOCK
#ifdef __ARM_ARCH_7EM__
	ldr	r3, [r1], #4
	str	r3, [r0], #4
	END_UNROLL
     b9c:	f851 3b04 	ldr.w	r3, [r1], #4
     ba0:	f840 3b04 	str.w	r3, [r0], #4
     ba4:	f851 3b04 	ldr.w	r3, [r1], #4
     ba8:	f840 3b04 	str.w	r3, [r0], #4
     bac:	f851 3b04 	ldr.w	r3, [r1], #4
     bb0:	f840 3b04 	str.w	r3, [r0], #4
     bb4:	f851 3b04 	ldr.w	r3, [r1], #4
     bb8:	f840 3b04 	str.w	r3, [r0], #4
     bbc:	f851 3b04 	ldr.w	r3, [r1], #4
     bc0:	f840 3b04 	str.w	r3, [r0], #4
     bc4:	f851 3b04 	ldr.w	r3, [r1], #4
     bc8:	f840 3b04 	str.w	r3, [r0], #4
     bcc:	f851 3b04 	ldr.w	r3, [r1], #4
     bd0:	f840 3b04 	str.w	r3, [r0], #4
     bd4:	f851 3b04 	ldr.w	r3, [r1], #4
     bd8:	f840 3b04 	str.w	r3, [r0], #4
     bdc:	f851 3b04 	ldr.w	r3, [r1], #4
     be0:	f840 3b04 	str.w	r3, [r0], #4
     be4:	f851 3b04 	ldr.w	r3, [r1], #4
     be8:	f840 3b04 	str.w	r3, [r0], #4
     bec:	f851 3b04 	ldr.w	r3, [r1], #4
     bf0:	f840 3b04 	str.w	r3, [r0], #4
     bf4:	f851 3b04 	ldr.w	r3, [r1], #4
     bf8:	f840 3b04 	str.w	r3, [r0], #4
     bfc:	f851 3b04 	ldr.w	r3, [r1], #4
     c00:	f840 3b04 	str.w	r3, [r0], #4
     c04:	f851 3b04 	ldr.w	r3, [r1], #4
     c08:	f840 3b04 	str.w	r3, [r0], #4
     c0c:	f851 3b04 	ldr.w	r3, [r1], #4
     c10:	f840 3b04 	str.w	r3, [r0], #4
     c14:	f851 3b04 	ldr.w	r3, [r1], #4
     c18:	f840 3b04 	str.w	r3, [r0], #4
	str	r3, [r0, \offset]
	END_UNROLL
	adds	r0, __OPT_BIG_BLOCK_SIZE
	adds	r1, __OPT_BIG_BLOCK_SIZE
#endif
	subs	r2, __OPT_BIG_BLOCK_SIZE
     c1c:	3a40      	subs	r2, #64	; 0x40
	bhs .Lbig_block_loop
     c1e:	d2bd      	bcs.n	b9c <memcpy+0x10>

.Lmid_block:
	adds	r2, __OPT_BIG_BLOCK_SIZE - __OPT_MID_BLOCK_SIZE
     c20:	3230      	adds	r2, #48	; 0x30
	blo	.Lcopy_word_by_word
     c22:	d311      	bcc.n	c48 <memcpy+0xbc>
.Lmid_block_loop:
	BEGIN_UNROLL_MID_BLOCK
#ifdef __ARM_ARCH_7EM__
	ldr	r3, [r1], #4
	str	r3, [r0], #4
	END_UNROLL
     c24:	f851 3b04 	ldr.w	r3, [r1], #4
     c28:	f840 3b04 	str.w	r3, [r0], #4
     c2c:	f851 3b04 	ldr.w	r3, [r1], #4
     c30:	f840 3b04 	str.w	r3, [r0], #4
     c34:	f851 3b04 	ldr.w	r3, [r1], #4
     c38:	f840 3b04 	str.w	r3, [r0], #4
     c3c:	f851 3b04 	ldr.w	r3, [r1], #4
     c40:	f840 3b04 	str.w	r3, [r0], #4
	str	r3, [r0, \offset]
	END_UNROLL
	adds    r0, __OPT_MID_BLOCK_SIZE
	adds    r1, __OPT_MID_BLOCK_SIZE
#endif
	subs	r2, __OPT_MID_BLOCK_SIZE
     c44:	3a10      	subs	r2, #16
	bhs	.Lmid_block_loop
     c46:	d2ed      	bcs.n	c24 <memcpy+0x98>

.Lcopy_word_by_word:
	adds	r2, __OPT_MID_BLOCK_SIZE - 4
     c48:	320c      	adds	r2, #12
	blo	.Lcopy_less_than_4
     c4a:	d305      	bcc.n	c58 <memcpy+0xcc>

	/* Kernel loop for small block copy */
	.align 2
.Lcopy_word_by_word_loop:
	ldr	r3, [r1], #4
     c4c:	f851 3b04 	ldr.w	r3, [r1], #4
	str	r3, [r0], #4
     c50:	f840 3b04 	str.w	r3, [r0], #4
	subs	r2, #4
     c54:	3a04      	subs	r2, #4
	bhs	.Lcopy_word_by_word_loop
     c56:	d2f9      	bcs.n	c4c <memcpy+0xc0>

.Lcopy_less_than_4:
	adds	r2, #4
     c58:	3204      	adds	r2, #4
	beq	.Ldone
     c5a:	d00a      	beq.n	c72 <memcpy+0xe6>

	lsls	r2, r2, #31
     c5c:	07d2      	lsls	r2, r2, #31
	itt ne
     c5e:	bf1c      	itt	ne
	ldrbne  r3, [r1], #1
     c60:	f811 3b01 	ldrbne.w	r3, [r1], #1
	strbne  r3, [r0], #1
     c64:	f800 3b01 	strbne.w	r3, [r0], #1

	bcc	.Ldone
     c68:	d303      	bcc.n	c72 <memcpy+0xe6>
#ifdef __ARM_FEATURE_UNALIGNED
	ldrh	r3, [r1]
	strh	r3, [r0]
#else
	ldrb	r3, [r1]
     c6a:	780b      	ldrb	r3, [r1, #0]
	strb	r3, [r0]
     c6c:	7003      	strb	r3, [r0, #0]
	ldrb	r3, [r1, #1]
     c6e:	784b      	ldrb	r3, [r1, #1]
	strb	r3, [r0, #1]
     c70:	7043      	strb	r3, [r0, #1]

.Ldone:
#ifdef __ARM_FEATURE_UNALIGNED
	mov	r0, ip
#else
	pop	{r0}
     c72:	bc01      	pop	{r0}
#endif
	bx	lr
     c74:	4770      	bx	lr
     c76:	bf00      	nop
	beq	.Ldst_aligned
#else
	/* if len < 12, misalignment adjustment has more overhead than
	just byte-to-byte copy.  Also, len must >=8 to guarantee code
	afterward work correctly.  */
	cmp	r2, #12
     c78:	2a0c      	cmp	r2, #12
	blo	.Lbyte_copy
     c7a:	d34a      	bcc.n	d12 <memcpy+0x186>
	handling of aligned src and misaligned dst need more overhead than
	otherwise.  By doing this the worst case is when initial src is aligned,
	additional up to 4 byte additional copy will executed, which is
	acceptable.  */

	ands	r3, r0, #3
     c7c:	f010 0303 	ands.w	r3, r0, #3
	beq	.Ldst_aligned
     c80:	d011      	beq.n	ca6 <memcpy+0x11a>

	rsb	r3, #4
     c82:	f1c3 0304 	rsb	r3, r3, #4
	subs	r2, r3
     c86:	1ad2      	subs	r2, r2, r3

	lsls    r3, r3, #31
     c88:	07db      	lsls	r3, r3, #31
	itt ne
     c8a:	bf1c      	itt	ne
	ldrbne  r3, [r1], #1
     c8c:	f811 3b01 	ldrbne.w	r3, [r1], #1
	strbne  r3, [r0], #1
     c90:	f800 3b01 	strbne.w	r3, [r0], #1

	bcc .Ldst_aligned
     c94:	d307      	bcc.n	ca6 <memcpy+0x11a>
#ifdef __ARM_FEATURE_UNALIGNED
	ldrh    r3, [r1], #2
	strh    r3, [r0], #2
	b	.Ldst_aligned
#else
	ldrb    r3, [r1], #1
     c96:	f811 3b01 	ldrb.w	r3, [r1], #1
	strb    r3, [r0], #1
     c9a:	f800 3b01 	strb.w	r3, [r0], #1
	ldrb    r3, [r1], #1
     c9e:	f811 3b01 	ldrb.w	r3, [r1], #1
	strb    r3, [r0], #1
     ca2:	f800 3b01 	strb.w	r3, [r0], #1
	/* Now that dst is aligned */
.Ldst_aligned:
	/* if r1 is aligned now, it means r0/r1 has the same misalignment,
	and they are both aligned now.  Go aligned copy.  */
	ands	r3, r1, #3
     ca6:	f011 0303 	ands.w	r3, r1, #3
	beq	.Lbig_block
     caa:	f43f af75 	beq.w	b98 <memcpy+0xc>

	/* dst is aligned, but src isn't.  Misaligned copy.  */

	push	{r4, r5}
     cae:	b430      	push	{r4, r5}
	subs	r2, #4
     cb0:	3a04      	subs	r2, #4

	/* Backward r1 by misaligned bytes, to make r1 aligned.
	Since we need to restore r1 to unaligned address after the loop,
	we need keep the offset bytes to ip and sub it from r1 afterward.  */
	subs	r1, r3
     cb2:	1ac9      	subs	r1, r1, r3
	rsb	ip, r3, #4
     cb4:	f1c3 0c04 	rsb	ip, r3, #4

	/* Pre-load on word */
	ldr	r4, [r1], #4
     cb8:	f851 4b04 	ldr.w	r4, [r1], #4

	cmp	r3, #2
     cbc:	2b02      	cmp	r3, #2
	beq	.Lmisaligned_copy_2_2
     cbe:	d019      	beq.n	cf4 <memcpy+0x168>
	cmp	r3, #3
     cc0:	2b03      	cmp	r3, #3
	beq	.Lmisaligned_copy_3_1
     cc2:	d00b      	beq.n	cdc <memcpy+0x150>
	subs	r2, #4
	bhs	1b
	.endm

.Lmisaligned_copy_1_3:
	mis_src_copy shift=8
     cc4:	0a24      	lsrs	r4, r4, #8
     cc6:	f851 3b04 	ldr.w	r3, [r1], #4
     cca:	061d      	lsls	r5, r3, #24
     ccc:	ea44 0405 	orr.w	r4, r4, r5
     cd0:	f840 4b04 	str.w	r4, [r0], #4
     cd4:	461c      	mov	r4, r3
     cd6:	3a04      	subs	r2, #4
     cd8:	d2f4      	bcs.n	cc4 <memcpy+0x138>
	b	.Lsrc_misaligned_tail
     cda:	e016      	b.n	d0a <memcpy+0x17e>

.Lmisaligned_copy_3_1:
	mis_src_copy shift=24
     cdc:	0e24      	lsrs	r4, r4, #24
     cde:	f851 3b04 	ldr.w	r3, [r1], #4
     ce2:	021d      	lsls	r5, r3, #8
     ce4:	ea44 0405 	orr.w	r4, r4, r5
     ce8:	f840 4b04 	str.w	r4, [r0], #4
     cec:	461c      	mov	r4, r3
     cee:	3a04      	subs	r2, #4
     cf0:	d2f4      	bcs.n	cdc <memcpy+0x150>
	b	.Lsrc_misaligned_tail
     cf2:	e00a      	b.n	d0a <memcpy+0x17e>

.Lmisaligned_copy_2_2:
	/* For 2_2 misalignment, ldr is still faster than 2 x ldrh.  */
	mis_src_copy shift=16
     cf4:	0c24      	lsrs	r4, r4, #16
     cf6:	f851 3b04 	ldr.w	r3, [r1], #4
     cfa:	041d      	lsls	r5, r3, #16
     cfc:	ea44 0405 	orr.w	r4, r4, r5
     d00:	f840 4b04 	str.w	r4, [r0], #4
     d04:	461c      	mov	r4, r3
     d06:	3a04      	subs	r2, #4
     d08:	d2f4      	bcs.n	cf4 <memcpy+0x168>

.Lsrc_misaligned_tail:
	adds	r2, #4
     d0a:	3204      	adds	r2, #4
	subs	r1, ip
     d0c:	ebb1 010c 	subs.w	r1, r1, ip
	pop	{r4, r5}
     d10:	bc30      	pop	{r4, r5}

#endif /* __ARM_FEATURE_UNALIGNED */

.Lbyte_copy:
	subs	r2, #4
     d12:	3a04      	subs	r2, #4
	blo	.Lcopy_less_than_4
     d14:	d3a0      	bcc.n	c58 <memcpy+0xcc>

.Lbyte_copy_loop:
	subs    r2, #1
     d16:	3a01      	subs	r2, #1
	ldrb    r3, [r1], #1
     d18:	f811 3b01 	ldrb.w	r3, [r1], #1
	strb    r3, [r0], #1
     d1c:	f800 3b01 	strb.w	r3, [r0], #1
	bhs	.Lbyte_copy_loop
     d20:	d2f9      	bcs.n	d16 <memcpy+0x18a>

	ldrb	r3, [r1]
     d22:	780b      	ldrb	r3, [r1, #0]
	strb	r3, [r0]
     d24:	7003      	strb	r3, [r0, #0]
	ldrb	r3, [r1, #1]
     d26:	784b      	ldrb	r3, [r1, #1]
	strb	r3, [r0, #1]
     d28:	7043      	strb	r3, [r0, #1]
	ldrb	r3, [r1, #2]
     d2a:	788b      	ldrb	r3, [r1, #2]
	strb	r3, [r0, #2]
     d2c:	7083      	strb	r3, [r0, #2]

#ifdef __ARM_FEATURE_UNALIGNED
	mov	r0, ip
#else
	pop	{r0}
     d2e:	bc01      	pop	{r0}
#endif
	bx	lr
     d30:	4770      	bx	lr
     d32:	bf00      	nop

00000d34 <fault_isr>:
{
     d34:	b508      	push	{r3, lr}
		if (SIM_SCGC4 & SIM_SCGC4_USBOTG) usb_isr();
     d36:	4c0f      	ldr	r4, [pc, #60]	; (d74 <fault_isr+0x40>)
     d38:	6b63      	ldr	r3, [r4, #52]	; 0x34
     d3a:	0358      	lsls	r0, r3, #13
     d3c:	d40d      	bmi.n	d5a <fault_isr+0x26>
		if (SIM_SCGC4 & SIM_SCGC4_UART0) uart0_status_isr();
     d3e:	6b63      	ldr	r3, [r4, #52]	; 0x34
     d40:	0559      	lsls	r1, r3, #21
     d42:	d40f      	bmi.n	d64 <fault_isr+0x30>
		if (SIM_SCGC4 & SIM_SCGC4_UART1) uart1_status_isr();
     d44:	6b63      	ldr	r3, [r4, #52]	; 0x34
     d46:	051a      	lsls	r2, r3, #20
     d48:	d411      	bmi.n	d6e <fault_isr+0x3a>
		if (SIM_SCGC4 & SIM_SCGC4_UART2) uart2_status_isr();
     d4a:	6b63      	ldr	r3, [r4, #52]	; 0x34
     d4c:	04db      	lsls	r3, r3, #19
     d4e:	d5f3      	bpl.n	d38 <fault_isr+0x4>
     d50:	f000 f812 	bl	d78 <unused_isr>
		if (SIM_SCGC4 & SIM_SCGC4_USBOTG) usb_isr();
     d54:	6b63      	ldr	r3, [r4, #52]	; 0x34
     d56:	0358      	lsls	r0, r3, #13
     d58:	d5f1      	bpl.n	d3e <fault_isr+0xa>
     d5a:	f000 fbaf 	bl	14bc <usb_isr>
		if (SIM_SCGC4 & SIM_SCGC4_UART0) uart0_status_isr();
     d5e:	6b63      	ldr	r3, [r4, #52]	; 0x34
     d60:	0559      	lsls	r1, r3, #21
     d62:	d5ef      	bpl.n	d44 <fault_isr+0x10>
     d64:	f000 f808 	bl	d78 <unused_isr>
		if (SIM_SCGC4 & SIM_SCGC4_UART1) uart1_status_isr();
     d68:	6b63      	ldr	r3, [r4, #52]	; 0x34
     d6a:	051a      	lsls	r2, r3, #20
     d6c:	d5ed      	bpl.n	d4a <fault_isr+0x16>
     d6e:	f000 f803 	bl	d78 <unused_isr>
     d72:	e7ea      	b.n	d4a <fault_isr+0x16>
     d74:	40048000 	.word	0x40048000

00000d78 <unused_isr>:
{
     d78:	b508      	push	{r3, lr}
	fault_isr();
     d7a:	f7ff ffdb 	bl	d34 <fault_isr>
     d7e:	bf00      	nop

00000d80 <startup_early_hook>:
	WDOG_STCTRLH = WDOG_STCTRLH_ALLOWUPDATE;
     d80:	4b01      	ldr	r3, [pc, #4]	; (d88 <startup_early_hook+0x8>)
     d82:	2210      	movs	r2, #16
     d84:	801a      	strh	r2, [r3, #0]
}
     d86:	4770      	bx	lr
     d88:	40052000 	.word	0x40052000

00000d8c <startup_late_hook>:
static void startup_default_late_hook(void) {}
     d8c:	4770      	bx	lr
     d8e:	bf00      	nop

00000d90 <ultoa>:


char * ultoa(unsigned long val, char *buf, int radix)
{
	unsigned digit;
	int i=0, j;
     d90:	f101 3cff 	add.w	ip, r1, #4294967295
{
     d94:	b570      	push	{r4, r5, r6, lr}
	char t;

	while (1) {
		digit = val % radix;
     d96:	4665      	mov	r5, ip
	int i=0, j;
     d98:	2400      	movs	r4, #0
     d9a:	e001      	b.n	da0 <ultoa+0x10>
		buf[i] = ((digit < 10) ? '0' + digit : 'A' + digit - 10);
		val /= radix;
     d9c:	4670      	mov	r0, lr
		if (val == 0) break;
		i++;
     d9e:	3401      	adds	r4, #1
		digit = val % radix;
     da0:	fbb0 fef2 	udiv	lr, r0, r2
     da4:	fb02 031e 	mls	r3, r2, lr, r0
		buf[i] = ((digit < 10) ? '0' + digit : 'A' + digit - 10);
     da8:	2b09      	cmp	r3, #9
     daa:	f103 0630 	add.w	r6, r3, #48	; 0x30
     dae:	bf8a      	itet	hi
     db0:	3337      	addhi	r3, #55	; 0x37
     db2:	b2f3      	uxtbls	r3, r6
     db4:	b2db      	uxtbhi	r3, r3
		if (val == 0) break;
     db6:	4290      	cmp	r0, r2
		buf[i] = ((digit < 10) ? '0' + digit : 'A' + digit - 10);
     db8:	f805 3f01 	strb.w	r3, [r5, #1]!
		if (val == 0) break;
     dbc:	d2ee      	bcs.n	d9c <ultoa+0xc>
	}
	buf[i + 1] = 0;
     dbe:	1c63      	adds	r3, r4, #1
     dc0:	2200      	movs	r2, #0
     dc2:	54ca      	strb	r2, [r1, r3]
     dc4:	18c8      	adds	r0, r1, r3
	for (j=0; j < i; j++, i--) {
     dc6:	b17c      	cbz	r4, de8 <ultoa+0x58>
		t = buf[j];
     dc8:	4662      	mov	r2, ip
     dca:	f81c 5f01 	ldrb.w	r5, [ip, #1]!
		buf[j] = buf[i];
     dce:	f810 ed01 	ldrb.w	lr, [r0, #-1]!
     dd2:	f88c e000 	strb.w	lr, [ip]
	for (j=0; j < i; j++, i--) {
     dd6:	eba1 030c 	sub.w	r3, r1, ip
     dda:	3202      	adds	r2, #2
     ddc:	3b01      	subs	r3, #1
     dde:	1a52      	subs	r2, r2, r1
     de0:	4423      	add	r3, r4
     de2:	429a      	cmp	r2, r3
		buf[i] = t;
     de4:	7005      	strb	r5, [r0, #0]
	for (j=0; j < i; j++, i--) {
     de6:	dbef      	blt.n	dc8 <ultoa+0x38>
	}
	return buf;
}
     de8:	4608      	mov	r0, r1
     dea:	bd70      	pop	{r4, r5, r6, pc}

00000dec <digitalWrite.part.0>:



// TODO: startup code needs to initialize all pins to GPIO mode, input by default

void digitalWrite(uint8_t pin, uint8_t val)
     dec:	b410      	push	{r4}
{
	if (pin >= CORE_NUM_DIGITAL) return;
#ifdef KINETISK
	if (*portModeRegister(pin)) {
     dee:	4b11      	ldr	r3, [pc, #68]	; (e34 <digitalWrite.part.0+0x48>)
     df0:	f853 2030 	ldr.w	r2, [r3, r0, lsl #3]
     df4:	f892 4280 	ldrb.w	r4, [r2, #640]	; 0x280
     df8:	b134      	cbz	r4, e08 <digitalWrite.part.0+0x1c>
		if (val) {
			*portSetRegister(pin) = 1;
     dfa:	2301      	movs	r3, #1
		if (val) {
     dfc:	b1a9      	cbz	r1, e2a <digitalWrite.part.0+0x3e>
			*portSetRegister(pin) = 1;
     dfe:	f882 3080 	strb.w	r3, [r2, #128]	; 0x80
			*config &= ~(PORT_PCR_PE);
			//*config = PORT_PCR_MUX(1);
		}
	}

}
     e02:	f85d 4b04 	ldr.w	r4, [sp], #4
     e06:	4770      	bx	lr
		volatile uint32_t *config = portConfigRegister(pin);
     e08:	eb03 03c0 	add.w	r3, r3, r0, lsl #3
     e0c:	685b      	ldr	r3, [r3, #4]
			*config |= (PORT_PCR_PE | PORT_PCR_PS);
     e0e:	681a      	ldr	r2, [r3, #0]
		if (val) {
     e10:	b929      	cbnz	r1, e1e <digitalWrite.part.0+0x32>
			*config &= ~(PORT_PCR_PE);
     e12:	f022 0202 	bic.w	r2, r2, #2
}
     e16:	f85d 4b04 	ldr.w	r4, [sp], #4
			*config &= ~(PORT_PCR_PE);
     e1a:	601a      	str	r2, [r3, #0]
}
     e1c:	4770      	bx	lr
			*config |= (PORT_PCR_PE | PORT_PCR_PS);
     e1e:	f042 0203 	orr.w	r2, r2, #3
}
     e22:	f85d 4b04 	ldr.w	r4, [sp], #4
			*config |= (PORT_PCR_PE | PORT_PCR_PS);
     e26:	601a      	str	r2, [r3, #0]
}
     e28:	4770      	bx	lr
			*portClearRegister(pin) = 1;
     e2a:	f882 3100 	strb.w	r3, [r2, #256]	; 0x100
}
     e2e:	f85d 4b04 	ldr.w	r4, [sp], #4
     e32:	4770      	bx	lr
     e34:	00002984 	.word	0x00002984

00000e38 <rtc_set>:
	RTC_SR = 0;
     e38:	4b03      	ldr	r3, [pc, #12]	; (e48 <rtc_set+0x10>)
     e3a:	2200      	movs	r2, #0
	RTC_SR = RTC_SR_TCE;
     e3c:	2110      	movs	r1, #16
	RTC_SR = 0;
     e3e:	615a      	str	r2, [r3, #20]
	RTC_TPR = 0;
     e40:	605a      	str	r2, [r3, #4]
	RTC_TSR = t;
     e42:	6018      	str	r0, [r3, #0]
	RTC_SR = RTC_SR_TCE;
     e44:	6159      	str	r1, [r3, #20]
}
     e46:	4770      	bx	lr
     e48:	4003d000 	.word	0x4003d000

00000e4c <startup_middle_hook>:
static void startup_default_middle_hook(void) {}
     e4c:	4770      	bx	lr
     e4e:	bf00      	nop

00000e50 <_init_Teensyduino_internal_>:
	NVIC_ENABLE_IRQ(IRQ_PORTA);
     e50:	f04f 2ce0 	mov.w	ip, #3758153728	; 0xe000e000
{
     e54:	b5f0      	push	{r4, r5, r6, r7, lr}
	NVIC_ENABLE_IRQ(IRQ_PORTA);
     e56:	f04f 6e00 	mov.w	lr, #134217728	; 0x8000000
     e5a:	f8cc e104 	str.w	lr, [ip, #260]	; 0x104
	NVIC_ENABLE_IRQ(IRQ_PORTB);
     e5e:	f04f 5e80 	mov.w	lr, #268435456	; 0x10000000
     e62:	f8cc e104 	str.w	lr, [ip, #260]	; 0x104
	NVIC_ENABLE_IRQ(IRQ_PORTC);
     e66:	f04f 5e00 	mov.w	lr, #536870912	; 0x20000000
     e6a:	f8cc e104 	str.w	lr, [ip, #260]	; 0x104
	FTM3_C0SC = 0x28;
     e6e:	4a24      	ldr	r2, [pc, #144]	; (f00 <_init_Teensyduino_internal_+0xb0>)
	FTM0_CNT = 0;
     e70:	4924      	ldr	r1, [pc, #144]	; (f04 <_init_Teensyduino_internal_+0xb4>)
	FTM2_CNT = 0;
     e72:	4825      	ldr	r0, [pc, #148]	; (f08 <_init_Teensyduino_internal_+0xb8>)
	FTM1_CNT = 0;
     e74:	4c25      	ldr	r4, [pc, #148]	; (f0c <_init_Teensyduino_internal_+0xbc>)
	NVIC_ENABLE_IRQ(IRQ_PORTD);
     e76:	f04f 4e80 	mov.w	lr, #1073741824	; 0x40000000
     e7a:	f8cc e104 	str.w	lr, [ip, #260]	; 0x104
	FTM0_C0SC = 0x28; // MSnB:MSnA = 10, ELSnB:ELSnA = 10
     e7e:	2328      	movs	r3, #40	; 0x28
	FTM0_CNT = 0;
     e80:	2700      	movs	r7, #0
	FTM0_MOD = DEFAULT_FTM_MOD;
     e82:	f64e 76ff 	movw	r6, #61439	; 0xefff
	FTM0_SC = FTM_SC_CLKS(1) | FTM_SC_PS(DEFAULT_FTM_PRESCALE);
     e86:	2509      	movs	r5, #9
	NVIC_ENABLE_IRQ(IRQ_PORTE);
     e88:	f04f 4e00 	mov.w	lr, #2147483648	; 0x80000000
     e8c:	f8cc e104 	str.w	lr, [ip, #260]	; 0x104
{
     e90:	b083      	sub	sp, #12
	FTM0_CNT = 0;
     e92:	604f      	str	r7, [r1, #4]
	FTM0_MOD = DEFAULT_FTM_MOD;
     e94:	608e      	str	r6, [r1, #8]
	FTM0_C0SC = 0x28; // MSnB:MSnA = 10, ELSnB:ELSnA = 10
     e96:	60cb      	str	r3, [r1, #12]
	FTM0_C1SC = 0x28;
     e98:	614b      	str	r3, [r1, #20]
	FTM0_C2SC = 0x28;
     e9a:	61cb      	str	r3, [r1, #28]
	FTM0_C3SC = 0x28;
     e9c:	624b      	str	r3, [r1, #36]	; 0x24
	FTM0_C4SC = 0x28;
     e9e:	62cb      	str	r3, [r1, #44]	; 0x2c
	FTM0_C5SC = 0x28;
     ea0:	634b      	str	r3, [r1, #52]	; 0x34
	FTM0_C6SC = 0x28;
     ea2:	63cb      	str	r3, [r1, #60]	; 0x3c
	FTM0_C7SC = 0x28;
     ea4:	644b      	str	r3, [r1, #68]	; 0x44
	FTM3_C0SC = 0x28;
     ea6:	60d3      	str	r3, [r2, #12]
	FTM3_C1SC = 0x28;
     ea8:	6153      	str	r3, [r2, #20]
	FTM3_C2SC = 0x28;
     eaa:	61d3      	str	r3, [r2, #28]
	FTM3_C3SC = 0x28;
     eac:	6253      	str	r3, [r2, #36]	; 0x24
	FTM3_C4SC = 0x28;
     eae:	62d3      	str	r3, [r2, #44]	; 0x2c
	FTM3_C5SC = 0x28;
     eb0:	6353      	str	r3, [r2, #52]	; 0x34
	FTM3_C6SC = 0x28;
     eb2:	63d3      	str	r3, [r2, #60]	; 0x3c
	FTM3_C7SC = 0x28;
     eb4:	6453      	str	r3, [r2, #68]	; 0x44
	FTM0_SC = FTM_SC_CLKS(1) | FTM_SC_PS(DEFAULT_FTM_PRESCALE);
     eb6:	600d      	str	r5, [r1, #0]
	FTM1_CNT = 0;
     eb8:	6067      	str	r7, [r4, #4]
	FTM1_MOD = DEFAULT_FTM_MOD;
     eba:	60a6      	str	r6, [r4, #8]
	FTM1_C0SC = 0x28;
     ebc:	60e3      	str	r3, [r4, #12]
	FTM1_C1SC = 0x28;
     ebe:	6163      	str	r3, [r4, #20]
	FTM1_SC = FTM_SC_CLKS(1) | FTM_SC_PS(DEFAULT_FTM_PRESCALE);
     ec0:	6025      	str	r5, [r4, #0]
	FTM2_CNT = 0;
     ec2:	6047      	str	r7, [r0, #4]
	FTM2_MOD = DEFAULT_FTM_MOD;
     ec4:	6086      	str	r6, [r0, #8]
	FTM2_C0SC = 0x28;
     ec6:	60c3      	str	r3, [r0, #12]
	FTM2_C1SC = 0x28;
     ec8:	6143      	str	r3, [r0, #20]
	FTM2_SC = FTM_SC_CLKS(1) | FTM_SC_PS(DEFAULT_FTM_PRESCALE);
     eca:	6005      	str	r5, [r0, #0]
	FTM3_CNT = 0;
     ecc:	6057      	str	r7, [r2, #4]
	FTM3_MOD = DEFAULT_FTM_MOD;
     ece:	6096      	str	r6, [r2, #8]
	FTM3_C0SC = 0x28;
     ed0:	60d3      	str	r3, [r2, #12]
	FTM3_C1SC = 0x28;
     ed2:	6153      	str	r3, [r2, #20]
	FTM3_SC = FTM_SC_CLKS(1) | FTM_SC_PS(DEFAULT_FTM_PRESCALE);
     ed4:	6015      	str	r5, [r2, #0]
	analog_init();
     ed6:	f7ff fd09 	bl	8ec <analog_init>
	startup_middle_hook();
     eda:	f7ff ffb7 	bl	e4c <startup_middle_hook>
	while (millis() < TEENSY_INIT_USB_DELAY_BEFORE) ; // wait
     ede:	4c0c      	ldr	r4, [pc, #48]	; (f10 <_init_Teensyduino_internal_+0xc0>)
	volatile uint32_t ret = systick_millis_count; // single aligned 32 bit is atomic
     ee0:	6823      	ldr	r3, [r4, #0]
     ee2:	9300      	str	r3, [sp, #0]
	return ret;
     ee4:	9b00      	ldr	r3, [sp, #0]
     ee6:	2b13      	cmp	r3, #19
     ee8:	d9fa      	bls.n	ee0 <_init_Teensyduino_internal_+0x90>
	usb_init();
     eea:	f000 fea3 	bl	1c34 <usb_init>
	volatile uint32_t ret = systick_millis_count; // single aligned 32 bit is atomic
     eee:	6823      	ldr	r3, [r4, #0]
     ef0:	9301      	str	r3, [sp, #4]
	return ret;
     ef2:	9b01      	ldr	r3, [sp, #4]
	while (millis() < TEENSY_INIT_USB_DELAY_AFTER + TEENSY_INIT_USB_DELAY_BEFORE) ; // wait
     ef4:	f5b3 7f96 	cmp.w	r3, #300	; 0x12c
     ef8:	d3f9      	bcc.n	eee <_init_Teensyduino_internal_+0x9e>
}
     efa:	b003      	add	sp, #12
     efc:	bdf0      	pop	{r4, r5, r6, r7, pc}
     efe:	bf00      	nop
     f00:	400b9000 	.word	0x400b9000
     f04:	40038000 	.word	0x40038000
     f08:	400b8000 	.word	0x400b8000
     f0c:	40039000 	.word	0x40039000
     f10:	1fff0d88 	.word	0x1fff0d88

00000f14 <analogWrite>:
	if (pin == A21 || pin == A22) {
     f14:	f1a0 0242 	sub.w	r2, r0, #66	; 0x42
     f18:	2a01      	cmp	r2, #1
{
     f1a:	4603      	mov	r3, r0
	if (pin == A21 || pin == A22) {
     f1c:	d925      	bls.n	f6a <analogWrite+0x56>
{
     f1e:	b510      	push	{r4, lr}
	max = 1 << analog_write_res;
     f20:	4a93      	ldr	r2, [pc, #588]	; (1170 <analogWrite+0x25c>)
	if (val <= 0) {
     f22:	2900      	cmp	r1, #0
	max = 1 << analog_write_res;
     f24:	f892 e000 	ldrb.w	lr, [r2]
	if (val <= 0) {
     f28:	f340 808e 	ble.w	1048 <analogWrite+0x134>
	max = 1 << analog_write_res;
     f2c:	2201      	movs	r2, #1
     f2e:	fa02 f00e 	lsl.w	r0, r2, lr
	} else if (val >= max) {
     f32:	4281      	cmp	r1, r0
     f34:	d228      	bcs.n	f88 <analogWrite+0x74>
	if (pin == FTM1_CH0_PIN || pin == FTM1_CH1_PIN) {
     f36:	f1a3 0c02 	sub.w	ip, r3, #2
     f3a:	f1bc 0f24 	cmp.w	ip, #36	; 0x24
     f3e:	d83c      	bhi.n	fba <analogWrite+0xa6>
     f40:	e8df f00c 	tbb	[pc, ip]
     f44:	3b74746b 	.word	0x3b74746b
     f48:	3b6b6b3b 	.word	0x3b6b6b3b
     f4c:	3b3b3b3b 	.word	0x3b3b3b3b
     f50:	3b3b3b6b 	.word	0x3b3b3b6b
     f54:	3b3b3b3b 	.word	0x3b3b3b3b
     f58:	3b3b3b3b 	.word	0x3b3b3b3b
     f5c:	7b3b3b3b 	.word	0x7b3b3b3b
     f60:	3b3b3b7b 	.word	0x3b3b3b7b
     f64:	6b6b6b3b 	.word	0x6b6b6b3b
     f68:	6b          	.byte	0x6b
     f69:	00          	.byte	0x00
		uint8_t res = analog_write_res;
     f6a:	4a81      	ldr	r2, [pc, #516]	; (1170 <analogWrite+0x25c>)
     f6c:	7810      	ldrb	r0, [r2, #0]
		if (res < 12) {
     f6e:	280b      	cmp	r0, #11
			val <<= 12 - res;
     f70:	bf95      	itete	ls
     f72:	f1c0 000c 	rsbls	r0, r0, #12
			val >>= res - 12;
     f76:	380c      	subhi	r0, #12
			val <<= 12 - res;
     f78:	fa01 f000 	lslls.w	r0, r1, r0
			val >>= res - 12;
     f7c:	fa41 f000 	asrhi.w	r0, r1, r0
		if (pin == A21) analogWriteDAC0(val);
     f80:	2b42      	cmp	r3, #66	; 0x42
     f82:	d051      	beq.n	1028 <analogWrite+0x114>
		else analogWriteDAC1(val);
     f84:	f7ff bddc 	b.w	b40 <analogWriteDAC1>
	if (pin >= CORE_NUM_DIGITAL) return;
     f88:	2b3f      	cmp	r3, #63	; 0x3f
     f8a:	d815      	bhi.n	fb8 <analogWrite+0xa4>
	if (*portModeRegister(pin)) {
     f8c:	4979      	ldr	r1, [pc, #484]	; (1174 <analogWrite+0x260>)
     f8e:	f851 0033 	ldr.w	r0, [r1, r3, lsl #3]
     f92:	f890 4280 	ldrb.w	r4, [r0, #640]	; 0x280
		volatile uint32_t *config = portConfigRegister(pin);
     f96:	eb01 01c3 	add.w	r1, r1, r3, lsl #3
	if (*portModeRegister(pin)) {
     f9a:	2c00      	cmp	r4, #0
     f9c:	d073      	beq.n	1086 <analogWrite+0x172>
		volatile uint32_t *config = portConfigRegister(pin);
     f9e:	684b      	ldr	r3, [r1, #4]
			*portSetRegister(pin) = 1;
     fa0:	f880 2080 	strb.w	r2, [r0, #128]	; 0x80
#ifdef KINETISK
		*portModeRegister(pin) = 1;
#else
		*portModeRegister(pin) |= digitalPinToBitMask(pin); // TODO: atomic
#endif
		*config = PORT_PCR_SRE | PORT_PCR_DSE | PORT_PCR_MUX(1);
     fa4:	f44f 72a2 	mov.w	r2, #324	; 0x144
		*portModeRegister(pin) = 1;
     fa8:	2101      	movs	r1, #1
     faa:	f880 1280 	strb.w	r1, [r0, #640]	; 0x280
		*config = PORT_PCR_SRE | PORT_PCR_DSE | PORT_PCR_MUX(1);
     fae:	601a      	str	r2, [r3, #0]
		if (mode == OUTPUT_OPENDRAIN) {
		    *config |= PORT_PCR_ODE;
		} else {
		    *config &= ~PORT_PCR_ODE;
     fb0:	681a      	ldr	r2, [r3, #0]
     fb2:	f022 0220 	bic.w	r2, r2, #32
     fb6:	601a      	str	r2, [r3, #0]
}
     fb8:	bd10      	pop	{r4, pc}
		cval = ((uint32_t)val * (uint32_t)(FTM0_MOD + 1)) >> analog_write_res;
     fba:	4a6f      	ldr	r2, [pc, #444]	; (1178 <analogWrite+0x264>)
     fbc:	6892      	ldr	r2, [r2, #8]
     fbe:	fb02 1201 	mla	r2, r2, r1, r1
     fc2:	fa22 f20e 	lsr.w	r2, r2, lr
	switch (pin) {
     fc6:	f1bc 0f24 	cmp.w	ip, #36	; 0x24
     fca:	d862      	bhi.n	1092 <analogWrite+0x17e>
     fcc:	e8df f01c 	tbh	[pc, ip, lsl #1]
     fd0:	010c0113 	.word	0x010c0113
     fd4:	00fe0105 	.word	0x00fe0105
     fd8:	00f000f7 	.word	0x00f000f7
     fdc:	00e200e9 	.word	0x00e200e9
     fe0:	006100c8 	.word	0x006100c8
     fe4:	00610061 	.word	0x00610061
     fe8:	006100c1 	.word	0x006100c1
     fec:	00610061 	.word	0x00610061
     ff0:	00610061 	.word	0x00610061
     ff4:	00b300ba 	.word	0x00b300ba
     ff8:	00a500ac 	.word	0x00a500ac
     ffc:	00610061 	.word	0x00610061
    1000:	00610061 	.word	0x00610061
    1004:	009e0061 	.word	0x009e0061
    1008:	00610097 	.word	0x00610097
    100c:	00610061 	.word	0x00610061
    1010:	00900061 	.word	0x00900061
    1014:	00820089 	.word	0x00820089
    1018:	007b      	.short	0x007b
		cval = ((uint32_t)val * (uint32_t)(FTM3_MOD + 1)) >> analog_write_res;
    101a:	4a58      	ldr	r2, [pc, #352]	; (117c <analogWrite+0x268>)
    101c:	6892      	ldr	r2, [r2, #8]
    101e:	fb02 1201 	mla	r2, r2, r1, r1
    1022:	fa22 f20e 	lsr.w	r2, r2, lr
    1026:	e7ce      	b.n	fc6 <analogWrite+0xb2>
		if (pin == A21) analogWriteDAC0(val);
    1028:	f7ff bd6c 	b.w	b04 <analogWriteDAC0>
		cval = ((uint32_t)val * (uint32_t)(FTM1_MOD + 1)) >> analog_write_res;
    102c:	4a54      	ldr	r2, [pc, #336]	; (1180 <analogWrite+0x26c>)
    102e:	6892      	ldr	r2, [r2, #8]
    1030:	fb02 1201 	mla	r2, r2, r1, r1
    1034:	fa22 f20e 	lsr.w	r2, r2, lr
    1038:	e7c5      	b.n	fc6 <analogWrite+0xb2>
		cval = ((uint32_t)val * (uint32_t)(FTM2_MOD + 1)) >> analog_write_res;
    103a:	4a52      	ldr	r2, [pc, #328]	; (1184 <analogWrite+0x270>)
    103c:	6892      	ldr	r2, [r2, #8]
    103e:	fb02 1201 	mla	r2, r2, r1, r1
    1042:	fa22 f20e 	lsr.w	r2, r2, lr
    1046:	e7be      	b.n	fc6 <analogWrite+0xb2>
	if (pin >= CORE_NUM_DIGITAL) return;
    1048:	283f      	cmp	r0, #63	; 0x3f
    104a:	d8b5      	bhi.n	fb8 <analogWrite+0xa4>
	if (*portModeRegister(pin)) {
    104c:	4a49      	ldr	r2, [pc, #292]	; (1174 <analogWrite+0x260>)
    104e:	f852 1030 	ldr.w	r1, [r2, r0, lsl #3]
    1052:	f891 0280 	ldrb.w	r0, [r1, #640]	; 0x280
		volatile uint32_t *config = portConfigRegister(pin);
    1056:	eb02 02c3 	add.w	r2, r2, r3, lsl #3
	if (*portModeRegister(pin)) {
    105a:	b170      	cbz	r0, 107a <analogWrite+0x166>
			*portClearRegister(pin) = 1;
    105c:	2001      	movs	r0, #1
		volatile uint32_t *config = portConfigRegister(pin);
    105e:	6853      	ldr	r3, [r2, #4]
			*portClearRegister(pin) = 1;
    1060:	f881 0100 	strb.w	r0, [r1, #256]	; 0x100
		*config = PORT_PCR_SRE | PORT_PCR_DSE | PORT_PCR_MUX(1);
    1064:	f44f 72a2 	mov.w	r2, #324	; 0x144
		*portModeRegister(pin) = 1;
    1068:	2001      	movs	r0, #1
    106a:	f881 0280 	strb.w	r0, [r1, #640]	; 0x280
		*config = PORT_PCR_SRE | PORT_PCR_DSE | PORT_PCR_MUX(1);
    106e:	601a      	str	r2, [r3, #0]
		    *config &= ~PORT_PCR_ODE;
    1070:	681a      	ldr	r2, [r3, #0]
    1072:	f022 0220 	bic.w	r2, r2, #32
    1076:	601a      	str	r2, [r3, #0]
}
    1078:	bd10      	pop	{r4, pc}
		volatile uint32_t *config = portConfigRegister(pin);
    107a:	6853      	ldr	r3, [r2, #4]
			*config &= ~(PORT_PCR_PE);
    107c:	681a      	ldr	r2, [r3, #0]
    107e:	f022 0202 	bic.w	r2, r2, #2
    1082:	601a      	str	r2, [r3, #0]
    1084:	e7ee      	b.n	1064 <analogWrite+0x150>
		volatile uint32_t *config = portConfigRegister(pin);
    1086:	684b      	ldr	r3, [r1, #4]
			*config |= (PORT_PCR_PE | PORT_PCR_PS);
    1088:	681a      	ldr	r2, [r3, #0]
    108a:	f042 0203 	orr.w	r2, r2, #3
    108e:	601a      	str	r2, [r3, #0]
    1090:	e788      	b.n	fa4 <analogWrite+0x90>
	if (pin >= CORE_NUM_DIGITAL) return;
    1092:	2b3f      	cmp	r3, #63	; 0x3f
    1094:	d890      	bhi.n	fb8 <analogWrite+0xa4>
    1096:	297f      	cmp	r1, #127	; 0x7f
    1098:	4618      	mov	r0, r3
    109a:	bfd4      	ite	le
    109c:	2100      	movle	r1, #0
    109e:	2101      	movgt	r1, #1
    10a0:	f7ff fea4 	bl	dec <digitalWrite.part.0>
	config = portConfigRegister(pin);
    10a4:	4b33      	ldr	r3, [pc, #204]	; (1174 <analogWrite+0x260>)
    10a6:	eb03 02c0 	add.w	r2, r3, r0, lsl #3
		*portModeRegister(pin) = 1;
    10aa:	f853 1030 	ldr.w	r1, [r3, r0, lsl #3]
	config = portConfigRegister(pin);
    10ae:	6853      	ldr	r3, [r2, #4]
		*portModeRegister(pin) = 1;
    10b0:	2401      	movs	r4, #1
		*config = PORT_PCR_SRE | PORT_PCR_DSE | PORT_PCR_MUX(1);
    10b2:	f44f 72a2 	mov.w	r2, #324	; 0x144
		*portModeRegister(pin) = 1;
    10b6:	f881 4280 	strb.w	r4, [r1, #640]	; 0x280
		*config = PORT_PCR_SRE | PORT_PCR_DSE | PORT_PCR_MUX(1);
    10ba:	601a      	str	r2, [r3, #0]
		    *config &= ~PORT_PCR_ODE;
    10bc:	681a      	ldr	r2, [r3, #0]
    10be:	f022 0220 	bic.w	r2, r2, #32
    10c2:	601a      	str	r2, [r3, #0]
}
    10c4:	bd10      	pop	{r4, pc}
		FTM3_C7V = cval;
    10c6:	482d      	ldr	r0, [pc, #180]	; (117c <analogWrite+0x268>)
		FTM_PINCFG(FTM3_CH7_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    10c8:	4b2f      	ldr	r3, [pc, #188]	; (1188 <analogWrite+0x274>)
		FTM3_C7V = cval;
    10ca:	6482      	str	r2, [r0, #72]	; 0x48
		FTM_PINCFG(FTM3_CH7_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    10cc:	f44f 7151 	mov.w	r1, #836	; 0x344
    10d0:	62d9      	str	r1, [r3, #44]	; 0x2c
}
    10d2:	bd10      	pop	{r4, pc}
		FTM3_C6V = cval;
    10d4:	4829      	ldr	r0, [pc, #164]	; (117c <analogWrite+0x268>)
		FTM_PINCFG(FTM3_CH6_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    10d6:	4b2c      	ldr	r3, [pc, #176]	; (1188 <analogWrite+0x274>)
		FTM3_C6V = cval;
    10d8:	6402      	str	r2, [r0, #64]	; 0x40
		FTM_PINCFG(FTM3_CH6_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    10da:	f44f 7151 	mov.w	r1, #836	; 0x344
    10de:	6299      	str	r1, [r3, #40]	; 0x28
}
    10e0:	bd10      	pop	{r4, pc}
		FTM3_C5V = cval;
    10e2:	4826      	ldr	r0, [pc, #152]	; (117c <analogWrite+0x268>)
		FTM_PINCFG(FTM3_CH5_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    10e4:	4b28      	ldr	r3, [pc, #160]	; (1188 <analogWrite+0x274>)
		FTM3_C5V = cval;
    10e6:	6382      	str	r2, [r0, #56]	; 0x38
		FTM_PINCFG(FTM3_CH5_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    10e8:	f44f 7151 	mov.w	r1, #836	; 0x344
    10ec:	6259      	str	r1, [r3, #36]	; 0x24
}
    10ee:	bd10      	pop	{r4, pc}
		FTM3_C4V = cval;
    10f0:	4822      	ldr	r0, [pc, #136]	; (117c <analogWrite+0x268>)
		FTM_PINCFG(FTM3_CH4_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    10f2:	4b25      	ldr	r3, [pc, #148]	; (1188 <analogWrite+0x274>)
		FTM3_C4V = cval;
    10f4:	6302      	str	r2, [r0, #48]	; 0x30
		FTM_PINCFG(FTM3_CH4_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    10f6:	f44f 7151 	mov.w	r1, #836	; 0x344
    10fa:	6219      	str	r1, [r3, #32]
}
    10fc:	bd10      	pop	{r4, pc}
		FTM2_C1V = cval;
    10fe:	4821      	ldr	r0, [pc, #132]	; (1184 <analogWrite+0x270>)
		FTM_PINCFG(FTM2_CH1_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    1100:	4b22      	ldr	r3, [pc, #136]	; (118c <analogWrite+0x278>)
		FTM2_C1V = cval;
    1102:	6182      	str	r2, [r0, #24]
		FTM_PINCFG(FTM2_CH1_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    1104:	f44f 7151 	mov.w	r1, #836	; 0x344
    1108:	64d9      	str	r1, [r3, #76]	; 0x4c
}
    110a:	bd10      	pop	{r4, pc}
		FTM2_C0V = cval;
    110c:	481d      	ldr	r0, [pc, #116]	; (1184 <analogWrite+0x270>)
		FTM_PINCFG(FTM2_CH0_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    110e:	4b1f      	ldr	r3, [pc, #124]	; (118c <analogWrite+0x278>)
		FTM2_C0V = cval;
    1110:	6102      	str	r2, [r0, #16]
		FTM_PINCFG(FTM2_CH0_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    1112:	f44f 7151 	mov.w	r1, #836	; 0x344
    1116:	6499      	str	r1, [r3, #72]	; 0x48
}
    1118:	bd10      	pop	{r4, pc}
		FTM0_C1V = cval;
    111a:	4817      	ldr	r0, [pc, #92]	; (1178 <analogWrite+0x264>)
		FTM_PINCFG(FTM0_CH1_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    111c:	4b1a      	ldr	r3, [pc, #104]	; (1188 <analogWrite+0x274>)
		FTM0_C1V = cval;
    111e:	6182      	str	r2, [r0, #24]
		FTM_PINCFG(FTM0_CH1_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    1120:	f240 4144 	movw	r1, #1092	; 0x444
    1124:	6099      	str	r1, [r3, #8]
}
    1126:	bd10      	pop	{r4, pc}
		FTM0_C0V = cval;
    1128:	4813      	ldr	r0, [pc, #76]	; (1178 <analogWrite+0x264>)
		FTM_PINCFG(FTM0_CH0_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    112a:	4b17      	ldr	r3, [pc, #92]	; (1188 <analogWrite+0x274>)
		FTM0_C0V = cval;
    112c:	6102      	str	r2, [r0, #16]
		FTM_PINCFG(FTM0_CH0_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    112e:	f240 4144 	movw	r1, #1092	; 0x444
    1132:	6059      	str	r1, [r3, #4]
}
    1134:	bd10      	pop	{r4, pc}
		FTM0_C6V = cval;
    1136:	4810      	ldr	r0, [pc, #64]	; (1178 <analogWrite+0x264>)
		FTM_PINCFG(FTM0_CH6_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    1138:	4b15      	ldr	r3, [pc, #84]	; (1190 <analogWrite+0x27c>)
		FTM0_C6V = cval;
    113a:	6402      	str	r2, [r0, #64]	; 0x40
		FTM_PINCFG(FTM0_CH6_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    113c:	f240 4144 	movw	r1, #1092	; 0x444
    1140:	6199      	str	r1, [r3, #24]
}
    1142:	bd10      	pop	{r4, pc}
		FTM0_C5V = cval;
    1144:	480c      	ldr	r0, [pc, #48]	; (1178 <analogWrite+0x264>)
		FTM_PINCFG(FTM0_CH5_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    1146:	4b12      	ldr	r3, [pc, #72]	; (1190 <analogWrite+0x27c>)
		FTM0_C5V = cval;
    1148:	6382      	str	r2, [r0, #56]	; 0x38
		FTM_PINCFG(FTM0_CH5_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    114a:	f240 4144 	movw	r1, #1092	; 0x444
    114e:	6159      	str	r1, [r3, #20]
}
    1150:	bd10      	pop	{r4, pc}
		FTM3_C1V = cval;
    1152:	480a      	ldr	r0, [pc, #40]	; (117c <analogWrite+0x268>)
		FTM_PINCFG(FTM3_CH1_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    1154:	4b0e      	ldr	r3, [pc, #56]	; (1190 <analogWrite+0x27c>)
		FTM3_C1V = cval;
    1156:	6182      	str	r2, [r0, #24]
		FTM_PINCFG(FTM3_CH1_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    1158:	f240 4144 	movw	r1, #1092	; 0x444
    115c:	6059      	str	r1, [r3, #4]
}
    115e:	bd10      	pop	{r4, pc}
		FTM0_C3V = cval;
    1160:	4805      	ldr	r0, [pc, #20]	; (1178 <analogWrite+0x264>)
		FTM_PINCFG(FTM0_CH3_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    1162:	4b09      	ldr	r3, [pc, #36]	; (1188 <analogWrite+0x274>)
		FTM0_C3V = cval;
    1164:	6282      	str	r2, [r0, #40]	; 0x28
		FTM_PINCFG(FTM0_CH3_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    1166:	f240 4144 	movw	r1, #1092	; 0x444
    116a:	6119      	str	r1, [r3, #16]
}
    116c:	bd10      	pop	{r4, pc}
    116e:	bf00      	nop
    1170:	1fff0c0a 	.word	0x1fff0c0a
    1174:	00002984 	.word	0x00002984
    1178:	40038000 	.word	0x40038000
    117c:	400b9000 	.word	0x400b9000
    1180:	40039000 	.word	0x40039000
    1184:	400b8000 	.word	0x400b8000
    1188:	4004b000 	.word	0x4004b000
    118c:	4004a000 	.word	0x4004a000
    1190:	4004c000 	.word	0x4004c000
		FTM0_C2V = cval;
    1194:	481b      	ldr	r0, [pc, #108]	; (1204 <analogWrite+0x2f0>)
		FTM_PINCFG(FTM0_CH2_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    1196:	4b1c      	ldr	r3, [pc, #112]	; (1208 <analogWrite+0x2f4>)
		FTM0_C2V = cval;
    1198:	6202      	str	r2, [r0, #32]
		FTM_PINCFG(FTM0_CH2_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    119a:	f240 4144 	movw	r1, #1092	; 0x444
    119e:	60d9      	str	r1, [r3, #12]
}
    11a0:	bd10      	pop	{r4, pc}
		FTM3_C3V = cval;
    11a2:	481a      	ldr	r0, [pc, #104]	; (120c <analogWrite+0x2f8>)
		FTM_PINCFG(FTM3_CH3_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    11a4:	4b1a      	ldr	r3, [pc, #104]	; (1210 <analogWrite+0x2fc>)
		FTM3_C3V = cval;
    11a6:	6282      	str	r2, [r0, #40]	; 0x28
		FTM_PINCFG(FTM3_CH3_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    11a8:	f240 4144 	movw	r1, #1092	; 0x444
    11ac:	60d9      	str	r1, [r3, #12]
}
    11ae:	bd10      	pop	{r4, pc}
		FTM3_C2V = cval;
    11b0:	4816      	ldr	r0, [pc, #88]	; (120c <analogWrite+0x2f8>)
		FTM_PINCFG(FTM3_CH2_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    11b2:	4b17      	ldr	r3, [pc, #92]	; (1210 <analogWrite+0x2fc>)
		FTM3_C2V = cval;
    11b4:	6202      	str	r2, [r0, #32]
		FTM_PINCFG(FTM3_CH2_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    11b6:	f240 4144 	movw	r1, #1092	; 0x444
    11ba:	6099      	str	r1, [r3, #8]
}
    11bc:	bd10      	pop	{r4, pc}
		FTM0_C4V = cval;
    11be:	4811      	ldr	r0, [pc, #68]	; (1204 <analogWrite+0x2f0>)
		FTM_PINCFG(FTM0_CH4_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    11c0:	4b13      	ldr	r3, [pc, #76]	; (1210 <analogWrite+0x2fc>)
		FTM0_C4V = cval;
    11c2:	6302      	str	r2, [r0, #48]	; 0x30
		FTM_PINCFG(FTM0_CH4_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    11c4:	f240 4144 	movw	r1, #1092	; 0x444
    11c8:	6119      	str	r1, [r3, #16]
}
    11ca:	bd10      	pop	{r4, pc}
		FTM0_C7V = cval;
    11cc:	480d      	ldr	r0, [pc, #52]	; (1204 <analogWrite+0x2f0>)
		FTM_PINCFG(FTM0_CH7_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    11ce:	4b10      	ldr	r3, [pc, #64]	; (1210 <analogWrite+0x2fc>)
		FTM0_C7V = cval;
    11d0:	6482      	str	r2, [r0, #72]	; 0x48
		FTM_PINCFG(FTM0_CH7_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    11d2:	f240 4144 	movw	r1, #1092	; 0x444
    11d6:	61d9      	str	r1, [r3, #28]
}
    11d8:	bd10      	pop	{r4, pc}
		FTM1_C1V = cval;
    11da:	480e      	ldr	r0, [pc, #56]	; (1214 <analogWrite+0x300>)
		FTM_PINCFG(FTM1_CH1_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    11dc:	4b0e      	ldr	r3, [pc, #56]	; (1218 <analogWrite+0x304>)
		FTM1_C1V = cval;
    11de:	6182      	str	r2, [r0, #24]
		FTM_PINCFG(FTM1_CH1_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    11e0:	f44f 7151 	mov.w	r1, #836	; 0x344
    11e4:	6359      	str	r1, [r3, #52]	; 0x34
}
    11e6:	bd10      	pop	{r4, pc}
		FTM1_C0V = cval;
    11e8:	480a      	ldr	r0, [pc, #40]	; (1214 <analogWrite+0x300>)
		FTM_PINCFG(FTM1_CH0_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    11ea:	4b0b      	ldr	r3, [pc, #44]	; (1218 <analogWrite+0x304>)
		FTM1_C0V = cval;
    11ec:	6102      	str	r2, [r0, #16]
		FTM_PINCFG(FTM1_CH0_PIN) = PORT_PCR_MUX(3) | PORT_PCR_DSE | PORT_PCR_SRE;
    11ee:	f44f 7151 	mov.w	r1, #836	; 0x344
    11f2:	6319      	str	r1, [r3, #48]	; 0x30
}
    11f4:	bd10      	pop	{r4, pc}
		FTM3_C0V = cval;
    11f6:	4805      	ldr	r0, [pc, #20]	; (120c <analogWrite+0x2f8>)
		FTM_PINCFG(FTM3_CH0_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    11f8:	4b05      	ldr	r3, [pc, #20]	; (1210 <analogWrite+0x2fc>)
		FTM3_C0V = cval;
    11fa:	6102      	str	r2, [r0, #16]
		FTM_PINCFG(FTM3_CH0_PIN) = PORT_PCR_MUX(4) | PORT_PCR_DSE | PORT_PCR_SRE;
    11fc:	f240 4144 	movw	r1, #1092	; 0x444
    1200:	6019      	str	r1, [r3, #0]
}
    1202:	bd10      	pop	{r4, pc}
    1204:	40038000 	.word	0x40038000
    1208:	4004b000 	.word	0x4004b000
    120c:	400b9000 	.word	0x400b9000
    1210:	4004c000 	.word	0x4004c000
    1214:	40039000 	.word	0x40039000
    1218:	40049000 	.word	0x40049000

0000121c <analogWriteRes>:
{
    121c:	4603      	mov	r3, r0
	analog_write_res = bits;
    121e:	2b10      	cmp	r3, #16
    1220:	bf28      	it	cs
    1222:	2310      	movcs	r3, #16
	prior_res = analog_write_res;
    1224:	4a03      	ldr	r2, [pc, #12]	; (1234 <analogWriteRes+0x18>)
	analog_write_res = bits;
    1226:	2b01      	cmp	r3, #1
    1228:	bf38      	it	cc
    122a:	2301      	movcc	r3, #1
	prior_res = analog_write_res;
    122c:	7810      	ldrb	r0, [r2, #0]
	analog_write_res = bits;
    122e:	7013      	strb	r3, [r2, #0]
}
    1230:	4770      	bx	lr
    1232:	bf00      	nop
    1234:	1fff0c0a 	.word	0x1fff0c0a

00001238 <digitalWrite>:
	if (pin >= CORE_NUM_DIGITAL) return;
    1238:	283f      	cmp	r0, #63	; 0x3f
    123a:	d818      	bhi.n	126e <digitalWrite+0x36>
{
    123c:	b410      	push	{r4}
	if (*portModeRegister(pin)) {
    123e:	4b12      	ldr	r3, [pc, #72]	; (1288 <digitalWrite+0x50>)
    1240:	f853 2030 	ldr.w	r2, [r3, r0, lsl #3]
    1244:	f892 4280 	ldrb.w	r4, [r2, #640]	; 0x280
    1248:	b134      	cbz	r4, 1258 <digitalWrite+0x20>
			*portSetRegister(pin) = 1;
    124a:	2301      	movs	r3, #1
		if (val) {
    124c:	b981      	cbnz	r1, 1270 <digitalWrite+0x38>
			*portClearRegister(pin) = 1;
    124e:	f882 3100 	strb.w	r3, [r2, #256]	; 0x100
}
    1252:	f85d 4b04 	ldr.w	r4, [sp], #4
    1256:	4770      	bx	lr
		volatile uint32_t *config = portConfigRegister(pin);
    1258:	eb03 03c0 	add.w	r3, r3, r0, lsl #3
    125c:	685b      	ldr	r3, [r3, #4]
			*config |= (PORT_PCR_PE | PORT_PCR_PS);
    125e:	681a      	ldr	r2, [r3, #0]
		if (val) {
    1260:	b159      	cbz	r1, 127a <digitalWrite+0x42>
			*config |= (PORT_PCR_PE | PORT_PCR_PS);
    1262:	f042 0203 	orr.w	r2, r2, #3
}
    1266:	f85d 4b04 	ldr.w	r4, [sp], #4
			*config |= (PORT_PCR_PE | PORT_PCR_PS);
    126a:	601a      	str	r2, [r3, #0]
}
    126c:	4770      	bx	lr
    126e:	4770      	bx	lr
			*portSetRegister(pin) = 1;
    1270:	f882 3080 	strb.w	r3, [r2, #128]	; 0x80
}
    1274:	f85d 4b04 	ldr.w	r4, [sp], #4
    1278:	4770      	bx	lr
			*config &= ~(PORT_PCR_PE);
    127a:	f022 0202 	bic.w	r2, r2, #2
}
    127e:	f85d 4b04 	ldr.w	r4, [sp], #4
			*config &= ~(PORT_PCR_PE);
    1282:	601a      	str	r2, [r3, #0]
}
    1284:	4770      	bx	lr
    1286:	bf00      	nop
    1288:	00002984 	.word	0x00002984

0000128c <pinMode>:
	if (pin >= CORE_NUM_DIGITAL) return;
    128c:	283f      	cmp	r0, #63	; 0x3f
    128e:	d820      	bhi.n	12d2 <pinMode+0x46>
	config = portConfigRegister(pin);
    1290:	4a1b      	ldr	r2, [pc, #108]	; (1300 <pinMode+0x74>)
    1292:	eb02 03c0 	add.w	r3, r2, r0, lsl #3
	if (mode == OUTPUT || mode == OUTPUT_OPENDRAIN) {
    1296:	2901      	cmp	r1, #1
	config = portConfigRegister(pin);
    1298:	685b      	ldr	r3, [r3, #4]
	if (mode == OUTPUT || mode == OUTPUT_OPENDRAIN) {
    129a:	d00f      	beq.n	12bc <pinMode+0x30>
    129c:	2904      	cmp	r1, #4
    129e:	d01d      	beq.n	12dc <pinMode+0x50>
                }
	} else {
#ifdef KINETISK
		*portModeRegister(pin) = 0;
    12a0:	f852 0030 	ldr.w	r0, [r2, r0, lsl #3]
    12a4:	2200      	movs	r2, #0
    12a6:	f880 2280 	strb.w	r2, [r0, #640]	; 0x280
#else
		*portModeRegister(pin) &= ~digitalPinToBitMask(pin);
#endif
		if (mode == INPUT) {
    12aa:	b199      	cbz	r1, 12d4 <pinMode+0x48>
			*config = PORT_PCR_MUX(1);
		} else if (mode == INPUT_PULLUP) {
    12ac:	2902      	cmp	r1, #2
    12ae:	d022      	beq.n	12f6 <pinMode+0x6a>
			*config = PORT_PCR_MUX(1) | PORT_PCR_PE | PORT_PCR_PS;
		} else if (mode == INPUT_PULLDOWN) {
    12b0:	2903      	cmp	r1, #3
    12b2:	d10d      	bne.n	12d0 <pinMode+0x44>
			*config = PORT_PCR_MUX(1) | PORT_PCR_PE;
    12b4:	f44f 7281 	mov.w	r2, #258	; 0x102
    12b8:	601a      	str	r2, [r3, #0]
    12ba:	4770      	bx	lr
		*portModeRegister(pin) = 1;
    12bc:	f852 0030 	ldr.w	r0, [r2, r0, lsl #3]
		*config = PORT_PCR_SRE | PORT_PCR_DSE | PORT_PCR_MUX(1);
    12c0:	f44f 72a2 	mov.w	r2, #324	; 0x144
		*portModeRegister(pin) = 1;
    12c4:	f880 1280 	strb.w	r1, [r0, #640]	; 0x280
		*config = PORT_PCR_SRE | PORT_PCR_DSE | PORT_PCR_MUX(1);
    12c8:	601a      	str	r2, [r3, #0]
		    *config &= ~PORT_PCR_ODE;
    12ca:	681a      	ldr	r2, [r3, #0]
    12cc:	f022 0220 	bic.w	r2, r2, #32
    12d0:	601a      	str	r2, [r3, #0]
		} else { // INPUT_DISABLE
			*config = 0;
		}
	}
}
    12d2:	4770      	bx	lr
			*config = PORT_PCR_MUX(1);
    12d4:	f44f 7280 	mov.w	r2, #256	; 0x100
    12d8:	601a      	str	r2, [r3, #0]
    12da:	4770      	bx	lr
		*portModeRegister(pin) = 1;
    12dc:	f852 1030 	ldr.w	r1, [r2, r0, lsl #3]
		*config = PORT_PCR_SRE | PORT_PCR_DSE | PORT_PCR_MUX(1);
    12e0:	f44f 72a2 	mov.w	r2, #324	; 0x144
		*portModeRegister(pin) = 1;
    12e4:	2001      	movs	r0, #1
    12e6:	f881 0280 	strb.w	r0, [r1, #640]	; 0x280
		*config = PORT_PCR_SRE | PORT_PCR_DSE | PORT_PCR_MUX(1);
    12ea:	601a      	str	r2, [r3, #0]
		    *config |= PORT_PCR_ODE;
    12ec:	681a      	ldr	r2, [r3, #0]
    12ee:	f042 0220 	orr.w	r2, r2, #32
    12f2:	601a      	str	r2, [r3, #0]
    12f4:	4770      	bx	lr
			*config = PORT_PCR_MUX(1) | PORT_PCR_PE | PORT_PCR_PS;
    12f6:	f240 1203 	movw	r2, #259	; 0x103
    12fa:	601a      	str	r2, [r3, #0]
    12fc:	4770      	bx	lr
    12fe:	bf00      	nop
    1300:	00002984 	.word	0x00002984

00001304 <micros>:

uint32_t micros(void)
{
	uint32_t count, current, istatus;

	__disable_irq();
    1304:	b672      	cpsid	i
	current = SYST_CVR;
	count = systick_millis_count;
    1306:	490d      	ldr	r1, [pc, #52]	; (133c <micros+0x38>)
	current = SYST_CVR;
    1308:	f04f 22e0 	mov.w	r2, #3758153728	; 0xe000e000
    130c:	6993      	ldr	r3, [r2, #24]
	count = systick_millis_count;
    130e:	6808      	ldr	r0, [r1, #0]
	istatus = SCB_ICSR;	// bit 26 indicates if systick exception pending
    1310:	f8d2 2d04 	ldr.w	r2, [r2, #3332]	; 0xd04
	__enable_irq();
    1314:	b662      	cpsie	i
	 //systick_current = current;
	 //systick_count = count;
	 //systick_istatus = istatus & SCB_ICSR_PENDSTSET ? 1 : 0;
	if ((istatus & SCB_ICSR_PENDSTSET) && current > 50) count++;
    1316:	0152      	lsls	r2, r2, #5
    1318:	d502      	bpl.n	1320 <micros+0x1c>
    131a:	2b32      	cmp	r3, #50	; 0x32
    131c:	bf88      	it	hi
    131e:	3001      	addhi	r0, #1
#if defined(KINETISL) && F_CPU == 48000000
	return count * 1000 + ((current * (uint32_t)87381) >> 22);
#elif defined(KINETISL) && F_CPU == 24000000
	return count * 1000 + ((current * (uint32_t)174763) >> 22);
#endif
	return count * 1000 + current / (F_CPU / 1000000);
    1320:	4a07      	ldr	r2, [pc, #28]	; (1340 <micros+0x3c>)
	current = ((F_CPU / 1000) - 1) - current;
    1322:	f5c3 33ea 	rsb	r3, r3, #119808	; 0x1d400
	return count * 1000 + current / (F_CPU / 1000000);
    1326:	f44f 717a 	mov.w	r1, #1000	; 0x3e8
	current = ((F_CPU / 1000) - 1) - current;
    132a:	33bf      	adds	r3, #191	; 0xbf
	return count * 1000 + current / (F_CPU / 1000000);
    132c:	fba2 2303 	umull	r2, r3, r2, r3
    1330:	fb01 f000 	mul.w	r0, r1, r0
}
    1334:	eb00 1093 	add.w	r0, r0, r3, lsr #6
    1338:	4770      	bx	lr
    133a:	bf00      	nop
    133c:	1fff0d88 	.word	0x1fff0d88
    1340:	88888889 	.word	0x88888889

00001344 <usb_rx>:


usb_packet_t *usb_rx(uint32_t endpoint)
{
	usb_packet_t *ret;
	endpoint--;
    1344:	1e43      	subs	r3, r0, #1
	if (endpoint >= NUM_ENDPOINTS) return NULL;
    1346:	2b04      	cmp	r3, #4
    1348:	d816      	bhi.n	1378 <usb_rx+0x34>
	__disable_irq();
    134a:	b672      	cpsid	i
	ret = rx_first[endpoint];
    134c:	490b      	ldr	r1, [pc, #44]	; (137c <usb_rx+0x38>)
    134e:	f851 0023 	ldr.w	r0, [r1, r3, lsl #2]
	if (ret) {
    1352:	b178      	cbz	r0, 1374 <usb_rx+0x30>
{
    1354:	b430      	push	{r4, r5}
		rx_first[endpoint] = ret->next;
		usb_rx_byte_count_data[endpoint] -= ret->len;
    1356:	4c0a      	ldr	r4, [pc, #40]	; (1380 <usb_rx+0x3c>)
    1358:	f8b0 c000 	ldrh.w	ip, [r0]
    135c:	f834 2013 	ldrh.w	r2, [r4, r3, lsl #1]
		rx_first[endpoint] = ret->next;
    1360:	6845      	ldr	r5, [r0, #4]
    1362:	f841 5023 	str.w	r5, [r1, r3, lsl #2]
		usb_rx_byte_count_data[endpoint] -= ret->len;
    1366:	eba2 020c 	sub.w	r2, r2, ip
    136a:	f824 2013 	strh.w	r2, [r4, r3, lsl #1]
	}
	__enable_irq();
    136e:	b662      	cpsie	i
	//serial_phex(endpoint);
	//serial_print(", packet=");
	//serial_phex32(ret);
	//serial_print("\n");
	return ret;
}
    1370:	bc30      	pop	{r4, r5}
    1372:	4770      	bx	lr
	__enable_irq();
    1374:	b662      	cpsie	i
}
    1376:	4770      	bx	lr
	if (endpoint >= NUM_ENDPOINTS) return NULL;
    1378:	2000      	movs	r0, #0
    137a:	4770      	bx	lr
    137c:	1fff0e1c 	.word	0x1fff0e1c
    1380:	1fff0e7c 	.word	0x1fff0e7c

00001384 <usb_tx_packet_count>:
uint32_t usb_tx_packet_count(uint32_t endpoint)
{
	const usb_packet_t *p;
	uint32_t count=0;

	endpoint--;
    1384:	3801      	subs	r0, #1
	if (endpoint >= NUM_ENDPOINTS) return 0;
    1386:	2804      	cmp	r0, #4
    1388:	d80b      	bhi.n	13a2 <usb_tx_packet_count+0x1e>
	__disable_irq();
    138a:	b672      	cpsid	i
	for (p = tx_first[endpoint]; p; p = p->next) count++;
    138c:	4b07      	ldr	r3, [pc, #28]	; (13ac <usb_tx_packet_count+0x28>)
    138e:	f853 3020 	ldr.w	r3, [r3, r0, lsl #2]
    1392:	b143      	cbz	r3, 13a6 <usb_tx_packet_count+0x22>
	uint32_t count=0;
    1394:	2000      	movs	r0, #0
	for (p = tx_first[endpoint]; p; p = p->next) count++;
    1396:	685b      	ldr	r3, [r3, #4]
    1398:	3001      	adds	r0, #1
    139a:	2b00      	cmp	r3, #0
    139c:	d1fb      	bne.n	1396 <usb_tx_packet_count+0x12>
	__enable_irq();
    139e:	b662      	cpsie	i
	return count;
    13a0:	4770      	bx	lr
	if (endpoint >= NUM_ENDPOINTS) return 0;
    13a2:	2000      	movs	r0, #0
}
    13a4:	4770      	bx	lr
	uint32_t count=0;
    13a6:	4618      	mov	r0, r3
    13a8:	e7f9      	b.n	139e <usb_tx_packet_count+0x1a>
    13aa:	bf00      	nop
    13ac:	1fff0e4c 	.word	0x1fff0e4c

000013b0 <usb_rx_memory>:
// user is creating data very quickly, their consumption could starve reception
// without this prioritization.  The packet buffer (input) is assigned to the
// first endpoint needing memory.
//
void usb_rx_memory(usb_packet_t *packet)
{
    13b0:	b410      	push	{r4}
	unsigned int i;
	const uint8_t *cfg;

	cfg = usb_endpoint_config_table;
	//serial_print("rx_mem:");
	__disable_irq();
    13b2:	b672      	cpsid	i
	cfg = usb_endpoint_config_table;
    13b4:	4a1a      	ldr	r2, [pc, #104]	; (1420 <usb_rx_memory+0x70>)
	for (i=1; i <= NUM_ENDPOINTS; i++) {
#ifdef AUDIO_INTERFACE
		if (i == AUDIO_RX_ENDPOINT) continue;
#endif
		if (*cfg++ & USB_ENDPT_EPRXEN) {
			if (table[index(i, RX, EVEN)].desc == 0) {
    13b6:	4c1b      	ldr	r4, [pc, #108]	; (1424 <usb_rx_memory+0x74>)
	__disable_irq();
    13b8:	2304      	movs	r3, #4
		if (*cfg++ & USB_ENDPT_EPRXEN) {
    13ba:	f812 1b01 	ldrb.w	r1, [r2], #1
    13be:	0709      	lsls	r1, r1, #28
				__enable_irq();
				//serial_phex(i);
				//serial_print(",even\n");
				return;
			}
			if (table[index(i, RX, ODD)].desc == 0) {
    13c0:	f043 0c01 	orr.w	ip, r3, #1
		if (*cfg++ & USB_ENDPT_EPRXEN) {
    13c4:	d505      	bpl.n	13d2 <usb_rx_memory+0x22>
			if (table[index(i, RX, EVEN)].desc == 0) {
    13c6:	f854 1033 	ldr.w	r1, [r4, r3, lsl #3]
    13ca:	b169      	cbz	r1, 13e8 <usb_rx_memory+0x38>
			if (table[index(i, RX, ODD)].desc == 0) {
    13cc:	f854 103c 	ldr.w	r1, [r4, ip, lsl #3]
    13d0:	b1c9      	cbz	r1, 1406 <usb_rx_memory+0x56>
	for (i=1; i <= NUM_ENDPOINTS; i++) {
    13d2:	3304      	adds	r3, #4
    13d4:	2b18      	cmp	r3, #24
    13d6:	d1f0      	bne.n	13ba <usb_rx_memory+0xa>
				//serial_print(",odd\n");
				return;
			}
		}
	}
	__enable_irq();
    13d8:	b662      	cpsie	i
	// we should never reach this point.  If we get here, it means
	// usb_rx_memory_needed was set greater than zero, but no memory
	// was actually needed.
	usb_rx_memory_needed = 0;
    13da:	4b13      	ldr	r3, [pc, #76]	; (1428 <usb_rx_memory+0x78>)
    13dc:	2200      	movs	r2, #0
    13de:	701a      	strb	r2, [r3, #0]
	usb_free(packet);
	return;
}
    13e0:	f85d 4b04 	ldr.w	r4, [sp], #4
	usb_free(packet);
    13e4:	f000 bca8 	b.w	1d38 <usb_free>
				usb_rx_memory_needed--;
    13e8:	490f      	ldr	r1, [pc, #60]	; (1428 <usb_rx_memory+0x78>)
				table[index(i, RX, EVEN)].addr = packet->buf;
    13ea:	eb04 02c3 	add.w	r2, r4, r3, lsl #3
    13ee:	3008      	adds	r0, #8
    13f0:	6050      	str	r0, [r2, #4]
				usb_rx_memory_needed--;
    13f2:	780a      	ldrb	r2, [r1, #0]
				table[index(i, RX, EVEN)].desc = BDT_DESC(64, 0);
    13f4:	480d      	ldr	r0, [pc, #52]	; (142c <usb_rx_memory+0x7c>)
    13f6:	f844 0033 	str.w	r0, [r4, r3, lsl #3]
				usb_rx_memory_needed--;
    13fa:	3a01      	subs	r2, #1
    13fc:	700a      	strb	r2, [r1, #0]
				__enable_irq();
    13fe:	b662      	cpsie	i
}
    1400:	f85d 4b04 	ldr.w	r4, [sp], #4
    1404:	4770      	bx	lr
				usb_rx_memory_needed--;
    1406:	4a08      	ldr	r2, [pc, #32]	; (1428 <usb_rx_memory+0x78>)
				table[index(i, RX, ODD)].desc = BDT_DESC(64, 1);
    1408:	4909      	ldr	r1, [pc, #36]	; (1430 <usb_rx_memory+0x80>)
				table[index(i, RX, ODD)].addr = packet->buf;
    140a:	eb04 03cc 	add.w	r3, r4, ip, lsl #3
    140e:	3008      	adds	r0, #8
    1410:	6058      	str	r0, [r3, #4]
				usb_rx_memory_needed--;
    1412:	7813      	ldrb	r3, [r2, #0]
				table[index(i, RX, ODD)].desc = BDT_DESC(64, 1);
    1414:	f844 103c 	str.w	r1, [r4, ip, lsl #3]
				usb_rx_memory_needed--;
    1418:	3b01      	subs	r3, #1
    141a:	7013      	strb	r3, [r2, #0]
				__enable_irq();
    141c:	b662      	cpsie	i
				return;
    141e:	e7ef      	b.n	1400 <usb_rx_memory+0x50>
    1420:	00002c10 	.word	0x00002c10
    1424:	1fff0000 	.word	0x1fff0000
    1428:	1fff0e86 	.word	0x1fff0e86
    142c:	******** 	.word	0x********
    1430:	004000c8 	.word	0x004000c8

00001434 <usb_tx>:
void usb_tx(uint32_t endpoint, usb_packet_t *packet)
{
	bdt_t *b = &table[index(endpoint, TX, EVEN)];
	uint8_t next;

	endpoint--;
    1434:	1e42      	subs	r2, r0, #1
	if (endpoint >= NUM_ENDPOINTS) return;
    1436:	2a04      	cmp	r2, #4
    1438:	d80e      	bhi.n	1458 <usb_tx+0x24>
	bdt_t *b = &table[index(endpoint, TX, EVEN)];
    143a:	4b1c      	ldr	r3, [pc, #112]	; (14ac <usb_tx+0x78>)
    143c:	0140      	lsls	r0, r0, #5
    143e:	f040 0010 	orr.w	r0, r0, #16
{
    1442:	b410      	push	{r4}
	bdt_t *b = &table[index(endpoint, TX, EVEN)];
    1444:	4403      	add	r3, r0
	__disable_irq();
    1446:	b672      	cpsid	i
	//serial_print("txstate=");
	//serial_phex(tx_state[endpoint]);
	//serial_print("\n");
	switch (tx_state[endpoint]) {
    1448:	4c19      	ldr	r4, [pc, #100]	; (14b0 <usb_tx+0x7c>)
    144a:	5ca0      	ldrb	r0, [r4, r2]
    144c:	2803      	cmp	r0, #3
    144e:	d81d      	bhi.n	148c <usb_tx+0x58>
    1450:	e8df f000 	tbb	[pc, r0]
    1454:	19031614 	.word	0x19031614
    1458:	4770      	bx	lr
    145a:	2005      	movs	r0, #5
		}
		tx_last[endpoint] = packet;
		__enable_irq();
		return;
	}
	tx_state[endpoint] = next;
    145c:	54a0      	strb	r0, [r4, r2]
	b->addr = packet->buf;
	b->desc = BDT_DESC(packet->len, ((uint32_t)b & 8) ? DATA1 : DATA0);
    145e:	f013 0f08 	tst.w	r3, #8
    1462:	8808      	ldrh	r0, [r1, #0]
    1464:	bf14      	ite	ne
    1466:	22c8      	movne	r2, #200	; 0xc8
    1468:	2288      	moveq	r2, #136	; 0x88
	b->addr = packet->buf;
    146a:	3108      	adds	r1, #8
	b->desc = BDT_DESC(packet->len, ((uint32_t)b & 8) ? DATA1 : DATA0);
    146c:	ea42 4200 	orr.w	r2, r2, r0, lsl #16
	b->addr = packet->buf;
    1470:	6059      	str	r1, [r3, #4]
	b->desc = BDT_DESC(packet->len, ((uint32_t)b & 8) ? DATA1 : DATA0);
    1472:	601a      	str	r2, [r3, #0]
	__enable_irq();
    1474:	b662      	cpsie	i
}
    1476:	f85d 4b04 	ldr.w	r4, [sp], #4
    147a:	4770      	bx	lr
		next = TX_STATE_ODD_FREE;
    147c:	2003      	movs	r0, #3
    147e:	e7ed      	b.n	145c <usb_tx+0x28>
		b++;
    1480:	3308      	adds	r3, #8
		next = TX_STATE_EVEN_FREE;
    1482:	2002      	movs	r0, #2
		break;
    1484:	e7ea      	b.n	145c <usb_tx+0x28>
		b++;
    1486:	3308      	adds	r3, #8
		next = TX_STATE_NONE_FREE_EVEN_FIRST;
    1488:	2004      	movs	r0, #4
		break;
    148a:	e7e7      	b.n	145c <usb_tx+0x28>
		if (tx_first[endpoint] == NULL) {
    148c:	4b09      	ldr	r3, [pc, #36]	; (14b4 <usb_tx+0x80>)
    148e:	f853 0022 	ldr.w	r0, [r3, r2, lsl #2]
    1492:	b138      	cbz	r0, 14a4 <usb_tx+0x70>
			tx_last[endpoint]->next = packet;
    1494:	4b08      	ldr	r3, [pc, #32]	; (14b8 <usb_tx+0x84>)
    1496:	f853 0022 	ldr.w	r0, [r3, r2, lsl #2]
    149a:	6041      	str	r1, [r0, #4]
		tx_last[endpoint] = packet;
    149c:	f843 1022 	str.w	r1, [r3, r2, lsl #2]
		__enable_irq();
    14a0:	b662      	cpsie	i
		return;
    14a2:	e7e8      	b.n	1476 <usb_tx+0x42>
			tx_first[endpoint] = packet;
    14a4:	f843 1022 	str.w	r1, [r3, r2, lsl #2]
    14a8:	4b03      	ldr	r3, [pc, #12]	; (14b8 <usb_tx+0x84>)
    14aa:	e7f7      	b.n	149c <usb_tx+0x68>
    14ac:	1fff0000 	.word	0x1fff0000
    14b0:	1fff0e74 	.word	0x1fff0e74
    14b4:	1fff0e4c 	.word	0x1fff0e4c
    14b8:	1fff0e60 	.word	0x1fff0e60

000014bc <usb_isr>:
}



void usb_isr(void)
{
    14bc:	e92d 4ff0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
	//serial_print("isr");
	//status = USB0_ISTAT;
	//serial_phex(status);
	//serial_print("\n");
	restart:
	status = USB0_ISTAT;
    14c0:	f8df a310 	ldr.w	sl, [pc, #784]	; 17d4 <usb_isr+0x318>
						//serial_phex(endpoint + 1);
						b->desc = 0;
						usb_rx_memory_needed++;
					}
				} else {
					b->desc = BDT_DESC(64, ((uint32_t)b & 8) ? DATA1 : DATA0);
    14c4:	f8df b310 	ldr.w	fp, [pc, #784]	; 17d8 <usb_isr+0x31c>
{
    14c8:	b085      	sub	sp, #20
    14ca:	e03d      	b.n	1548 <usb_isr+0x8c>
			bdt_t *b = stat2bufferdescriptor(stat);
    14cc:	4db1      	ldr	r5, [pc, #708]	; (1794 <usb_isr+0x2d8>)
    14ce:	089e      	lsrs	r6, r3, #2
    14d0:	eb05 07c6 	add.w	r7, r5, r6, lsl #3
			endpoint--;	// endpoint is index to zero-based arrays
    14d4:	3a01      	subs	r2, #1
			usb_packet_t *packet = (usb_packet_t *)((uint8_t *)(b->addr) - 8);
    14d6:	6879      	ldr	r1, [r7, #4]
			if (stat & 0x08) { // transmit
    14d8:	f013 0308 	ands.w	r3, r3, #8
			endpoint--;	// endpoint is index to zero-based arrays
    14dc:	b2d4      	uxtb	r4, r2
			usb_packet_t *packet = (usb_packet_t *)((uint8_t *)(b->addr) - 8);
    14de:	f1a1 0008 	sub.w	r0, r1, #8
			if (stat & 0x08) { // transmit
    14e2:	f040 80ba 	bne.w	165a <usb_isr+0x19e>
				packet->len = b->desc >> 16;
    14e6:	f855 2036 	ldr.w	r2, [r5, r6, lsl #3]
    14ea:	ea4f 4c12 	mov.w	ip, r2, lsr #16
				if (packet->len > 0) {
    14ee:	0c12      	lsrs	r2, r2, #16
				packet->len = b->desc >> 16;
    14f0:	f821 cc08 	strh.w	ip, [r1, #-8]
				if (packet->len > 0) {
    14f4:	f000 80a9 	beq.w	164a <usb_isr+0x18e>
					packet->index = 0;
    14f8:	f821 3c06 	strh.w	r3, [r1, #-6]
					packet->next = NULL;
    14fc:	f841 3c04 	str.w	r3, [r1, #-4]
					if (rx_first[endpoint] == NULL) {
    1500:	4ba5      	ldr	r3, [pc, #660]	; (1798 <usb_isr+0x2dc>)
    1502:	f853 2024 	ldr.w	r2, [r3, r4, lsl #2]
    1506:	2a00      	cmp	r2, #0
    1508:	f000 8203 	beq.w	1912 <usb_isr+0x456>
						rx_last[endpoint]->next = packet;
    150c:	4ba3      	ldr	r3, [pc, #652]	; (179c <usb_isr+0x2e0>)
    150e:	f853 2024 	ldr.w	r2, [r3, r4, lsl #2]
    1512:	6050      	str	r0, [r2, #4]
					rx_last[endpoint] = packet;
    1514:	f843 0024 	str.w	r0, [r3, r4, lsl #2]
					usb_rx_byte_count_data[endpoint] += packet->len;
    1518:	4ba1      	ldr	r3, [pc, #644]	; (17a0 <usb_isr+0x2e4>)
    151a:	f833 2014 	ldrh.w	r2, [r3, r4, lsl #1]
    151e:	4494      	add	ip, r2
    1520:	f823 c014 	strh.w	ip, [r3, r4, lsl #1]
					packet = usb_malloc();
    1524:	f000 fbe8 	bl	1cf8 <usb_malloc>
					if (packet) {
    1528:	2800      	cmp	r0, #0
    152a:	f000 81eb 	beq.w	1904 <usb_isr+0x448>
						b->desc = BDT_DESC(64,
    152e:	f017 0f08 	tst.w	r7, #8
    1532:	4b9c      	ldr	r3, [pc, #624]	; (17a4 <usb_isr+0x2e8>)
						b->addr = packet->buf;
    1534:	f100 0008 	add.w	r0, r0, #8
						b->desc = BDT_DESC(64,
    1538:	bf18      	it	ne
    153a:	465b      	movne	r3, fp
						b->addr = packet->buf;
    153c:	6078      	str	r0, [r7, #4]
						b->desc = BDT_DESC(64,
    153e:	f845 3036 	str.w	r3, [r5, r6, lsl #3]
				}
			}

		}
		USB0_ISTAT = USB_ISTAT_TOKDNE;
    1542:	2308      	movs	r3, #8
    1544:	f88a 3080 	strb.w	r3, [sl, #128]	; 0x80
	status = USB0_ISTAT;
    1548:	f89a 3080 	ldrb.w	r3, [sl, #128]	; 0x80
	if ((status & USB_ISTAT_SOFTOK /* 04 */ )) {
    154c:	0759      	lsls	r1, r3, #29
	status = USB0_ISTAT;
    154e:	b2dc      	uxtb	r4, r3
	if ((status & USB_ISTAT_SOFTOK /* 04 */ )) {
    1550:	d51d      	bpl.n	158e <usb_isr+0xd2>
		if (usb_configuration) {
    1552:	4b95      	ldr	r3, [pc, #596]	; (17a8 <usb_isr+0x2ec>)
    1554:	781b      	ldrb	r3, [r3, #0]
    1556:	b1bb      	cbz	r3, 1588 <usb_isr+0xcc>
			t = usb_reboot_timer;
    1558:	4994      	ldr	r1, [pc, #592]	; (17ac <usb_isr+0x2f0>)
    155a:	780a      	ldrb	r2, [r1, #0]
			if (t) {
    155c:	f002 03ff 	and.w	r3, r2, #255	; 0xff
    1560:	b12a      	cbz	r2, 156e <usb_isr+0xb2>
				usb_reboot_timer = --t;
    1562:	3b01      	subs	r3, #1
    1564:	b2db      	uxtb	r3, r3
    1566:	700b      	strb	r3, [r1, #0]
				if (!t) _reboot_Teensyduino_();
    1568:	2b00      	cmp	r3, #0
    156a:	f000 8348 	beq.w	1bfe <usb_isr+0x742>
			t = usb_cdc_transmit_flush_timer;
    156e:	4990      	ldr	r1, [pc, #576]	; (17b0 <usb_isr+0x2f4>)
    1570:	780a      	ldrb	r2, [r1, #0]
			if (t) {
    1572:	f002 03ff 	and.w	r3, r2, #255	; 0xff
    1576:	b12a      	cbz	r2, 1584 <usb_isr+0xc8>
				usb_cdc_transmit_flush_timer = --t;
    1578:	3b01      	subs	r3, #1
    157a:	b2db      	uxtb	r3, r3
    157c:	700b      	strb	r3, [r1, #0]
				if (t == 0) usb_serial_flush_callback();
    157e:	2b00      	cmp	r3, #0
    1580:	f000 81ed 	beq.w	195e <usb_isr+0x4a2>
                        usb_midi_flush_output();
    1584:	f000 fc20 	bl	1dc8 <usb_midi_flush_output>
		USB0_ISTAT = USB_ISTAT_SOFTOK;
    1588:	2304      	movs	r3, #4
    158a:	f88a 3080 	strb.w	r3, [sl, #128]	; 0x80
	if ((status & USB_ISTAT_TOKDNE /* 08 */ )) {
    158e:	f014 0208 	ands.w	r2, r4, #8
    1592:	f000 81cc 	beq.w	192e <usb_isr+0x472>
		stat = USB0_STAT;
    1596:	f89a 2090 	ldrb.w	r2, [sl, #144]	; 0x90
    159a:	b2d3      	uxtb	r3, r2
		if (endpoint == 0) {
    159c:	0912      	lsrs	r2, r2, #4
    159e:	d195      	bne.n	14cc <usb_isr+0x10>
	b = stat2bufferdescriptor(stat);
    15a0:	f8df 91f0 	ldr.w	r9, [pc, #496]	; 1794 <usb_isr+0x2d8>
    15a4:	089b      	lsrs	r3, r3, #2
    15a6:	eb09 04c3 	add.w	r4, r9, r3, lsl #3
	pid = BDT_PID(b->desc);
    15aa:	f859 0033 	ldr.w	r0, [r9, r3, lsl #3]
	buf = b->addr;
    15ae:	6864      	ldr	r4, [r4, #4]
	pid = BDT_PID(b->desc);
    15b0:	f3c0 0083 	ubfx	r0, r0, #2, #4
	switch (pid) {
    15b4:	2809      	cmp	r0, #9
    15b6:	d07c      	beq.n	16b2 <usb_isr+0x1f6>
    15b8:	d80f      	bhi.n	15da <usb_isr+0x11e>
    15ba:	3801      	subs	r0, #1
    15bc:	2801      	cmp	r0, #1
    15be:	d808      	bhi.n	15d2 <usb_isr+0x116>
		if (setup.wRequestAndType == 0x2021 /*CDC_SET_LINE_CODING*/) {
    15c0:	4a7c      	ldr	r2, [pc, #496]	; (17b4 <usb_isr+0x2f8>)
    15c2:	8815      	ldrh	r5, [r2, #0]
    15c4:	f242 0021 	movw	r0, #8225	; 0x2021
    15c8:	4285      	cmp	r5, r0
    15ca:	f000 81fb 	beq.w	19c4 <usb_isr+0x508>
		b->desc = BDT_DESC(EP0_SIZE, DATA1);
    15ce:	f849 b033 	str.w	fp, [r9, r3, lsl #3]
	USB0_CTL = USB_CTL_USBENSOFEN; // clear TXSUSPENDTOKENBUSY bit
    15d2:	2301      	movs	r3, #1
    15d4:	f88a 3094 	strb.w	r3, [sl, #148]	; 0x94
}
    15d8:	e7b3      	b.n	1542 <usb_isr+0x86>
	switch (pid) {
    15da:	280d      	cmp	r0, #13
    15dc:	d1f9      	bne.n	15d2 <usb_isr+0x116>
		setup.word2 = *(uint32_t *)(buf + 4);
    15de:	e9d4 0700 	ldrd	r0, r7, [r4]
		ep0_tx_ptr = NULL;
    15e2:	4d75      	ldr	r5, [pc, #468]	; (17b8 <usb_isr+0x2fc>)
		b->desc = BDT_DESC(EP0_SIZE, DATA1);
    15e4:	f849 b033 	str.w	fp, [r9, r3, lsl #3]
		setup.word1 = *(uint32_t *)(buf);
    15e8:	4e72      	ldr	r6, [pc, #456]	; (17b4 <usb_isr+0x2f8>)
		ep0_tx_data_toggle = 1;
    15ea:	4c74      	ldr	r4, [pc, #464]	; (17bc <usb_isr+0x300>)
		table[index(0, TX, EVEN)].desc = 0;
    15ec:	f8c9 2010 	str.w	r2, [r9, #16]
    15f0:	b283      	uxth	r3, r0
	switch (setup.wRequestAndType) {
    15f2:	f240 6c81 	movw	ip, #1665	; 0x681
		table[index(0, TX, ODD)].desc = 0;
    15f6:	f8c9 2018 	str.w	r2, [r9, #24]
		ep0_tx_ptr = NULL;
    15fa:	602a      	str	r2, [r5, #0]
	switch (setup.wRequestAndType) {
    15fc:	4563      	cmp	r3, ip
		ep0_tx_data_toggle = 1;
    15fe:	f04f 0201 	mov.w	r2, #1
		setup.word2 = *(uint32_t *)(buf + 4);
    1602:	e9c6 0700 	strd	r0, r7, [r6]
		ep0_tx_data_toggle = 1;
    1606:	7022      	strb	r2, [r4, #0]
	switch (setup.wRequestAndType) {
    1608:	f200 80ff 	bhi.w	180a <usb_isr+0x34e>
    160c:	f5b3 6fd0 	cmp.w	r3, #1664	; 0x680
    1610:	f080 82cb 	bcs.w	1baa <usb_isr+0x6ee>
    1614:	f5b3 7f81 	cmp.w	r3, #258	; 0x102
    1618:	f000 8226 	beq.w	1a68 <usb_isr+0x5ac>
    161c:	f240 80de 	bls.w	17dc <usb_isr+0x320>
    1620:	f240 3202 	movw	r2, #770	; 0x302
    1624:	4293      	cmp	r3, r2
    1626:	d17b      	bne.n	1720 <usb_isr+0x264>
		i = setup.wIndex & 0x7F;
    1628:	f007 077f 	and.w	r7, r7, #127	; 0x7f
		if (i > NUM_ENDPOINTS || setup.wValue != 0) {
    162c:	2f05      	cmp	r7, #5
    162e:	f200 8108 	bhi.w	1842 <usb_isr+0x386>
    1632:	0c02      	lsrs	r2, r0, #16
    1634:	f040 8105 	bne.w	1842 <usb_isr+0x386>
		(*(uint8_t *)(&USB0_ENDPT0 + i * 4)) |= 0x02;
    1638:	4961      	ldr	r1, [pc, #388]	; (17c0 <usb_isr+0x304>)
    163a:	f811 3027 	ldrb.w	r3, [r1, r7, lsl #2]
    163e:	f043 0302 	orr.w	r3, r3, #2
    1642:	f801 3027 	strb.w	r3, [r1, r7, lsl #2]
	const uint8_t *data = NULL;
    1646:	4613      	mov	r3, r2
		break;
    1648:	e070      	b.n	172c <usb_isr+0x270>
					b->desc = BDT_DESC(64, ((uint32_t)b & 8) ? DATA1 : DATA0);
    164a:	f017 0f08 	tst.w	r7, #8
    164e:	4b55      	ldr	r3, [pc, #340]	; (17a4 <usb_isr+0x2e8>)
    1650:	bf18      	it	ne
    1652:	465b      	movne	r3, fp
    1654:	f845 3036 	str.w	r3, [r5, r6, lsl #3]
    1658:	e773      	b.n	1542 <usb_isr+0x86>
				usb_free(packet);
    165a:	f000 fb6d 	bl	1d38 <usb_free>
				packet = tx_first[endpoint];
    165e:	4959      	ldr	r1, [pc, #356]	; (17c4 <usb_isr+0x308>)
					switch (tx_state[endpoint]) {
    1660:	4a59      	ldr	r2, [pc, #356]	; (17c8 <usb_isr+0x30c>)
				packet = tx_first[endpoint];
    1662:	f851 3024 	ldr.w	r3, [r1, r4, lsl #2]
				if (packet) {
    1666:	2b00      	cmp	r3, #0
    1668:	f000 8157 	beq.w	191a <usb_isr+0x45e>
					tx_first[endpoint] = packet->next;
    166c:	6858      	ldr	r0, [r3, #4]
    166e:	f841 0024 	str.w	r0, [r1, r4, lsl #2]
					switch (tx_state[endpoint]) {
    1672:	5d11      	ldrb	r1, [r2, r4]
					b->addr = packet->buf;
    1674:	f103 0008 	add.w	r0, r3, #8
    1678:	6078      	str	r0, [r7, #4]
					switch (tx_state[endpoint]) {
    167a:	2903      	cmp	r1, #3
    167c:	d805      	bhi.n	168a <usb_isr+0x1ce>
    167e:	e8df f001 	tbb	[pc, r1]
    1682:	0f12      	.short	0x0f12
    1684:	0215      	.short	0x0215
						tx_state[endpoint] = TX_STATE_NONE_FREE_EVEN_FIRST;
    1686:	2104      	movs	r1, #4
    1688:	5511      	strb	r1, [r2, r4]
					b->desc = BDT_DESC(packet->len,
    168a:	881a      	ldrh	r2, [r3, #0]
    168c:	f017 0f08 	tst.w	r7, #8
    1690:	bf14      	ite	ne
    1692:	23c8      	movne	r3, #200	; 0xc8
    1694:	2388      	moveq	r3, #136	; 0x88
    1696:	ea43 4302 	orr.w	r3, r3, r2, lsl #16
    169a:	f845 3036 	str.w	r3, [r5, r6, lsl #3]
    169e:	e750      	b.n	1542 <usb_isr+0x86>
						tx_state[endpoint] = TX_STATE_EVEN_FREE;
    16a0:	2102      	movs	r1, #2
    16a2:	5511      	strb	r1, [r2, r4]
						break;
    16a4:	e7f1      	b.n	168a <usb_isr+0x1ce>
						tx_state[endpoint] = TX_STATE_ODD_FREE;
    16a6:	2103      	movs	r1, #3
    16a8:	5511      	strb	r1, [r2, r4]
						break;
    16aa:	e7ee      	b.n	168a <usb_isr+0x1ce>
						tx_state[endpoint] = TX_STATE_NONE_FREE_ODD_FIRST;
    16ac:	2105      	movs	r1, #5
    16ae:	5511      	strb	r1, [r2, r4]
						break;
    16b0:	e7eb      	b.n	168a <usb_isr+0x1ce>
		data = ep0_tx_ptr;
    16b2:	4d41      	ldr	r5, [pc, #260]	; (17b8 <usb_isr+0x2fc>)
    16b4:	682a      	ldr	r2, [r5, #0]
		if (data) {
    16b6:	b33a      	cbz	r2, 1708 <usb_isr+0x24c>
			size = ep0_tx_len;
    16b8:	4944      	ldr	r1, [pc, #272]	; (17cc <usb_isr+0x310>)
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    16ba:	4c40      	ldr	r4, [pc, #256]	; (17bc <usb_isr+0x300>)
			size = ep0_tx_len;
    16bc:	880f      	ldrh	r7, [r1, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    16be:	4844      	ldr	r0, [pc, #272]	; (17d0 <usb_isr+0x314>)
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    16c0:	f894 e000 	ldrb.w	lr, [r4]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    16c4:	7806      	ldrb	r6, [r0, #0]
    16c6:	2f40      	cmp	r7, #64	; 0x40
    16c8:	463b      	mov	r3, r7
    16ca:	bf28      	it	cs
    16cc:	2340      	movcs	r3, #64	; 0x40
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    16ce:	f1be 0f00 	cmp.w	lr, #0
	ep0_tx_data_toggle ^= 1;
    16d2:	f08e 0e01 	eor.w	lr, lr, #1
    16d6:	f884 e000 	strb.w	lr, [r4]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    16da:	f046 0402 	orr.w	r4, r6, #2
	ep0_tx_bdt_bank ^= 1;
    16de:	f086 0601 	eor.w	r6, r6, #1
    16e2:	7006      	strb	r6, [r0, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    16e4:	eb09 00c4 	add.w	r0, r9, r4, lsl #3
    16e8:	6042      	str	r2, [r0, #4]
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    16ea:	bf14      	ite	ne
    16ec:	20c8      	movne	r0, #200	; 0xc8
    16ee:	2088      	moveq	r0, #136	; 0x88
    16f0:	ea40 4003 	orr.w	r0, r0, r3, lsl #16
			data += size;
    16f4:	441a      	add	r2, r3
			ep0_tx_len -= size;
    16f6:	1afb      	subs	r3, r7, r3
    16f8:	b29b      	uxth	r3, r3
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    16fa:	f849 0034 	str.w	r0, [r9, r4, lsl #3]
			ep0_tx_len -= size;
    16fe:	800b      	strh	r3, [r1, #0]
			ep0_tx_ptr = (ep0_tx_len > 0 || size == EP0_SIZE) ? data : NULL;
    1700:	2b00      	cmp	r3, #0
    1702:	f000 8136 	beq.w	1972 <usb_isr+0x4b6>
    1706:	602a      	str	r2, [r5, #0]
		if (setup.bRequest == 5 && setup.bmRequestType == 0) {
    1708:	4b2a      	ldr	r3, [pc, #168]	; (17b4 <usb_isr+0x2f8>)
    170a:	881a      	ldrh	r2, [r3, #0]
    170c:	f5b2 6fa0 	cmp.w	r2, #1280	; 0x500
    1710:	f47f af5f 	bne.w	15d2 <usb_isr+0x116>
			USB0_ADDR = setup.wValue;
    1714:	789a      	ldrb	r2, [r3, #2]
    1716:	f88a 2098 	strb.w	r2, [sl, #152]	; 0x98
			setup.bRequest = 0;
    171a:	2200      	movs	r2, #0
    171c:	705a      	strb	r2, [r3, #1]
			USB0_ADDR = setup.wValue;
    171e:	e758      	b.n	15d2 <usb_isr+0x116>
	switch (setup.wRequestAndType) {
    1720:	f5b3 6fa0 	cmp.w	r3, #1280	; 0x500
    1724:	f040 808d 	bne.w	1842 <usb_isr+0x386>
	uint32_t datalen = 0;
    1728:	2200      	movs	r2, #0
	const uint8_t *data = NULL;
    172a:	4613      	mov	r3, r2
	if (datalen > setup.wLength) datalen = setup.wLength;
    172c:	f8b6 c006 	ldrh.w	ip, [r6, #6]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    1730:	4827      	ldr	r0, [pc, #156]	; (17d0 <usb_isr+0x314>)
    1732:	4594      	cmp	ip, r2
    1734:	bf28      	it	cs
    1736:	4694      	movcs	ip, r2
	if (size > EP0_SIZE) size = EP0_SIZE;
    1738:	4667      	mov	r7, ip
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    173a:	7802      	ldrb	r2, [r0, #0]
    173c:	2f40      	cmp	r7, #64	; 0x40
    173e:	bf28      	it	cs
    1740:	2740      	movcs	r7, #64	; 0x40
    1742:	f042 0e02 	orr.w	lr, r2, #2
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    1746:	043e      	lsls	r6, r7, #16
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    1748:	eb09 08ce 	add.w	r8, r9, lr, lsl #3
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    174c:	f046 06c8 	orr.w	r6, r6, #200	; 0xc8
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    1750:	f8c8 3004 	str.w	r3, [r8, #4]
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    1754:	f849 603e 	str.w	r6, [r9, lr, lsl #3]
	ep0_tx_data_toggle ^= 1;
    1758:	2600      	movs	r6, #0
	data += size;
    175a:	443b      	add	r3, r7
	ep0_tx_data_toggle ^= 1;
    175c:	7026      	strb	r6, [r4, #0]
	if (datalen == 0 && size < EP0_SIZE) return;
    175e:	ebbc 0707 	subs.w	r7, ip, r7
	ep0_tx_bdt_bank ^= 1;
    1762:	f082 0601 	eor.w	r6, r2, #1
    1766:	7006      	strb	r6, [r0, #0]
	if (datalen == 0 && size < EP0_SIZE) return;
    1768:	f040 8111 	bne.w	198e <usb_isr+0x4d2>
    176c:	f1bc 0f3f 	cmp.w	ip, #63	; 0x3f
    1770:	f04f 0501 	mov.w	r5, #1
    1774:	d909      	bls.n	178a <usb_isr+0x2ce>
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    1776:	f046 0602 	orr.w	r6, r6, #2
	ep0_tx_bdt_bank ^= 1;
    177a:	7002      	strb	r2, [r0, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    177c:	eb09 02c6 	add.w	r2, r9, r6, lsl #3
	ep0_tx_data_toggle ^= 1;
    1780:	7025      	strb	r5, [r4, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    1782:	6053      	str	r3, [r2, #4]
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    1784:	2388      	movs	r3, #136	; 0x88
    1786:	f849 3036 	str.w	r3, [r9, r6, lsl #3]
		USB0_CTL = USB_CTL_USBENSOFEN; // clear TXSUSPENDTOKENBUSY bit
    178a:	2301      	movs	r3, #1
    178c:	f88a 3094 	strb.w	r3, [sl, #148]	; 0x94
		break;
    1790:	e71f      	b.n	15d2 <usb_isr+0x116>
    1792:	bf00      	nop
    1794:	1fff0000 	.word	0x1fff0000
    1798:	1fff0e1c 	.word	0x1fff0e1c
    179c:	1fff0e30 	.word	0x1fff0e30
    17a0:	1fff0e7c 	.word	0x1fff0e7c
    17a4:	******** 	.word	0x********
    17a8:	1fff0e79 	.word	0x1fff0e79
    17ac:	1fff0e7a 	.word	0x1fff0e7a
    17b0:	1fff102c 	.word	0x1fff102c
    17b4:	1fff0e44 	.word	0x1fff0e44
    17b8:	1fff0e10 	.word	0x1fff0e10
    17bc:	1fff0e0d 	.word	0x1fff0e0d
    17c0:	400720c0 	.word	0x400720c0
    17c4:	1fff0e4c 	.word	0x1fff0e4c
    17c8:	1fff0e74 	.word	0x1fff0e74
    17cc:	1fff0e0e 	.word	0x1fff0e0e
    17d0:	1fff0e0c 	.word	0x1fff0e0c
    17d4:	******** 	.word	0x********
    17d8:	004000c8 	.word	0x004000c8
	switch (setup.wRequestAndType) {
    17dc:	2b80      	cmp	r3, #128	; 0x80
    17de:	f000 818b 	beq.w	1af8 <usb_isr+0x63c>
    17e2:	2b82      	cmp	r3, #130	; 0x82
    17e4:	d12d      	bne.n	1842 <usb_isr+0x386>
		i = setup.wIndex & 0x7F;
    17e6:	f007 077f 	and.w	r7, r7, #127	; 0x7f
		if (i > NUM_ENDPOINTS) {
    17ea:	2f05      	cmp	r7, #5
    17ec:	d829      	bhi.n	1842 <usb_isr+0x386>
		reply_buffer[0] = 0;
    17ee:	4aa7      	ldr	r2, [pc, #668]	; (1a8c <usb_isr+0x5d0>)
    17f0:	2300      	movs	r3, #0
    17f2:	8013      	strh	r3, [r2, #0]
		if (*(uint8_t *)(&USB0_ENDPT0 + i * 4) & 0x02) reply_buffer[0] = 1;
    17f4:	4ba6      	ldr	r3, [pc, #664]	; (1a90 <usb_isr+0x5d4>)
    17f6:	f813 3027 	ldrb.w	r3, [r3, r7, lsl #2]
    17fa:	079f      	lsls	r7, r3, #30
    17fc:	f140 81fc 	bpl.w	1bf8 <usb_isr+0x73c>
    1800:	2301      	movs	r3, #1
    1802:	7013      	strb	r3, [r2, #0]
		data = reply_buffer;
    1804:	4ba1      	ldr	r3, [pc, #644]	; (1a8c <usb_isr+0x5d0>)
		datalen = 2;
    1806:	2202      	movs	r2, #2
    1808:	e790      	b.n	172c <usb_isr+0x270>
	switch (setup.wRequestAndType) {
    180a:	f242 0c21 	movw	ip, #8225	; 0x2021
    180e:	4563      	cmp	r3, ip
    1810:	d0bb      	beq.n	178a <usb_isr+0x2ce>
    1812:	d91d      	bls.n	1850 <usb_isr+0x394>
    1814:	f242 2221 	movw	r2, #8737	; 0x2221
    1818:	4293      	cmp	r3, r2
    181a:	d10d      	bne.n	1838 <usb_isr+0x37c>
		switch (setup.wIndex) {
    181c:	b2bf      	uxth	r7, r7
    181e:	2f00      	cmp	r7, #0
    1820:	d182      	bne.n	1728 <usb_isr+0x26c>
			usb_cdc_line_rtsdtr_millis = systick_millis_count;
    1822:	4b9c      	ldr	r3, [pc, #624]	; (1a94 <usb_isr+0x5d8>)
    1824:	681a      	ldr	r2, [r3, #0]
    1826:	4b9c      	ldr	r3, [pc, #624]	; (1a98 <usb_isr+0x5dc>)
    1828:	601a      	str	r2, [r3, #0]
			usb_cdc_line_rtsdtr = setup.wValue;
    182a:	4b9c      	ldr	r3, [pc, #624]	; (1a9c <usb_isr+0x5e0>)
    182c:	f3c0 4007 	ubfx	r0, r0, #16, #8
	uint32_t datalen = 0;
    1830:	2200      	movs	r2, #0
			usb_cdc_line_rtsdtr = setup.wValue;
    1832:	7018      	strb	r0, [r3, #0]
	const uint8_t *data = NULL;
    1834:	4613      	mov	r3, r2
			break;
    1836:	e779      	b.n	172c <usb_isr+0x270>
	switch (setup.wRequestAndType) {
    1838:	f242 3221 	movw	r2, #8993	; 0x2321
    183c:	4293      	cmp	r3, r2
    183e:	f43f af73 	beq.w	1728 <usb_isr+0x26c>
	USB0_ENDPT0 = USB_ENDPT_EPSTALL | USB_ENDPT_EPRXEN | USB_ENDPT_EPTXEN | USB_ENDPT_EPHSHK;
    1842:	230f      	movs	r3, #15
    1844:	f88a 30c0 	strb.w	r3, [sl, #192]	; 0xc0
		USB0_CTL = USB_CTL_USBENSOFEN; // clear TXSUSPENDTOKENBUSY bit
    1848:	2301      	movs	r3, #1
    184a:	f88a 3094 	strb.w	r3, [sl, #148]	; 0x94
		break;
    184e:	e6c0      	b.n	15d2 <usb_isr+0x116>
	switch (setup.wRequestAndType) {
    1850:	f5b3 6f08 	cmp.w	r3, #2176	; 0x880
    1854:	f000 814a 	beq.w	1aec <usb_isr+0x630>
    1858:	f5b3 6f10 	cmp.w	r3, #2304	; 0x900
    185c:	d1f1      	bne.n	1842 <usb_isr+0x386>
		usb_configuration = setup.wValue;
    185e:	4b90      	ldr	r3, [pc, #576]	; (1aa0 <usb_isr+0x5e4>)
    1860:	f3c0 4007 	ubfx	r0, r0, #16, #8
		for (i=4; i < (NUM_ENDPOINTS+1)*4; i++) {
    1864:	2504      	movs	r5, #4
		usb_configuration = setup.wValue;
    1866:	7018      	strb	r0, [r3, #0]
			if (table[i].desc & BDT_OWN) {
    1868:	f859 3035 	ldr.w	r3, [r9, r5, lsl #3]
    186c:	061a      	lsls	r2, r3, #24
    186e:	d406      	bmi.n	187e <usb_isr+0x3c2>
		for (i=4; i < (NUM_ENDPOINTS+1)*4; i++) {
    1870:	3501      	adds	r5, #1
    1872:	2d18      	cmp	r5, #24
    1874:	d00c      	beq.n	1890 <usb_isr+0x3d4>
			if (table[i].desc & BDT_OWN) {
    1876:	f859 3035 	ldr.w	r3, [r9, r5, lsl #3]
    187a:	061a      	lsls	r2, r3, #24
    187c:	d5f8      	bpl.n	1870 <usb_isr+0x3b4>
				usb_free((usb_packet_t *)((uint8_t *)(table[i].addr) - 8));
    187e:	eb09 03c5 	add.w	r3, r9, r5, lsl #3
		for (i=4; i < (NUM_ENDPOINTS+1)*4; i++) {
    1882:	3501      	adds	r5, #1
				usb_free((usb_packet_t *)((uint8_t *)(table[i].addr) - 8));
    1884:	6858      	ldr	r0, [r3, #4]
    1886:	3808      	subs	r0, #8
    1888:	f000 fa56 	bl	1d38 <usb_free>
		for (i=4; i < (NUM_ENDPOINTS+1)*4; i++) {
    188c:	2d18      	cmp	r5, #24
    188e:	d1f2      	bne.n	1876 <usb_isr+0x3ba>
    1890:	4a84      	ldr	r2, [pc, #528]	; (1aa4 <usb_isr+0x5e8>)
    1892:	4b85      	ldr	r3, [pc, #532]	; (1aa8 <usb_isr+0x5ec>)
    1894:	9202      	str	r2, [sp, #8]
    1896:	4a85      	ldr	r2, [pc, #532]	; (1aac <usb_isr+0x5f0>)
    1898:	9201      	str	r2, [sp, #4]
    189a:	4e85      	ldr	r6, [pc, #532]	; (1ab0 <usb_isr+0x5f4>)
    189c:	4a85      	ldr	r2, [pc, #532]	; (1ab4 <usb_isr+0x5f8>)
    189e:	f8df 8248 	ldr.w	r8, [pc, #584]	; 1ae8 <usb_isr+0x62c>
				tx_state[i] = TX_STATE_BOTH_FREE_ODD_FIRST;
    18a2:	9403      	str	r4, [sp, #12]
    18a4:	9200      	str	r2, [sp, #0]
			rx_first[i] = NULL;
    18a6:	2500      	movs	r5, #0
				tx_state[i] = TX_STATE_BOTH_FREE_ODD_FIRST;
    18a8:	461c      	mov	r4, r3
			p = rx_first[i];
    18aa:	f856 7b04 	ldr.w	r7, [r6], #4
			while (p) {
    18ae:	b12f      	cbz	r7, 18bc <usb_isr+0x400>
				n = p->next;
    18b0:	4638      	mov	r0, r7
    18b2:	687f      	ldr	r7, [r7, #4]
				usb_free(p);
    18b4:	f000 fa40 	bl	1d38 <usb_free>
			while (p) {
    18b8:	2f00      	cmp	r7, #0
    18ba:	d1f9      	bne.n	18b0 <usb_isr+0x3f4>
			rx_last[i] = NULL;
    18bc:	9b00      	ldr	r3, [sp, #0]
			p = tx_first[i];
    18be:	f8d8 7000 	ldr.w	r7, [r8]
			rx_last[i] = NULL;
    18c2:	f843 5b04 	str.w	r5, [r3], #4
			rx_first[i] = NULL;
    18c6:	f846 5c04 	str.w	r5, [r6, #-4]
			rx_last[i] = NULL;
    18ca:	9300      	str	r3, [sp, #0]
			while (p) {
    18cc:	b12f      	cbz	r7, 18da <usb_isr+0x41e>
				n = p->next;
    18ce:	4638      	mov	r0, r7
    18d0:	687f      	ldr	r7, [r7, #4]
				usb_free(p);
    18d2:	f000 fa31 	bl	1d38 <usb_free>
			while (p) {
    18d6:	2f00      	cmp	r7, #0
    18d8:	d1f9      	bne.n	18ce <usb_isr+0x412>
			tx_last[i] = NULL;
    18da:	9b02      	ldr	r3, [sp, #8]
			tx_first[i] = NULL;
    18dc:	f848 5b04 	str.w	r5, [r8], #4
			tx_last[i] = NULL;
    18e0:	f843 5b04 	str.w	r5, [r3], #4
    18e4:	9302      	str	r3, [sp, #8]
			usb_rx_byte_count_data[i] = 0;
    18e6:	9b01      	ldr	r3, [sp, #4]
    18e8:	f823 5b02 	strh.w	r5, [r3], #2
    18ec:	9301      	str	r3, [sp, #4]
			switch (tx_state[i]) {
    18ee:	7823      	ldrb	r3, [r4, #0]
    18f0:	3b02      	subs	r3, #2
    18f2:	2b03      	cmp	r3, #3
    18f4:	f200 8109 	bhi.w	1b0a <usb_isr+0x64e>
    18f8:	e8df f013 	tbh	[pc, r3, lsl #1]
    18fc:	0104013f 	.word	0x0104013f
    1900:	0104013f 	.word	0x0104013f
						usb_rx_memory_needed++;
    1904:	4a6c      	ldr	r2, [pc, #432]	; (1ab8 <usb_isr+0x5fc>)
						b->desc = 0;
    1906:	f845 0036 	str.w	r0, [r5, r6, lsl #3]
						usb_rx_memory_needed++;
    190a:	7813      	ldrb	r3, [r2, #0]
    190c:	3301      	adds	r3, #1
    190e:	7013      	strb	r3, [r2, #0]
    1910:	e617      	b.n	1542 <usb_isr+0x86>
						rx_first[endpoint] = packet;
    1912:	f843 0024 	str.w	r0, [r3, r4, lsl #2]
    1916:	4b67      	ldr	r3, [pc, #412]	; (1ab4 <usb_isr+0x5f8>)
    1918:	e5fc      	b.n	1514 <usb_isr+0x58>
					switch (tx_state[endpoint]) {
    191a:	5d11      	ldrb	r1, [r2, r4]
    191c:	2902      	cmp	r1, #2
    191e:	d026      	beq.n	196e <usb_isr+0x4b2>
    1920:	f67f ae0f 	bls.w	1542 <usb_isr+0x86>
    1924:	2903      	cmp	r1, #3
    1926:	d11d      	bne.n	1964 <usb_isr+0x4a8>
						tx_state[endpoint] = TX_STATE_BOTH_FREE_ODD_FIRST;
    1928:	2301      	movs	r3, #1
    192a:	5513      	strb	r3, [r2, r4]
						break;
    192c:	e609      	b.n	1542 <usb_isr+0x86>
		goto restart;
	}



	if (status & USB_ISTAT_USBRST /* 01 */ ) {
    192e:	07e0      	lsls	r0, r4, #31
    1930:	d46e      	bmi.n	1a10 <usb_isr+0x554>
		USB0_CTL = USB_CTL_USBENSOFEN;
		return;
	}


	if ((status & USB_ISTAT_STALL /* 80 */ )) {
    1932:	0621      	lsls	r1, r4, #24
    1934:	d421      	bmi.n	197a <usb_isr+0x4be>
		//serial_print("stall:\n");
		USB0_ENDPT0 = USB_ENDPT_EPRXEN | USB_ENDPT_EPTXEN | USB_ENDPT_EPHSHK;
		USB0_ISTAT = USB_ISTAT_STALL;
	}
	if ((status & USB_ISTAT_ERROR /* 02 */ )) {
    1936:	07a2      	lsls	r2, r4, #30
    1938:	d508      	bpl.n	194c <usb_isr+0x490>
		uint8_t err = USB0_ERRSTAT;
    193a:	4b60      	ldr	r3, [pc, #384]	; (1abc <usb_isr+0x600>)
    193c:	f893 2088 	ldrb.w	r2, [r3, #136]	; 0x88
		USB0_ERRSTAT = err;
		//serial_print("err:");
		//serial_phex(err);
		//serial_print("\n");
		USB0_ISTAT = USB_ISTAT_ERROR;
    1940:	2102      	movs	r1, #2
		uint8_t err = USB0_ERRSTAT;
    1942:	b2d2      	uxtb	r2, r2
		USB0_ERRSTAT = err;
    1944:	f883 2088 	strb.w	r2, [r3, #136]	; 0x88
		USB0_ISTAT = USB_ISTAT_ERROR;
    1948:	f883 1080 	strb.w	r1, [r3, #128]	; 0x80
	}

	if ((status & USB_ISTAT_SLEEP /* 10 */ )) {
    194c:	06e3      	lsls	r3, r4, #27
    194e:	d503      	bpl.n	1958 <usb_isr+0x49c>
		//serial_print("sleep\n");
		USB0_ISTAT = USB_ISTAT_SLEEP;
    1950:	4b5a      	ldr	r3, [pc, #360]	; (1abc <usb_isr+0x600>)
    1952:	2210      	movs	r2, #16
    1954:	f883 2080 	strb.w	r2, [r3, #128]	; 0x80
	}

}
    1958:	b005      	add	sp, #20
    195a:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
				if (t == 0) usb_serial_flush_callback();
    195e:	f000 fdb5 	bl	24cc <usb_serial_flush_callback>
    1962:	e60f      	b.n	1584 <usb_isr+0xc8>
						tx_state[endpoint] = ((uint32_t)b & 8) ?
    1964:	f017 0f08 	tst.w	r7, #8
    1968:	bf14      	ite	ne
    196a:	2303      	movne	r3, #3
    196c:	2302      	moveq	r3, #2
    196e:	5513      	strb	r3, [r2, r4]
						break;
    1970:	e5e7      	b.n	1542 <usb_isr+0x86>
			ep0_tx_ptr = (ep0_tx_len > 0 || size == EP0_SIZE) ? data : NULL;
    1972:	2f3f      	cmp	r7, #63	; 0x3f
    1974:	bf98      	it	ls
    1976:	2200      	movls	r2, #0
    1978:	e6c5      	b.n	1706 <usb_isr+0x24a>
		USB0_ENDPT0 = USB_ENDPT_EPRXEN | USB_ENDPT_EPTXEN | USB_ENDPT_EPHSHK;
    197a:	4b50      	ldr	r3, [pc, #320]	; (1abc <usb_isr+0x600>)
		USB0_ISTAT = USB_ISTAT_STALL;
    197c:	2280      	movs	r2, #128	; 0x80
		USB0_ENDPT0 = USB_ENDPT_EPRXEN | USB_ENDPT_EPTXEN | USB_ENDPT_EPHSHK;
    197e:	210d      	movs	r1, #13
    1980:	f883 10c0 	strb.w	r1, [r3, #192]	; 0xc0
		USB0_ISTAT = USB_ISTAT_STALL;
    1984:	f883 2080 	strb.w	r2, [r3, #128]	; 0x80
	if ((status & USB_ISTAT_ERROR /* 02 */ )) {
    1988:	07a2      	lsls	r2, r4, #30
    198a:	d5df      	bpl.n	194c <usb_isr+0x490>
    198c:	e7d5      	b.n	193a <usb_isr+0x47e>
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    198e:	f046 0602 	orr.w	r6, r6, #2
	ep0_tx_bdt_bank ^= 1;
    1992:	7002      	strb	r2, [r0, #0]
    1994:	2f40      	cmp	r7, #64	; 0x40
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    1996:	eb09 00c6 	add.w	r0, r9, r6, lsl #3
    199a:	463a      	mov	r2, r7
    199c:	bf28      	it	cs
    199e:	2240      	movcs	r2, #64	; 0x40
    19a0:	6043      	str	r3, [r0, #4]
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    19a2:	0410      	lsls	r0, r2, #16
	data += size;
    19a4:	4413      	add	r3, r2
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    19a6:	f040 0088 	orr.w	r0, r0, #136	; 0x88
	ep0_tx_data_toggle ^= 1;
    19aa:	2101      	movs	r1, #1
	if (datalen == 0 && size < EP0_SIZE) return;
    19ac:	1aba      	subs	r2, r7, r2
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    19ae:	f849 0036 	str.w	r0, [r9, r6, lsl #3]
	ep0_tx_data_toggle ^= 1;
    19b2:	7021      	strb	r1, [r4, #0]
	if (datalen == 0 && size < EP0_SIZE) return;
    19b4:	d055      	beq.n	1a62 <usb_isr+0x5a6>
	ep0_tx_ptr = data;
    19b6:	602b      	str	r3, [r5, #0]
	ep0_tx_len = datalen;
    19b8:	4b41      	ldr	r3, [pc, #260]	; (1ac0 <usb_isr+0x604>)
    19ba:	801a      	strh	r2, [r3, #0]
		USB0_CTL = USB_CTL_USBENSOFEN; // clear TXSUSPENDTOKENBUSY bit
    19bc:	2301      	movs	r3, #1
    19be:	f88a 3094 	strb.w	r3, [sl, #148]	; 0x94
		break;
    19c2:	e606      	b.n	15d2 <usb_isr+0x116>
			switch (setup.wIndex) {
    19c4:	8892      	ldrh	r2, [r2, #4]
    19c6:	b95a      	cbnz	r2, 19e0 <usb_isr+0x524>
    19c8:	4a3e      	ldr	r2, [pc, #248]	; (1ac4 <usb_isr+0x608>)
    19ca:	1de5      	adds	r5, r4, #7
					*dst++ = *buf++;
    19cc:	f814 1b01 	ldrb.w	r1, [r4], #1
    19d0:	f802 1f01 	strb.w	r1, [r2, #1]!
				for (i=0; i<7; i++) {
    19d4:	42ac      	cmp	r4, r5
    19d6:	d1f9      	bne.n	19cc <usb_isr+0x510>
				if (line_coding[0] == 134) usb_reboot_timer = 15;
    19d8:	4a3b      	ldr	r2, [pc, #236]	; (1ac8 <usb_isr+0x60c>)
    19da:	6812      	ldr	r2, [r2, #0]
    19dc:	2a86      	cmp	r2, #134	; 0x86
    19de:	d03c      	beq.n	1a5a <usb_isr+0x59e>
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    19e0:	4c3a      	ldr	r4, [pc, #232]	; (1acc <usb_isr+0x610>)
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    19e2:	483b      	ldr	r0, [pc, #236]	; (1ad0 <usb_isr+0x614>)
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    19e4:	7825      	ldrb	r5, [r4, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    19e6:	7802      	ldrb	r2, [r0, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    19e8:	2d00      	cmp	r5, #0
	ep0_tx_data_toggle ^= 1;
    19ea:	f085 0501 	eor.w	r5, r5, #1
    19ee:	7025      	strb	r5, [r4, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    19f0:	f042 0402 	orr.w	r4, r2, #2
	ep0_tx_bdt_bank ^= 1;
    19f4:	f082 0201 	eor.w	r2, r2, #1
    19f8:	7002      	strb	r2, [r0, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    19fa:	eb09 02c4 	add.w	r2, r9, r4, lsl #3
    19fe:	f04f 0000 	mov.w	r0, #0
    1a02:	6050      	str	r0, [r2, #4]
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    1a04:	bf14      	ite	ne
    1a06:	22c8      	movne	r2, #200	; 0xc8
    1a08:	2288      	moveq	r2, #136	; 0x88
    1a0a:	f849 2034 	str.w	r2, [r9, r4, lsl #3]
}
    1a0e:	e5de      	b.n	15ce <usb_isr+0x112>
		USB0_CTL = USB_CTL_ODDRST;
    1a10:	4b2a      	ldr	r3, [pc, #168]	; (1abc <usb_isr+0x600>)
		ep0_tx_bdt_bank = 0;
    1a12:	482f      	ldr	r0, [pc, #188]	; (1ad0 <usb_isr+0x614>)
		table[index(0, RX, EVEN)].desc = BDT_DESC(EP0_SIZE, 0);
    1a14:	492f      	ldr	r1, [pc, #188]	; (1ad4 <usb_isr+0x618>)
    1a16:	4c30      	ldr	r4, [pc, #192]	; (1ad8 <usb_isr+0x61c>)
		USB0_CTL = USB_CTL_ODDRST;
    1a18:	2502      	movs	r5, #2
    1a1a:	f883 5094 	strb.w	r5, [r3, #148]	; 0x94
		ep0_tx_bdt_bank = 0;
    1a1e:	7002      	strb	r2, [r0, #0]
		table[index(0, RX, EVEN)].addr = ep0_rx0_buf;
    1a20:	482e      	ldr	r0, [pc, #184]	; (1adc <usb_isr+0x620>)
		table[index(0, RX, EVEN)].desc = BDT_DESC(EP0_SIZE, 0);
    1a22:	600c      	str	r4, [r1, #0]
		table[index(0, RX, ODD)].addr = ep0_rx1_buf;
    1a24:	4d2e      	ldr	r5, [pc, #184]	; (1ae0 <usb_isr+0x624>)
		table[index(0, RX, EVEN)].addr = ep0_rx0_buf;
    1a26:	6048      	str	r0, [r1, #4]
		table[index(0, RX, ODD)].desc = BDT_DESC(EP0_SIZE, 0);
    1a28:	608c      	str	r4, [r1, #8]
		USB0_ENDPT0 = USB_ENDPT_EPRXEN | USB_ENDPT_EPTXEN | USB_ENDPT_EPHSHK;
    1a2a:	240d      	movs	r4, #13
		table[index(0, RX, ODD)].addr = ep0_rx1_buf;
    1a2c:	60cd      	str	r5, [r1, #12]
		USB0_ERRSTAT = 0xFF;
    1a2e:	20ff      	movs	r0, #255	; 0xff
		table[index(0, TX, EVEN)].desc = 0;
    1a30:	610a      	str	r2, [r1, #16]
		table[index(0, TX, ODD)].desc = 0;
    1a32:	618a      	str	r2, [r1, #24]
		USB0_ENDPT0 = USB_ENDPT_EPRXEN | USB_ENDPT_EPTXEN | USB_ENDPT_EPHSHK;
    1a34:	f883 40c0 	strb.w	r4, [r3, #192]	; 0xc0
		USB0_CTL = USB_CTL_USBENSOFEN;
    1a38:	2101      	movs	r1, #1
		USB0_INTEN = USB_INTEN_TOKDNEEN |
    1a3a:	249f      	movs	r4, #159	; 0x9f
		USB0_ERRSTAT = 0xFF;
    1a3c:	f883 0088 	strb.w	r0, [r3, #136]	; 0x88
		USB0_ISTAT = 0xFF;
    1a40:	f883 0080 	strb.w	r0, [r3, #128]	; 0x80
		USB0_ADDR = 0;
    1a44:	f883 2098 	strb.w	r2, [r3, #152]	; 0x98
		USB0_ERREN = 0xFF;
    1a48:	f883 008c 	strb.w	r0, [r3, #140]	; 0x8c
		USB0_INTEN = USB_INTEN_TOKDNEEN |
    1a4c:	f883 4084 	strb.w	r4, [r3, #132]	; 0x84
		USB0_CTL = USB_CTL_USBENSOFEN;
    1a50:	f883 1094 	strb.w	r1, [r3, #148]	; 0x94
}
    1a54:	b005      	add	sp, #20
    1a56:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
				if (line_coding[0] == 134) usb_reboot_timer = 15;
    1a5a:	4a22      	ldr	r2, [pc, #136]	; (1ae4 <usb_isr+0x628>)
    1a5c:	200f      	movs	r0, #15
    1a5e:	7010      	strb	r0, [r2, #0]
    1a60:	e7be      	b.n	19e0 <usb_isr+0x524>
	if (datalen == 0 && size < EP0_SIZE) return;
    1a62:	2f3f      	cmp	r7, #63	; 0x3f
    1a64:	d8a7      	bhi.n	19b6 <usb_isr+0x4fa>
    1a66:	e690      	b.n	178a <usb_isr+0x2ce>
		i = setup.wIndex & 0x7F;
    1a68:	f007 077f 	and.w	r7, r7, #127	; 0x7f
		if (i > NUM_ENDPOINTS || setup.wValue != 0) {
    1a6c:	2f05      	cmp	r7, #5
    1a6e:	f63f aee8 	bhi.w	1842 <usb_isr+0x386>
    1a72:	0c00      	lsrs	r0, r0, #16
    1a74:	f47f aee5 	bne.w	1842 <usb_isr+0x386>
		(*(uint8_t *)(&USB0_ENDPT0 + i * 4)) &= ~0x02;
    1a78:	4a05      	ldr	r2, [pc, #20]	; (1a90 <usb_isr+0x5d4>)
    1a7a:	f812 3027 	ldrb.w	r3, [r2, r7, lsl #2]
    1a7e:	f023 0302 	bic.w	r3, r3, #2
    1a82:	f802 3027 	strb.w	r3, [r2, r7, lsl #2]
	uint32_t datalen = 0;
    1a86:	2200      	movs	r2, #0
	const uint8_t *data = NULL;
    1a88:	4613      	mov	r3, r2
		break;
    1a8a:	e64f      	b.n	172c <usb_isr+0x270>
    1a8c:	1fff0e14 	.word	0x1fff0e14
    1a90:	400720c0 	.word	0x400720c0
    1a94:	1fff0d88 	.word	0x1fff0d88
    1a98:	1fff1028 	.word	0x1fff1028
    1a9c:	1fff1024 	.word	0x1fff1024
    1aa0:	1fff0e79 	.word	0x1fff0e79
    1aa4:	1fff0e60 	.word	0x1fff0e60
    1aa8:	1fff0e74 	.word	0x1fff0e74
    1aac:	1fff0e7c 	.word	0x1fff0e7c
    1ab0:	1fff0e1c 	.word	0x1fff0e1c
    1ab4:	1fff0e30 	.word	0x1fff0e30
    1ab8:	1fff0e86 	.word	0x1fff0e86
    1abc:	******** 	.word	0x********
    1ac0:	1fff0e0e 	.word	0x1fff0e0e
    1ac4:	1fff101b 	.word	0x1fff101b
    1ac8:	1fff101c 	.word	0x1fff101c
    1acc:	1fff0e0d 	.word	0x1fff0e0d
    1ad0:	1fff0e0c 	.word	0x1fff0e0c
    1ad4:	1fff0000 	.word	0x1fff0000
    1ad8:	******** 	.word	0x********
    1adc:	1fff0d8c 	.word	0x1fff0d8c
    1ae0:	1fff0dcc 	.word	0x1fff0dcc
    1ae4:	1fff0e7a 	.word	0x1fff0e7a
    1ae8:	1fff0e4c 	.word	0x1fff0e4c
		reply_buffer[0] = usb_configuration;
    1aec:	4b48      	ldr	r3, [pc, #288]	; (1c10 <usb_isr+0x754>)
    1aee:	4949      	ldr	r1, [pc, #292]	; (1c14 <usb_isr+0x758>)
    1af0:	781b      	ldrb	r3, [r3, #0]
    1af2:	700b      	strb	r3, [r1, #0]
		data = reply_buffer;
    1af4:	460b      	mov	r3, r1
		break;
    1af6:	e619      	b.n	172c <usb_isr+0x270>
		reply_buffer[0] = 0;
    1af8:	4a46      	ldr	r2, [pc, #280]	; (1c14 <usb_isr+0x758>)
    1afa:	2300      	movs	r3, #0
    1afc:	8013      	strh	r3, [r2, #0]
		data = reply_buffer;
    1afe:	4b45      	ldr	r3, [pc, #276]	; (1c14 <usb_isr+0x758>)
		datalen = 2;
    1b00:	2202      	movs	r2, #2
		break;
    1b02:	e613      	b.n	172c <usb_isr+0x270>
				tx_state[i] = TX_STATE_BOTH_FREE_ODD_FIRST;
    1b04:	f04f 0301 	mov.w	r3, #1
    1b08:	7023      	strb	r3, [r4, #0]
		for (i=0; i < NUM_ENDPOINTS; i++) {
    1b0a:	4b43      	ldr	r3, [pc, #268]	; (1c18 <usb_isr+0x75c>)
    1b0c:	42b3      	cmp	r3, r6
    1b0e:	f104 0401 	add.w	r4, r4, #1
    1b12:	f47f aeca 	bne.w	18aa <usb_isr+0x3ee>
		usb_rx_memory_needed = 0;
    1b16:	4e41      	ldr	r6, [pc, #260]	; (1c1c <usb_isr+0x760>)
    1b18:	9c03      	ldr	r4, [sp, #12]
		cfg = usb_endpoint_config_table;
    1b1a:	f8df 8114 	ldr.w	r8, [pc, #276]	; 1c30 <usb_isr+0x774>
					table[index(i, RX, EVEN)].desc = BDT_DESC(64, 0);
    1b1e:	4f40      	ldr	r7, [pc, #256]	; (1c20 <usb_isr+0x764>)
		usb_rx_memory_needed = 0;
    1b20:	2300      	movs	r3, #0
    1b22:	7033      	strb	r3, [r6, #0]
    1b24:	2504      	movs	r5, #4
			epconf = *cfg++;
    1b26:	f818 3b01 	ldrb.w	r3, [r8], #1
			*reg = epconf;
    1b2a:	4a3e      	ldr	r2, [pc, #248]	; (1c24 <usb_isr+0x768>)
    1b2c:	54ab      	strb	r3, [r5, r2]
			if (epconf & USB_ENDPT_EPRXEN) {
    1b2e:	071b      	lsls	r3, r3, #28
    1b30:	d425      	bmi.n	1b7e <usb_isr+0x6c2>
			table[index(i, TX, EVEN)].desc = 0;
    1b32:	f045 0102 	orr.w	r1, r5, #2
			table[index(i, TX, ODD)].desc = 0;
    1b36:	f045 0203 	orr.w	r2, r5, #3
		for (i=1; i <= NUM_ENDPOINTS; i++) {
    1b3a:	3504      	adds	r5, #4
			table[index(i, TX, EVEN)].desc = 0;
    1b3c:	2300      	movs	r3, #0
		for (i=1; i <= NUM_ENDPOINTS; i++) {
    1b3e:	2d18      	cmp	r5, #24
			table[index(i, TX, EVEN)].desc = 0;
    1b40:	f849 3031 	str.w	r3, [r9, r1, lsl #3]
			table[index(i, TX, ODD)].desc = 0;
    1b44:	f849 3032 	str.w	r3, [r9, r2, lsl #3]
		for (i=1; i <= NUM_ENDPOINTS; i++) {
    1b48:	d1ed      	bne.n	1b26 <usb_isr+0x66a>
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    1b4a:	4f37      	ldr	r7, [pc, #220]	; (1c28 <usb_isr+0x76c>)
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    1b4c:	7825      	ldrb	r5, [r4, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    1b4e:	783a      	ldrb	r2, [r7, #0]
    1b50:	f042 0002 	orr.w	r0, r2, #2
    1b54:	eb09 0cc0 	add.w	ip, r9, r0, lsl #3
	ep0_tx_data_toggle ^= 1;
    1b58:	f085 0601 	eor.w	r6, r5, #1
	table[index(0, TX, ep0_tx_bdt_bank)].addr = (void *)data;
    1b5c:	f8cc 3004 	str.w	r3, [ip, #4]
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    1b60:	2d00      	cmp	r5, #0
    1b62:	d14d      	bne.n	1c00 <usb_isr+0x744>
    1b64:	2388      	movs	r3, #136	; 0x88
	ep0_tx_bdt_bank ^= 1;
    1b66:	f082 0201 	eor.w	r2, r2, #1
	ep0_tx_data_toggle ^= 1;
    1b6a:	7026      	strb	r6, [r4, #0]
	ep0_tx_bdt_bank ^= 1;
    1b6c:	703a      	strb	r2, [r7, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    1b6e:	f849 3030 	str.w	r3, [r9, r0, lsl #3]
		USB0_CTL = USB_CTL_USBENSOFEN; // clear TXSUSPENDTOKENBUSY bit
    1b72:	2301      	movs	r3, #1
    1b74:	f88a 3094 	strb.w	r3, [sl, #148]	; 0x94
		break;
    1b78:	e52b      	b.n	15d2 <usb_isr+0x116>
				tx_state[i] = TX_STATE_BOTH_FREE_EVEN_FIRST;
    1b7a:	7025      	strb	r5, [r4, #0]
				break;
    1b7c:	e7c5      	b.n	1b0a <usb_isr+0x64e>
				p = usb_malloc();
    1b7e:	f000 f8bb 	bl	1cf8 <usb_malloc>
				if (p) {
    1b82:	2800      	cmp	r0, #0
    1b84:	d032      	beq.n	1bec <usb_isr+0x730>
					table[index(i, RX, EVEN)].addr = p->buf;
    1b86:	eb09 03c5 	add.w	r3, r9, r5, lsl #3
    1b8a:	3008      	adds	r0, #8
    1b8c:	6058      	str	r0, [r3, #4]
					table[index(i, RX, EVEN)].desc = BDT_DESC(64, 0);
    1b8e:	f849 7035 	str.w	r7, [r9, r5, lsl #3]
				p = usb_malloc();
    1b92:	f000 f8b1 	bl	1cf8 <usb_malloc>
				if (p) {
    1b96:	b308      	cbz	r0, 1bdc <usb_isr+0x720>
					table[index(i, RX, ODD)].addr = p->buf;
    1b98:	f045 0301 	orr.w	r3, r5, #1
    1b9c:	eb09 02c3 	add.w	r2, r9, r3, lsl #3
    1ba0:	3008      	adds	r0, #8
    1ba2:	6050      	str	r0, [r2, #4]
					table[index(i, RX, ODD)].desc = BDT_DESC(64, 1);
    1ba4:	f849 b033 	str.w	fp, [r9, r3, lsl #3]
    1ba8:	e7c3      	b.n	1b32 <usb_isr+0x676>
			if (list->addr == NULL) break;
    1baa:	4a20      	ldr	r2, [pc, #128]	; (1c2c <usb_isr+0x770>)
    1bac:	6853      	ldr	r3, [r2, #4]
    1bae:	2b00      	cmp	r3, #0
    1bb0:	f43f ae47 	beq.w	1842 <usb_isr+0x386>
    1bb4:	0c00      	lsrs	r0, r0, #16
			if (setup.wValue == list->wValue && setup.wIndex == list->wIndex) {
    1bb6:	b2bf      	uxth	r7, r7
    1bb8:	e004      	b.n	1bc4 <usb_isr+0x708>
			if (list->addr == NULL) break;
    1bba:	6913      	ldr	r3, [r2, #16]
		for (list = usb_descriptor_list; 1; list++) {
    1bbc:	320c      	adds	r2, #12
			if (list->addr == NULL) break;
    1bbe:	2b00      	cmp	r3, #0
    1bc0:	f43f ae3f 	beq.w	1842 <usb_isr+0x386>
			if (setup.wValue == list->wValue && setup.wIndex == list->wIndex) {
    1bc4:	8811      	ldrh	r1, [r2, #0]
    1bc6:	4281      	cmp	r1, r0
    1bc8:	d1f7      	bne.n	1bba <usb_isr+0x6fe>
    1bca:	8851      	ldrh	r1, [r2, #2]
    1bcc:	42b9      	cmp	r1, r7
    1bce:	d1f4      	bne.n	1bba <usb_isr+0x6fe>
				if ((setup.wValue >> 8) == 3) {
    1bd0:	0a00      	lsrs	r0, r0, #8
    1bd2:	2803      	cmp	r0, #3
					datalen = *(list->addr);
    1bd4:	bf0c      	ite	eq
    1bd6:	781a      	ldrbeq	r2, [r3, #0]
					datalen = list->length;
    1bd8:	8912      	ldrhne	r2, [r2, #8]
    1bda:	e5a7      	b.n	172c <usb_isr+0x270>
					usb_rx_memory_needed++;
    1bdc:	7833      	ldrb	r3, [r6, #0]
    1bde:	3301      	adds	r3, #1
    1be0:	7033      	strb	r3, [r6, #0]
					table[index(i, RX, ODD)].desc = 0;
    1be2:	f045 0301 	orr.w	r3, r5, #1
    1be6:	f849 0033 	str.w	r0, [r9, r3, lsl #3]
					usb_rx_memory_needed++;
    1bea:	e7a2      	b.n	1b32 <usb_isr+0x676>
					usb_rx_memory_needed++;
    1bec:	7833      	ldrb	r3, [r6, #0]
					table[index(i, RX, EVEN)].desc = 0;
    1bee:	f849 0035 	str.w	r0, [r9, r5, lsl #3]
					usb_rx_memory_needed++;
    1bf2:	3301      	adds	r3, #1
    1bf4:	7033      	strb	r3, [r6, #0]
    1bf6:	e7cc      	b.n	1b92 <usb_isr+0x6d6>
		data = reply_buffer;
    1bf8:	4b06      	ldr	r3, [pc, #24]	; (1c14 <usb_isr+0x758>)
		datalen = 2;
    1bfa:	2202      	movs	r2, #2
    1bfc:	e596      	b.n	172c <usb_isr+0x270>
	__asm__ volatile("bkpt");
    1bfe:	be00      	bkpt	0x0000
	ep0_tx_data_toggle ^= 1;
    1c00:	7026      	strb	r6, [r4, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    1c02:	23c8      	movs	r3, #200	; 0xc8
	ep0_tx_bdt_bank ^= 1;
    1c04:	f082 0601 	eor.w	r6, r2, #1
    1c08:	703e      	strb	r6, [r7, #0]
	table[index(0, TX, ep0_tx_bdt_bank)].desc = BDT_DESC(len, ep0_tx_data_toggle);
    1c0a:	f849 3030 	str.w	r3, [r9, r0, lsl #3]
	if (datalen == 0 && size < EP0_SIZE) return;
    1c0e:	e5bc      	b.n	178a <usb_isr+0x2ce>
    1c10:	1fff0e79 	.word	0x1fff0e79
    1c14:	1fff0e14 	.word	0x1fff0e14
    1c18:	1fff0e30 	.word	0x1fff0e30
    1c1c:	1fff0e86 	.word	0x1fff0e86
    1c20:	******** 	.word	0x********
    1c24:	400720c0 	.word	0x400720c0
    1c28:	1fff0e0c 	.word	0x1fff0e0c
    1c2c:	00002bb0 	.word	0x00002bb0
    1c30:	00002c10 	.word	0x00002c10

00001c34 <usb_init>:



void usb_init(void)
{
    1c34:	b510      	push	{r4, lr}
	int i;

	//serial_begin(BAUD2DIV(115200));
	//serial_print("usb_init\n");

	usb_init_serialnumber();
    1c36:	f000 fd13 	bl	2660 <usb_init_serialnumber>

	for (i=0; i < (NUM_ENDPOINTS+1)*4; i++) {
    1c3a:	2300      	movs	r3, #0
    1c3c:	4a1e      	ldr	r2, [pc, #120]	; (1cb8 <usb_init+0x84>)
		table[i].desc = 0;
    1c3e:	4619      	mov	r1, r3
    1c40:	f842 1033 	str.w	r1, [r2, r3, lsl #3]
		table[i].addr = 0;
    1c44:	eb02 00c3 	add.w	r0, r2, r3, lsl #3
	for (i=0; i < (NUM_ENDPOINTS+1)*4; i++) {
    1c48:	3301      	adds	r3, #1
    1c4a:	2b18      	cmp	r3, #24
		table[i].addr = 0;
    1c4c:	6041      	str	r1, [r0, #4]
	for (i=0; i < (NUM_ENDPOINTS+1)*4; i++) {
    1c4e:	d1f7      	bne.n	1c40 <usb_init+0xc>
	// this basically follows the flowchart in the Kinetis
	// Quick Reference User Guide, Rev. 1, 03/2012, page 141

	// assume 48 MHz clock already running
	// SIM - enable clock
	SIM_SCGC4 |= SIM_SCGC4_USBOTG;
    1c50:	481a      	ldr	r0, [pc, #104]	; (1cbc <usb_init+0x88>)
#ifdef HAS_KINETIS_MPU
	MPU_RGDAAC0 |= 0x03000000;
    1c52:	4c1b      	ldr	r4, [pc, #108]	; (1cc0 <usb_init+0x8c>)
	SIM_SCGC4 |= SIM_SCGC4_USBOTG;
    1c54:	6b43      	ldr	r3, [r0, #52]	; 0x34
    1c56:	f443 2380 	orr.w	r3, r3, #262144	; 0x40000
    1c5a:	6343      	str	r3, [r0, #52]	; 0x34
	MPU_RGDAAC0 |= 0x03000000;
    1c5c:	f8d4 0800 	ldr.w	r0, [r4, #2048]	; 0x800
	// reset USB module
	//USB0_USBTRC0 = USB_USBTRC_USBRESET;
	//while ((USB0_USBTRC0 & USB_USBTRC_USBRESET) != 0) ; // wait for reset to end

	// set desc table base addr
	USB0_BDTPAGE1 = ((uint32_t)table) >> 8;
    1c60:	4b18      	ldr	r3, [pc, #96]	; (1cc4 <usb_init+0x90>)
	MPU_RGDAAC0 |= 0x03000000;
    1c62:	f040 7040 	orr.w	r0, r0, #50331648	; 0x3000000
    1c66:	f8c4 0800 	str.w	r0, [r4, #2048]	; 0x800
	USB0_BDTPAGE1 = ((uint32_t)table) >> 8;
    1c6a:	f3c2 2c07 	ubfx	ip, r2, #8, #8
	USB0_BDTPAGE2 = ((uint32_t)table) >> 16;
    1c6e:	f3c2 4407 	ubfx	r4, r2, #16, #8
	USB0_BDTPAGE3 = ((uint32_t)table) >> 24;
    1c72:	0e12      	lsrs	r2, r2, #24
	USB0_BDTPAGE1 = ((uint32_t)table) >> 8;
    1c74:	f883 c09c 	strb.w	ip, [r3, #156]	; 0x9c
	USB0_BDTPAGE2 = ((uint32_t)table) >> 16;
    1c78:	f883 40b0 	strb.w	r4, [r3, #176]	; 0xb0
	USB0_BDTPAGE3 = ((uint32_t)table) >> 24;
    1c7c:	f883 20b4 	strb.w	r2, [r3, #180]	; 0xb4

	//USB0_USBTRC0 |= 0x40; // undocumented bit

	// enable USB
	USB0_CTL = USB_CTL_USBENSOFEN;
	USB0_USBCTRL = 0;
    1c80:	4a11      	ldr	r2, [pc, #68]	; (1cc8 <usb_init+0x94>)
	USB0_ISTAT = 0xFF;
    1c82:	20ff      	movs	r0, #255	; 0xff
	USB0_CTL = USB_CTL_USBENSOFEN;
    1c84:	f04f 0c01 	mov.w	ip, #1
	USB0_ISTAT = 0xFF;
    1c88:	f883 0080 	strb.w	r0, [r3, #128]	; 0x80
	USB0_ERRSTAT = 0xFF;
    1c8c:	f883 0088 	strb.w	r0, [r3, #136]	; 0x88
	USB0_OTGISTAT = 0xFF;
    1c90:	7418      	strb	r0, [r3, #16]
	USB0_CTL = USB_CTL_USBENSOFEN;
    1c92:	f883 c094 	strb.w	ip, [r3, #148]	; 0x94
	USB0_USBCTRL = 0;
    1c96:	7011      	strb	r1, [r2, #0]

	// enable reset interrupt
	USB0_INTEN = USB_INTEN_USBRSTEN;

	// enable interrupt in NVIC...
	NVIC_SET_PRIORITY(IRQ_USBOTG, 112);
    1c98:	490c      	ldr	r1, [pc, #48]	; (1ccc <usb_init+0x98>)
	USB0_INTEN = USB_INTEN_USBRSTEN;
    1c9a:	f883 c084 	strb.w	ip, [r3, #132]	; 0x84
	NVIC_SET_PRIORITY(IRQ_USBOTG, 112);
    1c9e:	2070      	movs	r0, #112	; 0x70
    1ca0:	f881 0035 	strb.w	r0, [r1, #53]	; 0x35
	NVIC_ENABLE_IRQ(IRQ_USBOTG);
    1ca4:	f04f 21e0 	mov.w	r1, #3758153728	; 0xe000e000
    1ca8:	f44f 1000 	mov.w	r0, #2097152	; 0x200000

	// enable d+ pullup
	USB0_CONTROL = USB_CONTROL_DPPULLUPNONOTG;
    1cac:	2310      	movs	r3, #16
	NVIC_ENABLE_IRQ(IRQ_USBOTG);
    1cae:	f8c1 0104 	str.w	r0, [r1, #260]	; 0x104
	USB0_CONTROL = USB_CONTROL_DPPULLUPNONOTG;
    1cb2:	7213      	strb	r3, [r2, #8]
}
    1cb4:	bd10      	pop	{r4, pc}
    1cb6:	bf00      	nop
    1cb8:	1fff0000 	.word	0x1fff0000
    1cbc:	40048000 	.word	0x40048000
    1cc0:	4000d000 	.word	0x4000d000
    1cc4:	******** 	.word	0x********
    1cc8:	40072100 	.word	0x40072100
    1ccc:	e000e400 	.word	0xe000e400

00001cd0 <usb_serial_class::clear()>:
        virtual void clear(void) { usb_serial_flush_input(); }
    1cd0:	f000 bafe 	b.w	22d0 <usb_serial_flush_input>

00001cd4 <usb_serial_class::peek()>:
        virtual int peek() { return usb_serial_peekchar(); }
    1cd4:	f000 bace 	b.w	2274 <usb_serial_peekchar>

00001cd8 <usb_serial_class::read()>:
        virtual int read() { return usb_serial_getchar(); }
    1cd8:	f000 baa8 	b.w	222c <usb_serial_getchar>

00001cdc <usb_serial_class::available()>:
        virtual int available() { return usb_serial_available(); }
    1cdc:	f000 bae2 	b.w	22a4 <usb_serial_available>

00001ce0 <usb_serial_class::flush()>:
        virtual void flush() { usb_serial_flush_output(); }  // TODO: actually wait for data to leave USB...
    1ce0:	f000 bbc8 	b.w	2474 <usb_serial_flush_output>

00001ce4 <usb_serial_class::availableForWrite()>:
	virtual int availableForWrite() { return usb_serial_write_buffer_free(); }
    1ce4:	f000 bba2 	b.w	242c <usb_serial_write_buffer_free>

00001ce8 <usb_serial_class::write(unsigned char const*, unsigned int)>:
        virtual size_t write(const uint8_t *buffer, size_t size) { return usb_serial_write(buffer, size); }
    1ce8:	4608      	mov	r0, r1
    1cea:	4611      	mov	r1, r2
    1cec:	f000 bb0c 	b.w	2308 <usb_serial_write>

00001cf0 <usb_serial_class::write(unsigned char)>:
        virtual size_t write(uint8_t c) { return usb_serial_putchar(c); }
    1cf0:	4608      	mov	r0, r1
    1cf2:	f000 bb8d 	b.w	2410 <usb_serial_putchar>
    1cf6:	bf00      	nop

00001cf8 <usb_malloc>:
usb_packet_t * usb_malloc(void)
{
	unsigned int n, avail;
	uint8_t *p;

	__disable_irq();
    1cf8:	b672      	cpsid	i
	avail = usb_buffer_available;
    1cfa:	490d      	ldr	r1, [pc, #52]	; (1d30 <usb_malloc+0x38>)
    1cfc:	680b      	ldr	r3, [r1, #0]
	n = __builtin_clz(avail); // clz = count leading zeros
    1cfe:	fab3 f083 	clz	r0, r3
	if (n >= NUM_USB_BUFFERS) {
    1d02:	281d      	cmp	r0, #29
    1d04:	dc10      	bgt.n	1d28 <usb_malloc+0x30>
	}
	//serial_print("malloc:");
	//serial_phex(n);
	//serial_print("\n");

	usb_buffer_available = avail & ~(0x80000000 >> n);
    1d06:	f04f 4200 	mov.w	r2, #2147483648	; 0x80000000
    1d0a:	40c2      	lsrs	r2, r0
    1d0c:	ea23 0302 	bic.w	r3, r3, r2
    1d10:	600b      	str	r3, [r1, #0]
	__enable_irq();
    1d12:	b662      	cpsie	i
	p = usb_buffer_memory + (n * sizeof(usb_packet_t));
    1d14:	4a07      	ldr	r2, [pc, #28]	; (1d34 <usb_malloc+0x3c>)
    1d16:	eb00 00c0 	add.w	r0, r0, r0, lsl #3
    1d1a:	00c1      	lsls	r1, r0, #3
    1d1c:	eb02 00c0 	add.w	r0, r2, r0, lsl #3
	//serial_print("malloc:");
	//serial_phex32((int)p);
	//serial_print("\n");
	*(uint32_t *)p = 0;
    1d20:	2300      	movs	r3, #0
    1d22:	5053      	str	r3, [r2, r1]
	*(uint32_t *)(p + 4) = 0;
    1d24:	6043      	str	r3, [r0, #4]
	return (usb_packet_t *)p;
}
    1d26:	4770      	bx	lr
		__enable_irq();
    1d28:	b662      	cpsie	i
		return NULL;
    1d2a:	2000      	movs	r0, #0
    1d2c:	4770      	bx	lr
    1d2e:	bf00      	nop
    1d30:	1fff0c1c 	.word	0x1fff0c1c
    1d34:	1fff0398 	.word	0x1fff0398

00001d38 <usb_free>:
void usb_free(usb_packet_t *p)
{
	unsigned int n, mask;

	//serial_print("free:");
	n = ((uint8_t *)p - usb_buffer_memory) / sizeof(usb_packet_t);
    1d38:	4b0e      	ldr	r3, [pc, #56]	; (1d74 <usb_free+0x3c>)
    1d3a:	1ac3      	subs	r3, r0, r3
	if (n >= NUM_USB_BUFFERS) return;
    1d3c:	f5b3 6f07 	cmp.w	r3, #2160	; 0x870
    1d40:	d210      	bcs.n	1d64 <usb_free+0x2c>
	//serial_phex(n);
	//serial_print("\n");

	// if any endpoints are starving for memory to receive
	// packets, give this memory to them immediately!
	if (usb_rx_memory_needed && usb_configuration) {
    1d42:	4a0d      	ldr	r2, [pc, #52]	; (1d78 <usb_free+0x40>)
    1d44:	7812      	ldrb	r2, [r2, #0]
    1d46:	b972      	cbnz	r2, 1d66 <usb_free+0x2e>
		usb_rx_memory(p);
		return;
	}

	mask = (0x80000000 >> n);
	__disable_irq();
    1d48:	b672      	cpsid	i
	n = ((uint8_t *)p - usb_buffer_memory) / sizeof(usb_packet_t);
    1d4a:	4a0c      	ldr	r2, [pc, #48]	; (1d7c <usb_free+0x44>)
	usb_buffer_available |= mask;
    1d4c:	490c      	ldr	r1, [pc, #48]	; (1d80 <usb_free+0x48>)
	n = ((uint8_t *)p - usb_buffer_memory) / sizeof(usb_packet_t);
    1d4e:	fba2 2303 	umull	r2, r3, r2, r3
	usb_buffer_available |= mask;
    1d52:	6808      	ldr	r0, [r1, #0]
	mask = (0x80000000 >> n);
    1d54:	f04f 4200 	mov.w	r2, #2147483648	; 0x80000000
	n = ((uint8_t *)p - usb_buffer_memory) / sizeof(usb_packet_t);
    1d58:	091b      	lsrs	r3, r3, #4
	mask = (0x80000000 >> n);
    1d5a:	fa22 f303 	lsr.w	r3, r2, r3
	usb_buffer_available |= mask;
    1d5e:	4303      	orrs	r3, r0
    1d60:	600b      	str	r3, [r1, #0]
	__enable_irq();
    1d62:	b662      	cpsie	i

	//serial_print("free:");
	//serial_phex32((int)p);
	//serial_print("\n");
}
    1d64:	4770      	bx	lr
	if (usb_rx_memory_needed && usb_configuration) {
    1d66:	4a07      	ldr	r2, [pc, #28]	; (1d84 <usb_free+0x4c>)
    1d68:	7812      	ldrb	r2, [r2, #0]
    1d6a:	2a00      	cmp	r2, #0
    1d6c:	d0ec      	beq.n	1d48 <usb_free+0x10>
		usb_rx_memory(p);
    1d6e:	f7ff bb1f 	b.w	13b0 <usb_rx_memory>
    1d72:	bf00      	nop
    1d74:	1fff0398 	.word	0x1fff0398
    1d78:	1fff0e86 	.word	0x1fff0e86
    1d7c:	38e38e39 	.word	0x38e38e39
    1d80:	1fff0c1c 	.word	0x1fff0c1c
    1d84:	1fff0e79 	.word	0x1fff0e79

00001d88 <sysex_byte>:
		tx_noautoflush = 0;
	}
}

static void sysex_byte(uint8_t b)
{
    1d88:	b538      	push	{r3, r4, r5, lr}
	if (usb_midi_handleSysExPartial && usb_midi_msg_sysex_len >= USB_MIDI_SYSEX_MAX) {
    1d8a:	4b0c      	ldr	r3, [pc, #48]	; (1dbc <sysex_byte+0x34>)
    1d8c:	4d0c      	ldr	r5, [pc, #48]	; (1dc0 <sysex_byte+0x38>)
    1d8e:	681b      	ldr	r3, [r3, #0]
    1d90:	8829      	ldrh	r1, [r5, #0]
{
    1d92:	4604      	mov	r4, r0
	if (usb_midi_handleSysExPartial && usb_midi_msg_sysex_len >= USB_MIDI_SYSEX_MAX) {
    1d94:	b143      	cbz	r3, 1da8 <sysex_byte+0x20>
    1d96:	f5b1 7f91 	cmp.w	r1, #290	; 0x122
    1d9a:	d209      	bcs.n	1db0 <sysex_byte+0x28>
		// when buffer is full, send another chunk to partial handler.
		(*usb_midi_handleSysExPartial)(usb_midi_msg_sysex, usb_midi_msg_sysex_len, 0);
		usb_midi_msg_sysex_len = 0;
	}
	if (usb_midi_msg_sysex_len < USB_MIDI_SYSEX_MAX) {
		usb_midi_msg_sysex[usb_midi_msg_sysex_len++] = b;
    1d9c:	1c4b      	adds	r3, r1, #1
    1d9e:	b29b      	uxth	r3, r3
    1da0:	4a08      	ldr	r2, [pc, #32]	; (1dc4 <sysex_byte+0x3c>)
    1da2:	802b      	strh	r3, [r5, #0]
    1da4:	5454      	strb	r4, [r2, r1]
	}
}
    1da6:	bd38      	pop	{r3, r4, r5, pc}
	if (usb_midi_msg_sysex_len < USB_MIDI_SYSEX_MAX) {
    1da8:	f5b1 7f91 	cmp.w	r1, #290	; 0x122
    1dac:	d3f6      	bcc.n	1d9c <sysex_byte+0x14>
}
    1dae:	bd38      	pop	{r3, r4, r5, pc}
		(*usb_midi_handleSysExPartial)(usb_midi_msg_sysex, usb_midi_msg_sysex_len, 0);
    1db0:	4804      	ldr	r0, [pc, #16]	; (1dc4 <sysex_byte+0x3c>)
    1db2:	2200      	movs	r2, #0
    1db4:	4798      	blx	r3
    1db6:	2100      	movs	r1, #0
    1db8:	2301      	movs	r3, #1
    1dba:	e7f1      	b.n	1da0 <sysex_byte+0x18>
    1dbc:	1fff0ed0 	.word	0x1fff0ed0
    1dc0:	1fff100a 	.word	0x1fff100a
    1dc4:	1fff0ee8 	.word	0x1fff0ee8

00001dc8 <usb_midi_flush_output>:
{
    1dc8:	b570      	push	{r4, r5, r6, lr}
	if (tx_noautoflush == 0) {
    1dca:	4c0b      	ldr	r4, [pc, #44]	; (1df8 <usb_midi_flush_output+0x30>)
    1dcc:	7825      	ldrb	r5, [r4, #0]
    1dce:	b945      	cbnz	r5, 1de2 <usb_midi_flush_output+0x1a>
		if (tx_packet && tx_packet->index > 0) {
    1dd0:	4e0a      	ldr	r6, [pc, #40]	; (1dfc <usb_midi_flush_output+0x34>)
    1dd2:	6831      	ldr	r1, [r6, #0]
		tx_noautoflush = 1;
    1dd4:	2301      	movs	r3, #1
    1dd6:	7023      	strb	r3, [r4, #0]
		if (tx_packet && tx_packet->index > 0) {
    1dd8:	b109      	cbz	r1, 1dde <usb_midi_flush_output+0x16>
    1dda:	884b      	ldrh	r3, [r1, #2]
    1ddc:	b913      	cbnz	r3, 1de4 <usb_midi_flush_output+0x1c>
		tx_noautoflush = 0;
    1dde:	2300      	movs	r3, #0
    1de0:	7023      	strb	r3, [r4, #0]
}
    1de2:	bd70      	pop	{r4, r5, r6, pc}
			tx_packet->len = tx_packet->index * 4;
    1de4:	009b      	lsls	r3, r3, #2
    1de6:	800b      	strh	r3, [r1, #0]
			usb_tx(MIDI_TX_ENDPOINT, tx_packet);
    1de8:	2004      	movs	r0, #4
    1dea:	f7ff fb23 	bl	1434 <usb_tx>
		tx_noautoflush = 0;
    1dee:	2300      	movs	r3, #0
			tx_packet = NULL;
    1df0:	6035      	str	r5, [r6, #0]
		tx_noautoflush = 0;
    1df2:	7023      	strb	r3, [r4, #0]
    1df4:	e7f5      	b.n	1de2 <usb_midi_flush_output+0x1a>
    1df6:	bf00      	nop
    1df8:	1fff0e8c 	.word	0x1fff0e8c
    1dfc:	1fff0e90 	.word	0x1fff0e90

00001e00 <usb_midi_read>:
	}
	return n;
}

int usb_midi_read(uint32_t channel)
{
    1e00:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
	uint32_t n, index, ch, type1, type2, b1;

	if (!rx_packet) {
    1e04:	4eac      	ldr	r6, [pc, #688]	; (20b8 <usb_midi_read+0x2b8>)
    1e06:	6833      	ldr	r3, [r6, #0]
{
    1e08:	4607      	mov	r7, r0
	if (!rx_packet) {
    1e0a:	2b00      	cmp	r3, #0
    1e0c:	d063      	beq.n	1ed6 <usb_midi_read+0xd6>
	n = ((uint32_t *)rx_packet->buf)[index/4];
	//serial_print("midi rx, n=");
	//serial_phex32(n);
	//serial_print("\n");
	index += 4;
	if (index < rx_packet->len) {
    1e0e:	881c      	ldrh	r4, [r3, #0]
	index = rx_packet->index;
    1e10:	885a      	ldrh	r2, [r3, #2]
	n = ((uint32_t *)rx_packet->buf)[index/4];
    1e12:	f022 0103 	bic.w	r1, r2, #3
    1e16:	4419      	add	r1, r3
	index += 4;
    1e18:	3204      	adds	r2, #4
	if (index < rx_packet->len) {
    1e1a:	4294      	cmp	r4, r2
	n = ((uint32_t *)rx_packet->buf)[index/4];
    1e1c:	688d      	ldr	r5, [r1, #8]
	if (index < rx_packet->len) {
    1e1e:	d96d      	bls.n	1efc <usb_midi_read+0xfc>
		rx_packet->index = index;
    1e20:	805a      	strh	r2, [r3, #2]
		rx_packet = usb_rx(MIDI_RX_ENDPOINT);
	}
	type1 = n & 15;
	type2 = (n >> 12) & 15;
	b1 = (n >> 8) & 0xFF;
	ch = (b1 & 15) + 1;
    1e22:	f3c5 2303 	ubfx	r3, r5, #8, #4
	usb_midi_msg_cable = (n >> 4) & 15;
    1e26:	4aa5      	ldr	r2, [pc, #660]	; (20bc <usb_midi_read+0x2bc>)
	type1 = n & 15;
    1e28:	f005 060f 	and.w	r6, r5, #15
	ch = (b1 & 15) + 1;
    1e2c:	1c5c      	adds	r4, r3, #1
	usb_midi_msg_cable = (n >> 4) & 15;
    1e2e:	f3c5 1303 	ubfx	r3, r5, #4, #4
    1e32:	7013      	strb	r3, [r2, #0]
	if (type1 >= 0x08 && type1 <= 0x0E) {
    1e34:	f1a6 0308 	sub.w	r3, r6, #8
    1e38:	2b06      	cmp	r3, #6
	b1 = (n >> 8) & 0xFF;
    1e3a:	ea4f 2815 	mov.w	r8, r5, lsr #8
	if (type1 >= 0x08 && type1 <= 0x0E) {
    1e3e:	d914      	bls.n	1e6a <usb_midi_read+0x6a>
		usb_midi_msg_channel = ch;
		usb_midi_msg_data1 = (n >> 16);
		usb_midi_msg_data2 = (n >> 24);
		return 1;
	}
	if (type1 == 0x02 || type1 == 0x03 || (type1 == 0x05 && b1 >= 0xF1 && b1 != 0xF7)) {
    1e40:	1eb3      	subs	r3, r6, #2
    1e42:	2b01      	cmp	r3, #1
	b1 = (n >> 8) & 0xFF;
    1e44:	fa5f f088 	uxtb.w	r0, r8
	if (type1 == 0x02 || type1 == 0x03 || (type1 == 0x05 && b1 >= 0xF1 && b1 != 0xF7)) {
    1e48:	d960      	bls.n	1f0c <usb_midi_read+0x10c>
    1e4a:	2e05      	cmp	r6, #5
    1e4c:	f000 80b6 	beq.w	1fbc <usb_midi_read+0x1bc>
			return 0; // unknown message, ignore it
		}
		usb_midi_msg_type = b1;
		goto return_message;
	}
	if (type1 == 0x04) {
    1e50:	2e04      	cmp	r6, #4
    1e52:	f000 819a 	beq.w	218a <usb_midi_read+0x38a>
		sysex_byte(n >> 8);
		sysex_byte(n >> 16);
		sysex_byte(n >> 24);
		return 0;
	}
	if (type1 >= 0x05 && type1 <= 0x07) {
    1e56:	1f73      	subs	r3, r6, #5
    1e58:	2b02      	cmp	r3, #2
    1e5a:	f240 81a1 	bls.w	21a0 <usb_midi_read+0x3a0>
		} else if (usb_midi_handleSysExComplete) {
			(*usb_midi_handleSysExComplete)(usb_midi_msg_sysex, len);
		}
		return 1;
	}
	if (type1 == 0x0F) {
    1e5e:	2e0f      	cmp	r6, #15
    1e60:	f000 80e9 	beq.w	2036 <usb_midi_read+0x236>
		switch (b1) {
    1e64:	2000      	movs	r0, #0
			// send bytes in the middle of a SYSEX message.
			sysex_byte(b1);
		}
	}
	return 0;
}
    1e66:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
		if (channel && channel != ch) {
    1e6a:	b10f      	cbz	r7, 1e70 <usb_midi_read+0x70>
    1e6c:	42bc      	cmp	r4, r7
    1e6e:	d1f9      	bne.n	1e64 <usb_midi_read+0x64>
		if (type1 == 0x08 && type2 == 0x08) {
    1e70:	2e08      	cmp	r6, #8
	type2 = (n >> 12) & 15;
    1e72:	f3c5 3303 	ubfx	r3, r5, #12, #4
		if (type1 == 0x08 && type2 == 0x08) {
    1e76:	d06d      	beq.n	1f54 <usb_midi_read+0x154>
		if (type1 == 0x09 && type2 == 0x09) {
    1e78:	2e09      	cmp	r6, #9
    1e7a:	d07d      	beq.n	1f78 <usb_midi_read+0x178>
		if (type1 == 0x0A && type2 == 0x0A) {
    1e7c:	2e0a      	cmp	r6, #10
    1e7e:	f000 808d 	beq.w	1f9c <usb_midi_read+0x19c>
		if (type1 == 0x0B && type2 == 0x0B) {
    1e82:	2e0b      	cmp	r6, #11
    1e84:	f000 8092 	beq.w	1fac <usb_midi_read+0x1ac>
		if (type1 == 0x0C && type2 == 0x0C) {
    1e88:	2e0c      	cmp	r6, #12
    1e8a:	f000 80b3 	beq.w	1ff4 <usb_midi_read+0x1f4>
		if (type1 == 0x0D && type2 == 0x0D) {
    1e8e:	2e0d      	cmp	r6, #13
    1e90:	f000 80c3 	beq.w	201a <usb_midi_read+0x21a>
		if (type1 == 0x0E && type2 == 0x0E) {
    1e94:	2b0e      	cmp	r3, #14
    1e96:	d1e5      	bne.n	1e64 <usb_midi_read+0x64>
			if (usb_midi_handlePitchChange) {
    1e98:	4b89      	ldr	r3, [pc, #548]	; (20c0 <usb_midi_read+0x2c0>)
			usb_midi_msg_type = 0xE0;		// 0xE0 = usbMIDI.PitchBend
    1e9a:	4a8a      	ldr	r2, [pc, #552]	; (20c4 <usb_midi_read+0x2c4>)
			if (usb_midi_handlePitchChange) {
    1e9c:	681b      	ldr	r3, [r3, #0]
			usb_midi_msg_type = 0xE0;		// 0xE0 = usbMIDI.PitchBend
    1e9e:	21e0      	movs	r1, #224	; 0xe0
    1ea0:	7011      	strb	r1, [r2, #0]
			if (usb_midi_handlePitchChange) {
    1ea2:	2b00      	cmp	r3, #0
    1ea4:	f000 819b 	beq.w	21de <usb_midi_read+0x3de>
				int value = ((n >> 16) & 0x7F) | ((n >> 17) & 0x3F80);
    1ea8:	0c69      	lsrs	r1, r5, #17
    1eaa:	f3c5 4206 	ubfx	r2, r5, #16, #7
    1eae:	f401 517e 	and.w	r1, r1, #16256	; 0x3f80
    1eb2:	4311      	orrs	r1, r2
				(*usb_midi_handlePitchChange)(ch, value);
    1eb4:	b2e4      	uxtb	r4, r4
    1eb6:	f5a1 5100 	sub.w	r1, r1, #8192	; 0x2000
    1eba:	4620      	mov	r0, r4
    1ebc:	4798      	blx	r3
				int value = ((n >> 16) & 0x7F) | ((n >> 17) & 0x3F80);
    1ebe:	0c2e      	lsrs	r6, r5, #16
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    1ec0:	b2f6      	uxtb	r6, r6
    1ec2:	0e2d      	lsrs	r5, r5, #24
		usb_midi_msg_channel = ch;
    1ec4:	4b80      	ldr	r3, [pc, #512]	; (20c8 <usb_midi_read+0x2c8>)
		usb_midi_msg_data1 = (n >> 16);
    1ec6:	4a81      	ldr	r2, [pc, #516]	; (20cc <usb_midi_read+0x2cc>)
		usb_midi_msg_channel = ch;
    1ec8:	701c      	strb	r4, [r3, #0]
		usb_midi_msg_data2 = (n >> 24);
    1eca:	4b81      	ldr	r3, [pc, #516]	; (20d0 <usb_midi_read+0x2d0>)
		usb_midi_msg_data1 = (n >> 16);
    1ecc:	7016      	strb	r6, [r2, #0]
		usb_midi_msg_data2 = (n >> 24);
    1ece:	701d      	strb	r5, [r3, #0]
		return 1;
    1ed0:	2001      	movs	r0, #1
}
    1ed2:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
		if (!usb_configuration) return 0;
    1ed6:	4b7f      	ldr	r3, [pc, #508]	; (20d4 <usb_midi_read+0x2d4>)
    1ed8:	781b      	ldrb	r3, [r3, #0]
    1eda:	2b00      	cmp	r3, #0
    1edc:	d0c2      	beq.n	1e64 <usb_midi_read+0x64>
		rx_packet = usb_rx(MIDI_RX_ENDPOINT);
    1ede:	2005      	movs	r0, #5
    1ee0:	f7ff fa30 	bl	1344 <usb_rx>
    1ee4:	4603      	mov	r3, r0
    1ee6:	6030      	str	r0, [r6, #0]
		if (!rx_packet) return 0;
    1ee8:	2800      	cmp	r0, #0
    1eea:	d0bb      	beq.n	1e64 <usb_midi_read+0x64>
		if (rx_packet->len == 0) {
    1eec:	8804      	ldrh	r4, [r0, #0]
    1eee:	2c00      	cmp	r4, #0
    1ef0:	d18e      	bne.n	1e10 <usb_midi_read+0x10>
			usb_free(rx_packet);
    1ef2:	f7ff ff21 	bl	1d38 <usb_free>
			rx_packet = NULL;
    1ef6:	6034      	str	r4, [r6, #0]
			return 0;
    1ef8:	4620      	mov	r0, r4
    1efa:	e7b4      	b.n	1e66 <usb_midi_read+0x66>
		usb_free(rx_packet);
    1efc:	4618      	mov	r0, r3
    1efe:	f7ff ff1b 	bl	1d38 <usb_free>
		rx_packet = usb_rx(MIDI_RX_ENDPOINT);
    1f02:	2005      	movs	r0, #5
    1f04:	f7ff fa1e 	bl	1344 <usb_rx>
    1f08:	6030      	str	r0, [r6, #0]
    1f0a:	e78a      	b.n	1e22 <usb_midi_read+0x22>
		switch (b1) {
    1f0c:	38f1      	subs	r0, #241	; 0xf1
    1f0e:	280e      	cmp	r0, #14
    1f10:	d8a8      	bhi.n	1e64 <usb_midi_read+0x64>
    1f12:	a301      	add	r3, pc, #4	; (adr r3, 1f18 <usb_midi_read+0x118>)
    1f14:	f853 f020 	ldr.w	pc, [r3, r0, lsl #2]
    1f18:	0000215b 	.word	0x0000215b
    1f1c:	00002053 	.word	0x00002053
    1f20:	0000207b 	.word	0x0000207b
    1f24:	00001e65 	.word	0x00001e65
    1f28:	00001e65 	.word	0x00001e65
    1f2c:	00002181 	.word	0x00002181
    1f30:	00001e65 	.word	0x00001e65
    1f34:	0000208d 	.word	0x0000208d
    1f38:	00001e65 	.word	0x00001e65
    1f3c:	0000209f 	.word	0x0000209f
    1f40:	00002111 	.word	0x00002111
    1f44:	0000212b 	.word	0x0000212b
    1f48:	00001e65 	.word	0x00001e65
    1f4c:	00002143 	.word	0x00002143
    1f50:	00002169 	.word	0x00002169
		if (type1 == 0x08 && type2 == 0x08) {
    1f54:	2b08      	cmp	r3, #8
    1f56:	d185      	bne.n	1e64 <usb_midi_read+0x64>
			if (usb_midi_handleNoteOff)
    1f58:	4b5f      	ldr	r3, [pc, #380]	; (20d8 <usb_midi_read+0x2d8>)
			usb_midi_msg_type = 0x80;		// 0x80 = usbMIDI.NoteOff
    1f5a:	4a5a      	ldr	r2, [pc, #360]	; (20c4 <usb_midi_read+0x2c4>)
			if (usb_midi_handleNoteOff)
    1f5c:	681b      	ldr	r3, [r3, #0]
			usb_midi_msg_type = 0x80;		// 0x80 = usbMIDI.NoteOff
    1f5e:	2180      	movs	r1, #128	; 0x80
				(*usb_midi_handleControlChange)(ch, (n >> 16), (n >> 24));
    1f60:	f3c5 4607 	ubfx	r6, r5, #16, #8
			usb_midi_msg_type = 0xB0;		// 0xB0 = usbMIDI.ControlChange
    1f64:	7011      	strb	r1, [r2, #0]
				(*usb_midi_handleControlChange)(ch, (n >> 16), (n >> 24));
    1f66:	b2e4      	uxtb	r4, r4
    1f68:	0e2d      	lsrs	r5, r5, #24
			if (usb_midi_handleControlChange)
    1f6a:	2b00      	cmp	r3, #0
    1f6c:	d0aa      	beq.n	1ec4 <usb_midi_read+0xc4>
				(*usb_midi_handleControlChange)(ch, (n >> 16), (n >> 24));
    1f6e:	462a      	mov	r2, r5
    1f70:	4631      	mov	r1, r6
    1f72:	4620      	mov	r0, r4
    1f74:	4798      	blx	r3
    1f76:	e7a5      	b.n	1ec4 <usb_midi_read+0xc4>
		if (type1 == 0x09 && type2 == 0x09) {
    1f78:	2b09      	cmp	r3, #9
    1f7a:	f47f af73 	bne.w	1e64 <usb_midi_read+0x64>
			if ((n >> 24) > 0) {
    1f7e:	0e2a      	lsrs	r2, r5, #24
				usb_midi_msg_type = 0x90;	// 0x90 = usbMIDI.NoteOn
    1f80:	4b50      	ldr	r3, [pc, #320]	; (20c4 <usb_midi_read+0x2c4>)
			if ((n >> 24) > 0) {
    1f82:	f000 811f 	beq.w	21c4 <usb_midi_read+0x3c4>
				usb_midi_msg_type = 0x90;	// 0x90 = usbMIDI.NoteOn
    1f86:	2190      	movs	r1, #144	; 0x90
    1f88:	7019      	strb	r1, [r3, #0]
				if (usb_midi_handleNoteOn)
    1f8a:	4b54      	ldr	r3, [pc, #336]	; (20dc <usb_midi_read+0x2dc>)
    1f8c:	681b      	ldr	r3, [r3, #0]
					(*usb_midi_handleNoteOn)(ch, (n >> 16), (n >> 24));
    1f8e:	f3c5 4607 	ubfx	r6, r5, #16, #8
    1f92:	b2e4      	uxtb	r4, r4
    1f94:	b2d5      	uxtb	r5, r2
				if (usb_midi_handleNoteOn)
    1f96:	2b00      	cmp	r3, #0
    1f98:	d1e9      	bne.n	1f6e <usb_midi_read+0x16e>
    1f9a:	e793      	b.n	1ec4 <usb_midi_read+0xc4>
		if (type1 == 0x0A && type2 == 0x0A) {
    1f9c:	2b0a      	cmp	r3, #10
    1f9e:	f47f af61 	bne.w	1e64 <usb_midi_read+0x64>
			if (usb_midi_handleVelocityChange)
    1fa2:	4b4f      	ldr	r3, [pc, #316]	; (20e0 <usb_midi_read+0x2e0>)
			usb_midi_msg_type = 0xA0;		// 0xA0 = usbMIDI.AfterTouchPoly
    1fa4:	4a47      	ldr	r2, [pc, #284]	; (20c4 <usb_midi_read+0x2c4>)
			if (usb_midi_handleVelocityChange)
    1fa6:	681b      	ldr	r3, [r3, #0]
			usb_midi_msg_type = 0xA0;		// 0xA0 = usbMIDI.AfterTouchPoly
    1fa8:	21a0      	movs	r1, #160	; 0xa0
    1faa:	e7d9      	b.n	1f60 <usb_midi_read+0x160>
		if (type1 == 0x0B && type2 == 0x0B) {
    1fac:	2b0b      	cmp	r3, #11
    1fae:	f47f af59 	bne.w	1e64 <usb_midi_read+0x64>
			if (usb_midi_handleControlChange)
    1fb2:	4b4c      	ldr	r3, [pc, #304]	; (20e4 <usb_midi_read+0x2e4>)
			usb_midi_msg_type = 0xB0;		// 0xB0 = usbMIDI.ControlChange
    1fb4:	4a43      	ldr	r2, [pc, #268]	; (20c4 <usb_midi_read+0x2c4>)
			if (usb_midi_handleControlChange)
    1fb6:	681b      	ldr	r3, [r3, #0]
			usb_midi_msg_type = 0xB0;		// 0xB0 = usbMIDI.ControlChange
    1fb8:	21b0      	movs	r1, #176	; 0xb0
    1fba:	e7d1      	b.n	1f60 <usb_midi_read+0x160>
	if (type1 == 0x02 || type1 == 0x03 || (type1 == 0x05 && b1 >= 0xF1 && b1 != 0xF7)) {
    1fbc:	28f0      	cmp	r0, #240	; 0xf0
    1fbe:	d901      	bls.n	1fc4 <usb_midi_read+0x1c4>
    1fc0:	28f7      	cmp	r0, #247	; 0xf7
    1fc2:	d1a3      	bne.n	1f0c <usb_midi_read+0x10c>
		if (type1 == 0x07) sysex_byte(n >> 24);
    1fc4:	f7ff fee0 	bl	1d88 <sysex_byte>
		uint16_t len = usb_midi_msg_sysex_len;
    1fc8:	4b47      	ldr	r3, [pc, #284]	; (20e8 <usb_midi_read+0x2e8>)
		usb_midi_msg_data1 = len;
    1fca:	4a40      	ldr	r2, [pc, #256]	; (20cc <usb_midi_read+0x2cc>)
		uint16_t len = usb_midi_msg_sysex_len;
    1fcc:	8819      	ldrh	r1, [r3, #0]
		usb_midi_msg_data2 = len >> 8;
    1fce:	4840      	ldr	r0, [pc, #256]	; (20d0 <usb_midi_read+0x2d0>)
		if (usb_midi_handleSysExPartial) {
    1fd0:	4c46      	ldr	r4, [pc, #280]	; (20ec <usb_midi_read+0x2ec>)
		usb_midi_msg_data1 = len;
    1fd2:	7011      	strb	r1, [r2, #0]
		usb_midi_msg_data2 = len >> 8;
    1fd4:	0a0d      	lsrs	r5, r1, #8
		usb_midi_msg_type = 0xF0;			// 0xF0 = usbMIDI.SystemExclusive
    1fd6:	4a3b      	ldr	r2, [pc, #236]	; (20c4 <usb_midi_read+0x2c4>)
		usb_midi_msg_data2 = len >> 8;
    1fd8:	7005      	strb	r5, [r0, #0]
		if (usb_midi_handleSysExPartial) {
    1fda:	6824      	ldr	r4, [r4, #0]
		usb_midi_msg_sysex_len = 0;
    1fdc:	2000      	movs	r0, #0
    1fde:	8018      	strh	r0, [r3, #0]
		usb_midi_msg_type = 0xF0;			// 0xF0 = usbMIDI.SystemExclusive
    1fe0:	23f0      	movs	r3, #240	; 0xf0
    1fe2:	7013      	strb	r3, [r2, #0]
		if (usb_midi_handleSysExPartial) {
    1fe4:	2c00      	cmp	r4, #0
    1fe6:	f000 80e6 	beq.w	21b6 <usb_midi_read+0x3b6>
			(*usb_midi_handleSysExPartial)(usb_midi_msg_sysex, len, 1);
    1fea:	4841      	ldr	r0, [pc, #260]	; (20f0 <usb_midi_read+0x2f0>)
    1fec:	2201      	movs	r2, #1
    1fee:	47a0      	blx	r4
		return 1;
    1ff0:	2001      	movs	r0, #1
    1ff2:	e738      	b.n	1e66 <usb_midi_read+0x66>
		if (type1 == 0x0C && type2 == 0x0C) {
    1ff4:	2b0c      	cmp	r3, #12
    1ff6:	f47f af35 	bne.w	1e64 <usb_midi_read+0x64>
			if (usb_midi_handleProgramChange)
    1ffa:	4b3e      	ldr	r3, [pc, #248]	; (20f4 <usb_midi_read+0x2f4>)
			usb_midi_msg_type = 0xC0;		// 0xC0 = usbMIDI.ProgramChange
    1ffc:	4a31      	ldr	r2, [pc, #196]	; (20c4 <usb_midi_read+0x2c4>)
			if (usb_midi_handleProgramChange)
    1ffe:	681b      	ldr	r3, [r3, #0]
			usb_midi_msg_type = 0xC0;		// 0xC0 = usbMIDI.ProgramChange
    2000:	21c0      	movs	r1, #192	; 0xc0
    2002:	7011      	strb	r1, [r2, #0]
				(*usb_midi_handleProgramChange)(ch, (n >> 16));
    2004:	b2e4      	uxtb	r4, r4
    2006:	f3c5 4607 	ubfx	r6, r5, #16, #8
			if (usb_midi_handleProgramChange)
    200a:	2b00      	cmp	r3, #0
    200c:	f43f af59 	beq.w	1ec2 <usb_midi_read+0xc2>
				(*usb_midi_handleAfterTouch)(ch, (n >> 16));
    2010:	4631      	mov	r1, r6
    2012:	4620      	mov	r0, r4
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    2014:	0e2d      	lsrs	r5, r5, #24
				(*usb_midi_handleAfterTouch)(ch, (n >> 16));
    2016:	4798      	blx	r3
    2018:	e754      	b.n	1ec4 <usb_midi_read+0xc4>
		if (type1 == 0x0D && type2 == 0x0D) {
    201a:	2b0d      	cmp	r3, #13
    201c:	f47f af22 	bne.w	1e64 <usb_midi_read+0x64>
			if (usb_midi_handleAfterTouch)
    2020:	4b35      	ldr	r3, [pc, #212]	; (20f8 <usb_midi_read+0x2f8>)
			usb_midi_msg_type = 0xD0;		// 0xD0 = usbMIDI.AfterTouchChannel
    2022:	4a28      	ldr	r2, [pc, #160]	; (20c4 <usb_midi_read+0x2c4>)
			if (usb_midi_handleAfterTouch)
    2024:	681b      	ldr	r3, [r3, #0]
			usb_midi_msg_type = 0xD0;		// 0xD0 = usbMIDI.AfterTouchChannel
    2026:	21d0      	movs	r1, #208	; 0xd0
    2028:	7011      	strb	r1, [r2, #0]
				(*usb_midi_handleAfterTouch)(ch, (n >> 16));
    202a:	b2e4      	uxtb	r4, r4
    202c:	f3c5 4607 	ubfx	r6, r5, #16, #8
			if (usb_midi_handleAfterTouch)
    2030:	2b00      	cmp	r3, #0
    2032:	d1ed      	bne.n	2010 <usb_midi_read+0x210>
    2034:	e745      	b.n	1ec2 <usb_midi_read+0xc2>
		if (b1 >= 0xF8) {
    2036:	28f7      	cmp	r0, #247	; 0xf7
    2038:	f63f af68 	bhi.w	1f0c <usb_midi_read+0x10c>
		if (b1 == 0xF0 || usb_midi_msg_sysex_len > 0) {
    203c:	28f0      	cmp	r0, #240	; 0xf0
    203e:	d004      	beq.n	204a <usb_midi_read+0x24a>
    2040:	4b29      	ldr	r3, [pc, #164]	; (20e8 <usb_midi_read+0x2e8>)
    2042:	881b      	ldrh	r3, [r3, #0]
    2044:	2b00      	cmp	r3, #0
    2046:	f43f af0d 	beq.w	1e64 <usb_midi_read+0x64>
			sysex_byte(b1);
    204a:	f7ff fe9d 	bl	1d88 <sysex_byte>
	return 0;
    204e:	2000      	movs	r0, #0
    2050:	e709      	b.n	1e66 <usb_midi_read+0x66>
			if (usb_midi_handleSongPosition) {
    2052:	4b2a      	ldr	r3, [pc, #168]	; (20fc <usb_midi_read+0x2fc>)
    2054:	681a      	ldr	r2, [r3, #0]
    2056:	2a00      	cmp	r2, #0
    2058:	f000 808f 	beq.w	217a <usb_midi_read+0x37a>
				  ((n >> 16) & 0x7F) | ((n >> 17) & 0x3F80));
    205c:	0c6b      	lsrs	r3, r5, #17
    205e:	f403 537e 	and.w	r3, r3, #16256	; 0x3f80
    2062:	f3c5 4006 	ubfx	r0, r5, #16, #7
    2066:	0c2e      	lsrs	r6, r5, #16
				(*usb_midi_handleSongPosition)(
    2068:	4318      	orrs	r0, r3
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    206a:	b2f6      	uxtb	r6, r6
				(*usb_midi_handleSongPosition)(
    206c:	4790      	blx	r2
		usb_midi_msg_type = b1;
    206e:	4b15      	ldr	r3, [pc, #84]	; (20c4 <usb_midi_read+0x2c4>)
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    2070:	b2e4      	uxtb	r4, r4
		usb_midi_msg_type = b1;
    2072:	f883 8000 	strb.w	r8, [r3]
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    2076:	0e2d      	lsrs	r5, r5, #24
		goto return_message;
    2078:	e724      	b.n	1ec4 <usb_midi_read+0xc4>
			if (usb_midi_handleSongSelect) {
    207a:	4b21      	ldr	r3, [pc, #132]	; (2100 <usb_midi_read+0x300>)
    207c:	681b      	ldr	r3, [r3, #0]
				(*usb_midi_handleSongSelect)(n >> 16);
    207e:	f3c5 4607 	ubfx	r6, r5, #16, #8
			if (usb_midi_handleSongSelect) {
    2082:	2b00      	cmp	r3, #0
    2084:	d0f3      	beq.n	206e <usb_midi_read+0x26e>
				(*usb_midi_handleSongSelect)(n >> 16);
    2086:	4630      	mov	r0, r6
    2088:	4798      	blx	r3
    208a:	e7f0      	b.n	206e <usb_midi_read+0x26e>
			if (usb_midi_handleClock) {
    208c:	4b1d      	ldr	r3, [pc, #116]	; (2104 <usb_midi_read+0x304>)
    208e:	681b      	ldr	r3, [r3, #0]
    2090:	2b00      	cmp	r3, #0
    2092:	f000 80a9 	beq.w	21e8 <usb_midi_read+0x3e8>
				(*usb_midi_handleSystemReset)();
    2096:	4798      	blx	r3
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    2098:	f3c5 4607 	ubfx	r6, r5, #16, #8
    209c:	e7e7      	b.n	206e <usb_midi_read+0x26e>
			if (usb_midi_handleStart) {
    209e:	4b1a      	ldr	r3, [pc, #104]	; (2108 <usb_midi_read+0x308>)
    20a0:	681b      	ldr	r3, [r3, #0]
    20a2:	2b00      	cmp	r3, #0
    20a4:	d1f7      	bne.n	2096 <usb_midi_read+0x296>
			} else if (usb_midi_handleRealTimeSystem) {
    20a6:	4b19      	ldr	r3, [pc, #100]	; (210c <usb_midi_read+0x30c>)
    20a8:	681b      	ldr	r3, [r3, #0]
    20aa:	2b00      	cmp	r3, #0
    20ac:	d065      	beq.n	217a <usb_midi_read+0x37a>
				(*usb_midi_handleRealTimeSystem)(0xFA);
    20ae:	20fa      	movs	r0, #250	; 0xfa
    20b0:	4798      	blx	r3
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    20b2:	f3c5 4607 	ubfx	r6, r5, #16, #8
    20b6:	e7da      	b.n	206e <usb_midi_read+0x26e>
    20b8:	1fff0e88 	.word	0x1fff0e88
    20bc:	1fff0ee4 	.word	0x1fff0ee4
    20c0:	1fff0eb0 	.word	0x1fff0eb0
    20c4:	1fff100c 	.word	0x1fff100c
    20c8:	1fff0ee5 	.word	0x1fff0ee5
    20cc:	1fff0ee6 	.word	0x1fff0ee6
    20d0:	1fff0ee7 	.word	0x1fff0ee7
    20d4:	1fff0e79 	.word	0x1fff0e79
    20d8:	1fff0ea8 	.word	0x1fff0ea8
    20dc:	1fff0eac 	.word	0x1fff0eac
    20e0:	1fff0ee0 	.word	0x1fff0ee0
    20e4:	1fff0ea4 	.word	0x1fff0ea4
    20e8:	1fff100a 	.word	0x1fff100a
    20ec:	1fff0ed0 	.word	0x1fff0ed0
    20f0:	1fff0ee8 	.word	0x1fff0ee8
    20f4:	1fff0eb4 	.word	0x1fff0eb4
    20f8:	1fff0e98 	.word	0x1fff0e98
    20fc:	1fff0ebc 	.word	0x1fff0ebc
    2100:	1fff0ec0 	.word	0x1fff0ec0
    2104:	1fff0e9c 	.word	0x1fff0e9c
    2108:	1fff0ec4 	.word	0x1fff0ec4
    210c:	1fff0eb8 	.word	0x1fff0eb8
			if (usb_midi_handleContinue) {
    2110:	4b3c      	ldr	r3, [pc, #240]	; (2204 <usb_midi_read+0x404>)
    2112:	681b      	ldr	r3, [r3, #0]
    2114:	2b00      	cmp	r3, #0
    2116:	d1be      	bne.n	2096 <usb_midi_read+0x296>
			} else if (usb_midi_handleRealTimeSystem) {
    2118:	4b3b      	ldr	r3, [pc, #236]	; (2208 <usb_midi_read+0x408>)
    211a:	681b      	ldr	r3, [r3, #0]
    211c:	2b00      	cmp	r3, #0
    211e:	d02c      	beq.n	217a <usb_midi_read+0x37a>
				(*usb_midi_handleRealTimeSystem)(0xFB);
    2120:	20fb      	movs	r0, #251	; 0xfb
    2122:	4798      	blx	r3
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    2124:	f3c5 4607 	ubfx	r6, r5, #16, #8
    2128:	e7a1      	b.n	206e <usb_midi_read+0x26e>
			if (usb_midi_handleStop) {
    212a:	4b38      	ldr	r3, [pc, #224]	; (220c <usb_midi_read+0x40c>)
    212c:	681b      	ldr	r3, [r3, #0]
    212e:	2b00      	cmp	r3, #0
    2130:	d1b1      	bne.n	2096 <usb_midi_read+0x296>
			} else if (usb_midi_handleRealTimeSystem) {
    2132:	4b35      	ldr	r3, [pc, #212]	; (2208 <usb_midi_read+0x408>)
    2134:	681b      	ldr	r3, [r3, #0]
    2136:	b303      	cbz	r3, 217a <usb_midi_read+0x37a>
				(*usb_midi_handleRealTimeSystem)(0xFC);
    2138:	20fc      	movs	r0, #252	; 0xfc
    213a:	4798      	blx	r3
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    213c:	f3c5 4607 	ubfx	r6, r5, #16, #8
    2140:	e795      	b.n	206e <usb_midi_read+0x26e>
			if (usb_midi_handleActiveSensing) {
    2142:	4b33      	ldr	r3, [pc, #204]	; (2210 <usb_midi_read+0x410>)
    2144:	681b      	ldr	r3, [r3, #0]
    2146:	2b00      	cmp	r3, #0
    2148:	d1a5      	bne.n	2096 <usb_midi_read+0x296>
			} else if (usb_midi_handleRealTimeSystem) {
    214a:	4b2f      	ldr	r3, [pc, #188]	; (2208 <usb_midi_read+0x408>)
    214c:	681b      	ldr	r3, [r3, #0]
    214e:	b1a3      	cbz	r3, 217a <usb_midi_read+0x37a>
				(*usb_midi_handleRealTimeSystem)(0xFE);
    2150:	20fe      	movs	r0, #254	; 0xfe
    2152:	4798      	blx	r3
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    2154:	f3c5 4607 	ubfx	r6, r5, #16, #8
    2158:	e789      	b.n	206e <usb_midi_read+0x26e>
			if (usb_midi_handleTimeCodeQuarterFrame) {
    215a:	4b2e      	ldr	r3, [pc, #184]	; (2214 <usb_midi_read+0x414>)
    215c:	681b      	ldr	r3, [r3, #0]
				(*usb_midi_handleTimeCodeQuarterFrame)(n >> 16);
    215e:	f3c5 4607 	ubfx	r6, r5, #16, #8
			if (usb_midi_handleTimeCodeQuarterFrame) {
    2162:	2b00      	cmp	r3, #0
    2164:	d18f      	bne.n	2086 <usb_midi_read+0x286>
    2166:	e782      	b.n	206e <usb_midi_read+0x26e>
			if (usb_midi_handleSystemReset) {
    2168:	4b2b      	ldr	r3, [pc, #172]	; (2218 <usb_midi_read+0x418>)
    216a:	681b      	ldr	r3, [r3, #0]
    216c:	2b00      	cmp	r3, #0
    216e:	d192      	bne.n	2096 <usb_midi_read+0x296>
			} else if (usb_midi_handleRealTimeSystem) {
    2170:	4b25      	ldr	r3, [pc, #148]	; (2208 <usb_midi_read+0x408>)
    2172:	681b      	ldr	r3, [r3, #0]
    2174:	b10b      	cbz	r3, 217a <usb_midi_read+0x37a>
				(*usb_midi_handleRealTimeSystem)(0xFF);
    2176:	20ff      	movs	r0, #255	; 0xff
    2178:	4798      	blx	r3
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    217a:	f3c5 4607 	ubfx	r6, r5, #16, #8
    217e:	e776      	b.n	206e <usb_midi_read+0x26e>
			if (usb_midi_handleTuneRequest) {
    2180:	4b26      	ldr	r3, [pc, #152]	; (221c <usb_midi_read+0x41c>)
    2182:	681b      	ldr	r3, [r3, #0]
    2184:	2b00      	cmp	r3, #0
    2186:	d186      	bne.n	2096 <usb_midi_read+0x296>
    2188:	e7f7      	b.n	217a <usb_midi_read+0x37a>
		sysex_byte(n >> 8);
    218a:	f7ff fdfd 	bl	1d88 <sysex_byte>
		sysex_byte(n >> 16);
    218e:	f3c5 4007 	ubfx	r0, r5, #16, #8
    2192:	f7ff fdf9 	bl	1d88 <sysex_byte>
		sysex_byte(n >> 24);
    2196:	0e28      	lsrs	r0, r5, #24
    2198:	f7ff fdf6 	bl	1d88 <sysex_byte>
		return 0;
    219c:	2000      	movs	r0, #0
    219e:	e662      	b.n	1e66 <usb_midi_read+0x66>
		sysex_byte(b1);
    21a0:	f7ff fdf2 	bl	1d88 <sysex_byte>
		if (type1 >= 0x06) sysex_byte(n >> 16);
    21a4:	f3c5 4007 	ubfx	r0, r5, #16, #8
    21a8:	f7ff fdee 	bl	1d88 <sysex_byte>
		if (type1 == 0x07) sysex_byte(n >> 24);
    21ac:	2e07      	cmp	r6, #7
    21ae:	f47f af0b 	bne.w	1fc8 <usb_midi_read+0x1c8>
    21b2:	0e28      	lsrs	r0, r5, #24
    21b4:	e706      	b.n	1fc4 <usb_midi_read+0x1c4>
		} else if (usb_midi_handleSysExComplete) {
    21b6:	4b1a      	ldr	r3, [pc, #104]	; (2220 <usb_midi_read+0x420>)
    21b8:	681b      	ldr	r3, [r3, #0]
    21ba:	b1f3      	cbz	r3, 21fa <usb_midi_read+0x3fa>
			(*usb_midi_handleSysExComplete)(usb_midi_msg_sysex, len);
    21bc:	4819      	ldr	r0, [pc, #100]	; (2224 <usb_midi_read+0x424>)
    21be:	4798      	blx	r3
		return 1;
    21c0:	2001      	movs	r0, #1
    21c2:	e650      	b.n	1e66 <usb_midi_read+0x66>
				usb_midi_msg_type = 0x80;	// 0x80 = usbMIDI.NoteOff
    21c4:	2180      	movs	r1, #128	; 0x80
    21c6:	7019      	strb	r1, [r3, #0]
				if (usb_midi_handleNoteOff)
    21c8:	4b17      	ldr	r3, [pc, #92]	; (2228 <usb_midi_read+0x428>)
    21ca:	681b      	ldr	r3, [r3, #0]
					(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    21cc:	b2e4      	uxtb	r4, r4
    21ce:	f3c5 4607 	ubfx	r6, r5, #16, #8
				if (usb_midi_handleNoteOff)
    21d2:	b1a3      	cbz	r3, 21fe <usb_midi_read+0x3fe>
					(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    21d4:	4631      	mov	r1, r6
    21d6:	4620      	mov	r0, r4
    21d8:	4615      	mov	r5, r2
    21da:	4798      	blx	r3
    21dc:	e672      	b.n	1ec4 <usb_midi_read+0xc4>
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    21de:	f3c5 4607 	ubfx	r6, r5, #16, #8
    21e2:	b2e4      	uxtb	r4, r4
    21e4:	0e2d      	lsrs	r5, r5, #24
    21e6:	e66d      	b.n	1ec4 <usb_midi_read+0xc4>
			} else if (usb_midi_handleRealTimeSystem) {
    21e8:	4b07      	ldr	r3, [pc, #28]	; (2208 <usb_midi_read+0x408>)
    21ea:	681b      	ldr	r3, [r3, #0]
    21ec:	2b00      	cmp	r3, #0
    21ee:	d0c4      	beq.n	217a <usb_midi_read+0x37a>
				(*usb_midi_handleRealTimeSystem)(0xF8);
    21f0:	20f8      	movs	r0, #248	; 0xf8
    21f2:	4798      	blx	r3
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    21f4:	f3c5 4607 	ubfx	r6, r5, #16, #8
    21f8:	e739      	b.n	206e <usb_midi_read+0x26e>
		return 1;
    21fa:	2001      	movs	r0, #1
    21fc:	e633      	b.n	1e66 <usb_midi_read+0x66>
				(*usb_midi_handleNoteOff)(ch, (n >> 16), (n >> 24));
    21fe:	461d      	mov	r5, r3
    2200:	e660      	b.n	1ec4 <usb_midi_read+0xc4>
    2202:	bf00      	nop
    2204:	1fff0ea0 	.word	0x1fff0ea0
    2208:	1fff0eb8 	.word	0x1fff0eb8
    220c:	1fff0ec8 	.word	0x1fff0ec8
    2210:	1fff0e94 	.word	0x1fff0e94
    2214:	1fff0ed8 	.word	0x1fff0ed8
    2218:	1fff0ed4 	.word	0x1fff0ed4
    221c:	1fff0edc 	.word	0x1fff0edc
    2220:	1fff0ecc 	.word	0x1fff0ecc
    2224:	1fff0ee8 	.word	0x1fff0ee8
    2228:	1fff0ea8 	.word	0x1fff0ea8

0000222c <usb_serial_getchar>:

#define TRANSMIT_FLUSH_TIMEOUT	5   /* in milliseconds */

// get the next character, or -1 if nothing received
int usb_serial_getchar(void)
{
    222c:	b538      	push	{r3, r4, r5, lr}
	unsigned int i;
	int c;

	if (!rx_packet) {
    222e:	4c0f      	ldr	r4, [pc, #60]	; (226c <usb_serial_getchar+0x40>)
    2230:	6820      	ldr	r0, [r4, #0]
    2232:	b178      	cbz	r0, 2254 <usb_serial_getchar+0x28>
		if (!usb_configuration) return -1;
		rx_packet = usb_rx(CDC_RX_ENDPOINT);
		if (!rx_packet) return -1;
	}
	i = rx_packet->index;
    2234:	8843      	ldrh	r3, [r0, #2]
	c = rx_packet->buf[i++];
	if (i >= rx_packet->len) {
    2236:	8802      	ldrh	r2, [r0, #0]
	c = rx_packet->buf[i++];
    2238:	18c1      	adds	r1, r0, r3
    223a:	3301      	adds	r3, #1
	if (i >= rx_packet->len) {
    223c:	429a      	cmp	r2, r3
	c = rx_packet->buf[i++];
    223e:	7a0d      	ldrb	r5, [r1, #8]
	if (i >= rx_packet->len) {
    2240:	d902      	bls.n	2248 <usb_serial_getchar+0x1c>
		usb_free(rx_packet);
		rx_packet = NULL;
	} else {
		rx_packet->index = i;
    2242:	8043      	strh	r3, [r0, #2]
	}
	return c;
}
    2244:	4628      	mov	r0, r5
    2246:	bd38      	pop	{r3, r4, r5, pc}
		usb_free(rx_packet);
    2248:	f7ff fd76 	bl	1d38 <usb_free>
		rx_packet = NULL;
    224c:	2300      	movs	r3, #0
    224e:	6023      	str	r3, [r4, #0]
}
    2250:	4628      	mov	r0, r5
    2252:	bd38      	pop	{r3, r4, r5, pc}
		if (!usb_configuration) return -1;
    2254:	4b06      	ldr	r3, [pc, #24]	; (2270 <usb_serial_getchar+0x44>)
    2256:	781b      	ldrb	r3, [r3, #0]
    2258:	b12b      	cbz	r3, 2266 <usb_serial_getchar+0x3a>
		rx_packet = usb_rx(CDC_RX_ENDPOINT);
    225a:	2002      	movs	r0, #2
    225c:	f7ff f872 	bl	1344 <usb_rx>
    2260:	6020      	str	r0, [r4, #0]
		if (!rx_packet) return -1;
    2262:	2800      	cmp	r0, #0
    2264:	d1e6      	bne.n	2234 <usb_serial_getchar+0x8>
		if (!usb_configuration) return -1;
    2266:	f04f 35ff 	mov.w	r5, #4294967295
    226a:	e7eb      	b.n	2244 <usb_serial_getchar+0x18>
    226c:	1fff1010 	.word	0x1fff1010
    2270:	1fff0e79 	.word	0x1fff0e79

00002274 <usb_serial_peekchar>:

// peek at the next character, or -1 if nothing received
int usb_serial_peekchar(void)
{
    2274:	b510      	push	{r4, lr}
	if (!rx_packet) {
    2276:	4c09      	ldr	r4, [pc, #36]	; (229c <usb_serial_peekchar+0x28>)
    2278:	6820      	ldr	r0, [r4, #0]
    227a:	b118      	cbz	r0, 2284 <usb_serial_peekchar+0x10>
		if (!usb_configuration) return -1;
		rx_packet = usb_rx(CDC_RX_ENDPOINT);
		if (!rx_packet) return -1;
	}
	if (!rx_packet) return -1;
	return rx_packet->buf[rx_packet->index];
    227c:	8843      	ldrh	r3, [r0, #2]
    227e:	4418      	add	r0, r3
    2280:	7a00      	ldrb	r0, [r0, #8]
}
    2282:	bd10      	pop	{r4, pc}
		if (!usb_configuration) return -1;
    2284:	4b06      	ldr	r3, [pc, #24]	; (22a0 <usb_serial_peekchar+0x2c>)
    2286:	781b      	ldrb	r3, [r3, #0]
    2288:	b12b      	cbz	r3, 2296 <usb_serial_peekchar+0x22>
		rx_packet = usb_rx(CDC_RX_ENDPOINT);
    228a:	2002      	movs	r0, #2
    228c:	f7ff f85a 	bl	1344 <usb_rx>
    2290:	6020      	str	r0, [r4, #0]
		if (!rx_packet) return -1;
    2292:	2800      	cmp	r0, #0
    2294:	d1f2      	bne.n	227c <usb_serial_peekchar+0x8>
		if (!usb_configuration) return -1;
    2296:	f04f 30ff 	mov.w	r0, #4294967295
}
    229a:	bd10      	pop	{r4, pc}
    229c:	1fff1010 	.word	0x1fff1010
    22a0:	1fff0e79 	.word	0x1fff0e79

000022a4 <usb_serial_available>:
// number of bytes available in the receive buffer
int usb_serial_available(void)
{
	int count;
	count = usb_rx_byte_count(CDC_RX_ENDPOINT);
	if (rx_packet) count += rx_packet->len - rx_packet->index;
    22a4:	4b08      	ldr	r3, [pc, #32]	; (22c8 <usb_serial_available+0x24>)
static inline uint32_t usb_rx_byte_count(uint32_t endpoint) __attribute__((always_inline));
static inline uint32_t usb_rx_byte_count(uint32_t endpoint)
{
        endpoint--;
        if (endpoint >= NUM_ENDPOINTS) return 0;
        return usb_rx_byte_count_data[endpoint];
    22a6:	4a09      	ldr	r2, [pc, #36]	; (22cc <usb_serial_available+0x28>)
    22a8:	681b      	ldr	r3, [r3, #0]
{
    22aa:	b510      	push	{r4, lr}
	count = usb_rx_byte_count(CDC_RX_ENDPOINT);
    22ac:	8854      	ldrh	r4, [r2, #2]
	if (rx_packet) count += rx_packet->len - rx_packet->index;
    22ae:	b11b      	cbz	r3, 22b8 <usb_serial_available+0x14>
    22b0:	881a      	ldrh	r2, [r3, #0]
    22b2:	885b      	ldrh	r3, [r3, #2]
    22b4:	1ad3      	subs	r3, r2, r3
    22b6:	441c      	add	r4, r3
	if (count == 0) yield();
    22b8:	b10c      	cbz	r4, 22be <usb_serial_available+0x1a>
	return count;
}
    22ba:	4620      	mov	r0, r4
    22bc:	bd10      	pop	{r4, pc}
	if (count == 0) yield();
    22be:	f000 f929 	bl	2514 <yield>
}
    22c2:	4620      	mov	r0, r4
    22c4:	bd10      	pop	{r4, pc}
    22c6:	bf00      	nop
    22c8:	1fff1010 	.word	0x1fff1010
    22cc:	1fff0e7c 	.word	0x1fff0e7c

000022d0 <usb_serial_flush_input>:
// discard any buffered input
void usb_serial_flush_input(void)
{
	usb_packet_t *rx;

	if (!usb_configuration) return;
    22d0:	4b0b      	ldr	r3, [pc, #44]	; (2300 <usb_serial_flush_input+0x30>)
    22d2:	781b      	ldrb	r3, [r3, #0]
    22d4:	b19b      	cbz	r3, 22fe <usb_serial_flush_input+0x2e>
{
    22d6:	b510      	push	{r4, lr}
	if (rx_packet) {
    22d8:	4c0a      	ldr	r4, [pc, #40]	; (2304 <usb_serial_flush_input+0x34>)
    22da:	6820      	ldr	r0, [r4, #0]
    22dc:	b148      	cbz	r0, 22f2 <usb_serial_flush_input+0x22>
		usb_free(rx_packet);
    22de:	f7ff fd2b 	bl	1d38 <usb_free>
		rx_packet = NULL;
    22e2:	2300      	movs	r3, #0
	}
	while (1) {
		rx = usb_rx(CDC_RX_ENDPOINT);
    22e4:	2002      	movs	r0, #2
		rx_packet = NULL;
    22e6:	6023      	str	r3, [r4, #0]
		rx = usb_rx(CDC_RX_ENDPOINT);
    22e8:	f7ff f82c 	bl	1344 <usb_rx>
		if (!rx) break;
    22ec:	b130      	cbz	r0, 22fc <usb_serial_flush_input+0x2c>
		usb_free(rx);
    22ee:	f7ff fd23 	bl	1d38 <usb_free>
		rx = usb_rx(CDC_RX_ENDPOINT);
    22f2:	2002      	movs	r0, #2
    22f4:	f7ff f826 	bl	1344 <usb_rx>
		if (!rx) break;
    22f8:	2800      	cmp	r0, #0
    22fa:	d1f8      	bne.n	22ee <usb_serial_flush_input+0x1e>
	}
}
    22fc:	bd10      	pop	{r4, pc}
    22fe:	4770      	bx	lr
    2300:	1fff0e79 	.word	0x1fff0e79
    2304:	1fff1010 	.word	0x1fff1010

00002308 <usb_serial_write>:
	return usb_serial_write(&c, 1);
}


int usb_serial_write(const void *buffer, uint32_t size)
{
    2308:	e92d 4ff0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
	uint32_t len;
	uint32_t wait_count;
	const uint8_t *src = (const uint8_t *)buffer;
	uint8_t *dest;

	tx_noautoflush = 1;
    230c:	f8df 90f4 	ldr.w	r9, [pc, #244]	; 2404 <usb_serial_write+0xfc>
{
    2310:	b083      	sub	sp, #12
	tx_noautoflush = 1;
    2312:	2201      	movs	r2, #1
    2314:	f889 2000 	strb.w	r2, [r9]
	while (size > 0) {
    2318:	9101      	str	r1, [sp, #4]
    231a:	b379      	cbz	r1, 237c <usb_serial_write+0x74>
    231c:	4f37      	ldr	r7, [pc, #220]	; (23fc <usb_serial_write+0xf4>)
		if (!tx_packet) {
			wait_count = 0;
			while (1) {
				if (!usb_configuration) {
    231e:	f8df 80e8 	ldr.w	r8, [pc, #232]	; 2408 <usb_serial_write+0x100>
	while (size > 0) {
    2322:	9d01      	ldr	r5, [sp, #4]
    2324:	4e36      	ldr	r6, [pc, #216]	; (2400 <usb_serial_write+0xf8>)
		if (tx_packet->index >= CDC_TX_SIZE) {
			tx_packet->len = CDC_TX_SIZE;
			usb_tx(CDC_TX_ENDPOINT, tx_packet);
			tx_packet = NULL;
		}
		usb_cdc_transmit_flush_timer = TRANSMIT_FLUSH_TIMEOUT;
    2326:	f8df a0e4 	ldr.w	sl, [pc, #228]	; 240c <usb_serial_write+0x104>
    232a:	4683      	mov	fp, r0
		if (!tx_packet) {
    232c:	6838      	ldr	r0, [r7, #0]
    232e:	b3a8      	cbz	r0, 239c <usb_serial_write+0x94>
		len = CDC_TX_SIZE - tx_packet->index;
    2330:	f8b0 e002 	ldrh.w	lr, [r0, #2]
    2334:	f1ce 0c40 	rsb	ip, lr, #64	; 0x40
    2338:	45ac      	cmp	ip, r5
    233a:	bf28      	it	cs
    233c:	46ac      	movcs	ip, r5
		tx_packet->index += len;
    233e:	eb0e 040c 	add.w	r4, lr, ip
		transmit_previous_timeout = 0;
    2342:	2200      	movs	r2, #0
		tx_packet->index += len;
    2344:	b2a4      	uxth	r4, r4
		transmit_previous_timeout = 0;
    2346:	7032      	strb	r2, [r6, #0]
		size -= len;
    2348:	eba5 050c 	sub.w	r5, r5, ip
		tx_packet->index += len;
    234c:	8044      	strh	r4, [r0, #2]
		dest = tx_packet->buf + tx_packet->index;
    234e:	f100 0208 	add.w	r2, r0, #8
		while (len-- > 0) *dest++ = *src++;
    2352:	f1bc 0f00 	cmp.w	ip, #0
    2356:	d00a      	beq.n	236e <usb_serial_write+0x66>
    2358:	f10e 3eff 	add.w	lr, lr, #4294967295
    235c:	4472      	add	r2, lr
    235e:	44dc      	add	ip, fp
    2360:	f81b 4b01 	ldrb.w	r4, [fp], #1
    2364:	f802 4f01 	strb.w	r4, [r2, #1]!
    2368:	45dc      	cmp	ip, fp
    236a:	d1f9      	bne.n	2360 <usb_serial_write+0x58>
		if (tx_packet->index >= CDC_TX_SIZE) {
    236c:	8844      	ldrh	r4, [r0, #2]
    236e:	2c3f      	cmp	r4, #63	; 0x3f
    2370:	d80b      	bhi.n	238a <usb_serial_write+0x82>
		usb_cdc_transmit_flush_timer = TRANSMIT_FLUSH_TIMEOUT;
    2372:	2205      	movs	r2, #5
    2374:	f88a 2000 	strb.w	r2, [sl]
	while (size > 0) {
    2378:	2d00      	cmp	r5, #0
    237a:	d1d7      	bne.n	232c <usb_serial_write+0x24>
	}
	tx_noautoflush = 0;
	return ret;
    237c:	9801      	ldr	r0, [sp, #4]
	tx_noautoflush = 0;
    237e:	2300      	movs	r3, #0
    2380:	f889 3000 	strb.w	r3, [r9]
}
    2384:	b003      	add	sp, #12
    2386:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
			tx_packet->len = CDC_TX_SIZE;
    238a:	2240      	movs	r2, #64	; 0x40
    238c:	8002      	strh	r2, [r0, #0]
			usb_tx(CDC_TX_ENDPOINT, tx_packet);
    238e:	4601      	mov	r1, r0
    2390:	2003      	movs	r0, #3
    2392:	f7ff f84f 	bl	1434 <usb_tx>
			tx_packet = NULL;
    2396:	2200      	movs	r2, #0
    2398:	603a      	str	r2, [r7, #0]
    239a:	e7ea      	b.n	2372 <usb_serial_write+0x6a>
				if (!usb_configuration) {
    239c:	f898 2000 	ldrb.w	r2, [r8]
    23a0:	b322      	cbz	r2, 23ec <usb_serial_write+0xe4>
    23a2:	f24d 04e9 	movw	r4, #53481	; 0xd0e9
    23a6:	e008      	b.n	23ba <usb_serial_write+0xb2>
				if (++wait_count > TX_TIMEOUT || transmit_previous_timeout) {
    23a8:	3c01      	subs	r4, #1
    23aa:	d018      	beq.n	23de <usb_serial_write+0xd6>
    23ac:	7832      	ldrb	r2, [r6, #0]
    23ae:	b9b2      	cbnz	r2, 23de <usb_serial_write+0xd6>
				yield();
    23b0:	f000 f8b0 	bl	2514 <yield>
				if (!usb_configuration) {
    23b4:	f898 2000 	ldrb.w	r2, [r8]
    23b8:	b1c2      	cbz	r2, 23ec <usb_serial_write+0xe4>
				if (usb_tx_packet_count(CDC_TX_ENDPOINT) < TX_PACKET_LIMIT) {
    23ba:	2003      	movs	r0, #3
    23bc:	f7fe ffe2 	bl	1384 <usb_tx_packet_count>
    23c0:	2807      	cmp	r0, #7
    23c2:	d8f1      	bhi.n	23a8 <usb_serial_write+0xa0>
					tx_noautoflush = 1;
    23c4:	f04f 0301 	mov.w	r3, #1
    23c8:	f889 3000 	strb.w	r3, [r9]
					tx_packet = usb_malloc();
    23cc:	f7ff fc94 	bl	1cf8 <usb_malloc>
    23d0:	6038      	str	r0, [r7, #0]
					if (tx_packet) break;
    23d2:	2800      	cmp	r0, #0
    23d4:	d1ac      	bne.n	2330 <usb_serial_write+0x28>
				if (++wait_count > TX_TIMEOUT || transmit_previous_timeout) {
    23d6:	3c01      	subs	r4, #1
					tx_noautoflush = 0;
    23d8:	f889 0000 	strb.w	r0, [r9]
				if (++wait_count > TX_TIMEOUT || transmit_previous_timeout) {
    23dc:	d1e6      	bne.n	23ac <usb_serial_write+0xa4>
					transmit_previous_timeout = 1;
    23de:	2301      	movs	r3, #1
					return -1;
    23e0:	f04f 30ff 	mov.w	r0, #4294967295
					transmit_previous_timeout = 1;
    23e4:	7033      	strb	r3, [r6, #0]
}
    23e6:	b003      	add	sp, #12
    23e8:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
					tx_noautoflush = 0;
    23ec:	2300      	movs	r3, #0
					return -1;
    23ee:	f04f 30ff 	mov.w	r0, #4294967295
					tx_noautoflush = 0;
    23f2:	f889 3000 	strb.w	r3, [r9]
}
    23f6:	b003      	add	sp, #12
    23f8:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
    23fc:	1fff1018 	.word	0x1fff1018
    2400:	1fff1014 	.word	0x1fff1014
    2404:	1fff1015 	.word	0x1fff1015
    2408:	1fff0e79 	.word	0x1fff0e79
    240c:	1fff102c 	.word	0x1fff102c

00002410 <usb_serial_putchar>:
{
    2410:	b500      	push	{lr}
    2412:	b083      	sub	sp, #12
    2414:	4603      	mov	r3, r0
	return usb_serial_write(&c, 1);
    2416:	2101      	movs	r1, #1
    2418:	f10d 0007 	add.w	r0, sp, #7
{
    241c:	f88d 3007 	strb.w	r3, [sp, #7]
	return usb_serial_write(&c, 1);
    2420:	f7ff ff72 	bl	2308 <usb_serial_write>
}
    2424:	b003      	add	sp, #12
    2426:	f85d fb04 	ldr.w	pc, [sp], #4
    242a:	bf00      	nop

0000242c <usb_serial_write_buffer_free>:

int usb_serial_write_buffer_free(void)
{
    242c:	b538      	push	{r3, r4, r5, lr}
	uint32_t len;

	tx_noautoflush = 1;
	if (!tx_packet) {
    242e:	4d0e      	ldr	r5, [pc, #56]	; (2468 <usb_serial_write_buffer_free+0x3c>)
	tx_noautoflush = 1;
    2430:	4c0e      	ldr	r4, [pc, #56]	; (246c <usb_serial_write_buffer_free+0x40>)
	if (!tx_packet) {
    2432:	6828      	ldr	r0, [r5, #0]
	tx_noautoflush = 1;
    2434:	2301      	movs	r3, #1
    2436:	7023      	strb	r3, [r4, #0]
	if (!tx_packet) {
    2438:	b128      	cbz	r0, 2446 <usb_serial_write_buffer_free+0x1a>
		  (tx_packet = usb_malloc()) == NULL) {
			tx_noautoflush = 0;
			return 0;
		}
	}
	len = CDC_TX_SIZE - tx_packet->index;
    243a:	8840      	ldrh	r0, [r0, #2]
	// space we just promised the user could write without blocking?
	// But does this come with other performance downsides?  Could it lead to
	// buffer data never actually transmitting in some usage cases?  More
	// investigation is needed.
	// https://github.com/PaulStoffregen/cores/issues/10#issuecomment-61514955
	tx_noautoflush = 0;
    243c:	2300      	movs	r3, #0
	len = CDC_TX_SIZE - tx_packet->index;
    243e:	f1c0 0040 	rsb	r0, r0, #64	; 0x40
	tx_noautoflush = 0;
    2442:	7023      	strb	r3, [r4, #0]
	return len;
}
    2444:	bd38      	pop	{r3, r4, r5, pc}
		if (!usb_configuration ||
    2446:	4b0a      	ldr	r3, [pc, #40]	; (2470 <usb_serial_write_buffer_free+0x44>)
    2448:	781b      	ldrb	r3, [r3, #0]
    244a:	b913      	cbnz	r3, 2452 <usb_serial_write_buffer_free+0x26>
			tx_noautoflush = 0;
    244c:	2000      	movs	r0, #0
    244e:	7020      	strb	r0, [r4, #0]
}
    2450:	bd38      	pop	{r3, r4, r5, pc}
		  usb_tx_packet_count(CDC_TX_ENDPOINT) >= TX_PACKET_LIMIT ||
    2452:	2003      	movs	r0, #3
    2454:	f7fe ff96 	bl	1384 <usb_tx_packet_count>
		if (!usb_configuration ||
    2458:	2807      	cmp	r0, #7
    245a:	d8f7      	bhi.n	244c <usb_serial_write_buffer_free+0x20>
		  (tx_packet = usb_malloc()) == NULL) {
    245c:	f7ff fc4c 	bl	1cf8 <usb_malloc>
    2460:	6028      	str	r0, [r5, #0]
		  usb_tx_packet_count(CDC_TX_ENDPOINT) >= TX_PACKET_LIMIT ||
    2462:	2800      	cmp	r0, #0
    2464:	d1e9      	bne.n	243a <usb_serial_write_buffer_free+0xe>
    2466:	e7f1      	b.n	244c <usb_serial_write_buffer_free+0x20>
    2468:	1fff1018 	.word	0x1fff1018
    246c:	1fff1015 	.word	0x1fff1015
    2470:	1fff0e79 	.word	0x1fff0e79

00002474 <usb_serial_flush_output>:

void usb_serial_flush_output(void)
{
    2474:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
	if (!usb_configuration) return;
    2476:	4b11      	ldr	r3, [pc, #68]	; (24bc <usb_serial_flush_output+0x48>)
    2478:	781b      	ldrb	r3, [r3, #0]
    247a:	b18b      	cbz	r3, 24a0 <usb_serial_flush_output+0x2c>
	tx_noautoflush = 1;
	if (tx_packet) {
    247c:	4e10      	ldr	r6, [pc, #64]	; (24c0 <usb_serial_flush_output+0x4c>)
	tx_noautoflush = 1;
    247e:	4d11      	ldr	r5, [pc, #68]	; (24c4 <usb_serial_flush_output+0x50>)
	if (tx_packet) {
    2480:	6834      	ldr	r4, [r6, #0]
	tx_noautoflush = 1;
    2482:	2701      	movs	r7, #1
    2484:	702f      	strb	r7, [r5, #0]
	if (tx_packet) {
    2486:	b164      	cbz	r4, 24a2 <usb_serial_flush_output+0x2e>
		usb_cdc_transmit_flush_timer = 0;
    2488:	4b0f      	ldr	r3, [pc, #60]	; (24c8 <usb_serial_flush_output+0x54>)
    248a:	2700      	movs	r7, #0
    248c:	701f      	strb	r7, [r3, #0]
		tx_packet->len = tx_packet->index;
    248e:	8863      	ldrh	r3, [r4, #2]
    2490:	8023      	strh	r3, [r4, #0]
		usb_tx(CDC_TX_ENDPOINT, tx_packet);
    2492:	4621      	mov	r1, r4
    2494:	2003      	movs	r0, #3
    2496:	f7fe ffcd 	bl	1434 <usb_tx>
		tx_packet = NULL;
    249a:	6037      	str	r7, [r6, #0]
			usb_tx(CDC_TX_ENDPOINT, tx);
		} else {
			usb_cdc_transmit_flush_timer = 1;
		}
	}
	tx_noautoflush = 0;
    249c:	2300      	movs	r3, #0
    249e:	702b      	strb	r3, [r5, #0]
}
    24a0:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
		usb_packet_t *tx = usb_malloc();
    24a2:	f7ff fc29 	bl	1cf8 <usb_malloc>
			usb_cdc_transmit_flush_timer = 0;
    24a6:	4b08      	ldr	r3, [pc, #32]	; (24c8 <usb_serial_flush_output+0x54>)
		if (tx) {
    24a8:	4601      	mov	r1, r0
    24aa:	b120      	cbz	r0, 24b6 <usb_serial_flush_output+0x42>
			usb_tx(CDC_TX_ENDPOINT, tx);
    24ac:	2003      	movs	r0, #3
			usb_cdc_transmit_flush_timer = 0;
    24ae:	701c      	strb	r4, [r3, #0]
			usb_tx(CDC_TX_ENDPOINT, tx);
    24b0:	f7fe ffc0 	bl	1434 <usb_tx>
    24b4:	e7f2      	b.n	249c <usb_serial_flush_output+0x28>
			usb_cdc_transmit_flush_timer = 1;
    24b6:	701f      	strb	r7, [r3, #0]
    24b8:	e7f0      	b.n	249c <usb_serial_flush_output+0x28>
    24ba:	bf00      	nop
    24bc:	1fff0e79 	.word	0x1fff0e79
    24c0:	1fff1018 	.word	0x1fff1018
    24c4:	1fff1015 	.word	0x1fff1015
    24c8:	1fff102c 	.word	0x1fff102c

000024cc <usb_serial_flush_callback>:

void usb_serial_flush_callback(void)
{
    24cc:	b538      	push	{r3, r4, r5, lr}
	if (tx_noautoflush) return;
    24ce:	4b0e      	ldr	r3, [pc, #56]	; (2508 <usb_serial_flush_callback+0x3c>)
    24d0:	781b      	ldrb	r3, [r3, #0]
    24d2:	b953      	cbnz	r3, 24ea <usb_serial_flush_callback+0x1e>
	if (tx_packet) {
    24d4:	4c0d      	ldr	r4, [pc, #52]	; (250c <usb_serial_flush_callback+0x40>)
    24d6:	6821      	ldr	r1, [r4, #0]
    24d8:	b141      	cbz	r1, 24ec <usb_serial_flush_callback+0x20>
    24da:	f003 05ff 	and.w	r5, r3, #255	; 0xff
		tx_packet->len = tx_packet->index;
    24de:	884b      	ldrh	r3, [r1, #2]
    24e0:	800b      	strh	r3, [r1, #0]
		usb_tx(CDC_TX_ENDPOINT, tx_packet);
    24e2:	2003      	movs	r0, #3
    24e4:	f7fe ffa6 	bl	1434 <usb_tx>
		tx_packet = NULL;
    24e8:	6025      	str	r5, [r4, #0]
			usb_tx(CDC_TX_ENDPOINT, tx);
		} else {
			usb_cdc_transmit_flush_timer = 1;
		}
	}
}
    24ea:	bd38      	pop	{r3, r4, r5, pc}
		usb_packet_t *tx = usb_malloc();
    24ec:	f7ff fc04 	bl	1cf8 <usb_malloc>
		if (tx) {
    24f0:	4601      	mov	r1, r0
    24f2:	b120      	cbz	r0, 24fe <usb_serial_flush_callback+0x32>
}
    24f4:	e8bd 4038 	ldmia.w	sp!, {r3, r4, r5, lr}
			usb_tx(CDC_TX_ENDPOINT, tx);
    24f8:	2003      	movs	r0, #3
    24fa:	f7fe bf9b 	b.w	1434 <usb_tx>
			usb_cdc_transmit_flush_timer = 1;
    24fe:	4b04      	ldr	r3, [pc, #16]	; (2510 <usb_serial_flush_callback+0x44>)
    2500:	2201      	movs	r2, #1
    2502:	701a      	strb	r2, [r3, #0]
}
    2504:	bd38      	pop	{r3, r4, r5, pc}
    2506:	bf00      	nop
    2508:	1fff1015 	.word	0x1fff1015
    250c:	1fff1018 	.word	0x1fff1018
    2510:	1fff102c 	.word	0x1fff102c

00002514 <yield>:

extern const uint8_t _serialEvent_default;	

void yield(void) __attribute__ ((weak));
void yield(void)
{
    2514:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
	static uint8_t running=0;
	if (!yield_active_check_flags) return;	// nothing to do
    2518:	4e32      	ldr	r6, [pc, #200]	; (25e4 <yield+0xd0>)
    251a:	7833      	ldrb	r3, [r6, #0]
    251c:	b163      	cbz	r3, 2538 <yield+0x24>
	if (running) return; // TODO: does this need to be atomic?
    251e:	4f32      	ldr	r7, [pc, #200]	; (25e8 <yield+0xd4>)
    2520:	783a      	ldrb	r2, [r7, #0]
    2522:	b94a      	cbnz	r2, 2538 <yield+0x24>
	running = 1;
    2524:	2201      	movs	r2, #1


	// USB Serail - Add hack to minimize impact...
	if (yield_active_check_flags & YIELD_CHECK_USB_SERIAL) {
    2526:	07d9      	lsls	r1, r3, #31
	running = 1;
    2528:	703a      	strb	r2, [r7, #0]
	if (yield_active_check_flags & YIELD_CHECK_USB_SERIAL) {
    252a:	d443      	bmi.n	25b4 <yield+0xa0>
	if (yield_active_check_flags & YIELD_CHECK_USB_SERIALUSB2) {
		if (SerialUSB2.available()) serialEventUSB2();
		if (_serialEventUSB2_default) yield_active_check_flags &= ~YIELD_CHECK_USB_SERIALUSB2;
	}
#endif
	if (yield_active_check_flags & YIELD_CHECK_HARDWARE_SERIAL) {
    252c:	079a      	lsls	r2, r3, #30
    252e:	d426      	bmi.n	257e <yield+0x6a>
		HardwareSerial::processSerialEventsList();
	}
	running = 0;
    2530:	2200      	movs	r2, #0
	if (yield_active_check_flags & YIELD_CHECK_EVENT_RESPONDER) EventResponder::runFromYield();
    2532:	075b      	lsls	r3, r3, #29
	running = 0;
    2534:	703a      	strb	r2, [r7, #0]
	if (yield_active_check_flags & YIELD_CHECK_EVENT_RESPONDER) EventResponder::runFromYield();
    2536:	d401      	bmi.n	253c <yield+0x28>
	
};
    2538:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
	// used with a scheduler or RTOS.
	bool waitForEvent(EventResponderRef event, int timeout);
	EventResponder * waitForEvent(EventResponder *list, int listsize, int timeout);

	static void runFromYield() {
		if (!firstYield) return;  
    253c:	4b2b      	ldr	r3, [pc, #172]	; (25ec <yield+0xd8>)
    253e:	681a      	ldr	r2, [r3, #0]
    2540:	2a00      	cmp	r2, #0
    2542:	d0f9      	beq.n	2538 <yield+0x24>
		// First, check if yield was called from an interrupt
		// never call normal handler functions from any interrupt context
		uint32_t ipsr;
		__asm__ volatile("mrs %0, ipsr\n" : "=r" (ipsr)::);
    2544:	f3ef 8205 	mrs	r2, IPSR
		if (ipsr != 0) return;
    2548:	2a00      	cmp	r2, #0
    254a:	d1f5      	bne.n	2538 <yield+0x24>
	static EventResponder *lastInterrupt;
	static bool runningFromYield;
private:
	static bool disableInterrupts() {
		uint32_t primask;
		__asm__ volatile("mrs %0, primask\n" : "=r" (primask)::);
    254c:	f3ef 8510 	mrs	r5, PRIMASK
		__disable_irq();
    2550:	b672      	cpsid	i
		EventResponder *first = firstYield;
    2552:	6818      	ldr	r0, [r3, #0]
		if (first == nullptr) {
    2554:	2800      	cmp	r0, #0
    2556:	d03a      	beq.n	25ce <yield+0xba>
		if (runningFromYield) {
    2558:	4c25      	ldr	r4, [pc, #148]	; (25f0 <yield+0xdc>)
    255a:	7821      	ldrb	r1, [r4, #0]
    255c:	2900      	cmp	r1, #0
    255e:	d136      	bne.n	25ce <yield+0xba>
		firstYield = first->_next;
    2560:	6942      	ldr	r2, [r0, #20]
    2562:	601a      	str	r2, [r3, #0]
		runningFromYield = true;
    2564:	2301      	movs	r3, #1
    2566:	7023      	strb	r3, [r4, #0]
		if (firstYield) {
    2568:	2a00      	cmp	r2, #0
    256a:	d037      	beq.n	25dc <yield+0xc8>
			firstYield->_prev = nullptr;
    256c:	6191      	str	r1, [r2, #24]
		return (primask == 0) ? true : false;
	}
	static void enableInterrupts(bool doit) {
		if (doit) __enable_irq();
    256e:	b905      	cbnz	r5, 2572 <yield+0x5e>
    2570:	b662      	cpsie	i
		first->_triggered = false;
    2572:	2500      	movs	r5, #0
		(*(first->_function))(*first);
    2574:	6883      	ldr	r3, [r0, #8]
		first->_triggered = false;
    2576:	7745      	strb	r5, [r0, #29]
		(*(first->_function))(*first);
    2578:	4798      	blx	r3
		runningFromYield = false;
    257a:	7025      	strb	r5, [r4, #0]
    257c:	e7dc      	b.n	2538 <yield+0x24>
					  return len; }
	virtual size_t write9bit(uint32_t c)	{ serial_putchar(c); return 1; }
	operator bool()			{ return true; }

	static inline void processSerialEventsList() {
		for (uint8_t i = 0; i < s_count_serials_with_serial_events; i++) {
    257e:	f8df 807c 	ldr.w	r8, [pc, #124]	; 25fc <yield+0xe8>
    2582:	f898 2000 	ldrb.w	r2, [r8]
    2586:	2a00      	cmp	r2, #0
    2588:	d0d2      	beq.n	2530 <yield+0x1c>
    258a:	2500      	movs	r5, #0
    258c:	f8df 9070 	ldr.w	r9, [pc, #112]	; 2600 <yield+0xec>
    2590:	462b      	mov	r3, r5
			s_serials_with_serial_events[i]->doYieldCode();
    2592:	f859 4023 	ldr.w	r4, [r9, r3, lsl #2]
	static HardwareSerial 	*s_serials_with_serial_events[CNT_HARDWARE_SERIAL];
	static uint8_t 			s_count_serials_with_serial_events;
	void 		(* const _serialEvent)(); 
	void addToSerialEventsList(); 
	inline void doYieldCode()  {
		if (available()) (*_serialEvent)();
    2596:	6823      	ldr	r3, [r4, #0]
    2598:	4620      	mov	r0, r4
    259a:	691b      	ldr	r3, [r3, #16]
		for (uint8_t i = 0; i < s_count_serials_with_serial_events; i++) {
    259c:	3501      	adds	r5, #1
		if (available()) (*_serialEvent)();
    259e:	4798      	blx	r3
    25a0:	b108      	cbz	r0, 25a6 <yield+0x92>
    25a2:	6923      	ldr	r3, [r4, #16]
    25a4:	4798      	blx	r3
		for (uint8_t i = 0; i < s_count_serials_with_serial_events; i++) {
    25a6:	f898 2000 	ldrb.w	r2, [r8]
    25aa:	b2eb      	uxtb	r3, r5
    25ac:	429a      	cmp	r2, r3
    25ae:	d8f0      	bhi.n	2592 <yield+0x7e>
	if (yield_active_check_flags & YIELD_CHECK_EVENT_RESPONDER) EventResponder::runFromYield();
    25b0:	7833      	ldrb	r3, [r6, #0]
    25b2:	e7bd      	b.n	2530 <yield+0x1c>
        virtual int available() { return usb_serial_available(); }
    25b4:	f7ff fe76 	bl	22a4 <usb_serial_available>
		if (Serial.available()) serialEvent();
    25b8:	b968      	cbnz	r0, 25d6 <yield+0xc2>
		if (_serialEvent_default) yield_active_check_flags &= ~YIELD_CHECK_USB_SERIAL;
    25ba:	4b0e      	ldr	r3, [pc, #56]	; (25f4 <yield+0xe0>)
    25bc:	781b      	ldrb	r3, [r3, #0]
    25be:	b90b      	cbnz	r3, 25c4 <yield+0xb0>
    25c0:	7833      	ldrb	r3, [r6, #0]
    25c2:	e7b3      	b.n	252c <yield+0x18>
    25c4:	7833      	ldrb	r3, [r6, #0]
    25c6:	f003 03fe 	and.w	r3, r3, #254	; 0xfe
    25ca:	7033      	strb	r3, [r6, #0]
    25cc:	e7ae      	b.n	252c <yield+0x18>
		if (doit) __enable_irq();
    25ce:	2d00      	cmp	r5, #0
    25d0:	d1b2      	bne.n	2538 <yield+0x24>
    25d2:	b662      	cpsie	i
    25d4:	e7b0      	b.n	2538 <yield+0x24>
		if (Serial.available()) serialEvent();
    25d6:	f000 f841 	bl	265c <serialEvent()>
    25da:	e7ee      	b.n	25ba <yield+0xa6>
			lastYield = nullptr;
    25dc:	4b06      	ldr	r3, [pc, #24]	; (25f8 <yield+0xe4>)
    25de:	601a      	str	r2, [r3, #0]
    25e0:	e7c5      	b.n	256e <yield+0x5a>
    25e2:	bf00      	nop
    25e4:	1fff0c20 	.word	0x1fff0c20
    25e8:	1fff102d 	.word	0x1fff102d
    25ec:	1fff1030 	.word	0x1fff1030
    25f0:	1fff103c 	.word	0x1fff103c
    25f4:	00002bac 	.word	0x00002bac
    25f8:	1fff1040 	.word	0x1fff1040
    25fc:	1fff105c 	.word	0x1fff105c
    2600:	1fff1044 	.word	0x1fff1044

00002604 <EventResponder::runFromInterrupt()>:
{
	EventResponder::runFromInterrupt();
}

void EventResponder::runFromInterrupt()
{
    2604:	b570      	push	{r4, r5, r6, lr}
		__asm__ volatile("mrs %0, primask\n" : "=r" (primask)::);
    2606:	f3ef 8210 	mrs	r2, PRIMASK
		__disable_irq();
    260a:	b672      	cpsid	i
	while (1) {
		bool irq = disableInterrupts();
		EventResponder *first = firstInterrupt;
    260c:	4c0c      	ldr	r4, [pc, #48]	; (2640 <EventResponder::runFromInterrupt()+0x3c>)
    260e:	6820      	ldr	r0, [r4, #0]
		if (first) {
    2610:	b180      	cbz	r0, 2634 <EventResponder::runFromInterrupt()+0x30>
			firstInterrupt = first->_next;
			if (firstInterrupt) {
				firstInterrupt->_prev = nullptr;
			} else {
				lastInterrupt = nullptr;
    2612:	4e0c      	ldr	r6, [pc, #48]	; (2644 <EventResponder::runFromInterrupt()+0x40>)
				firstInterrupt->_prev = nullptr;
    2614:	2500      	movs	r5, #0
			firstInterrupt = first->_next;
    2616:	6943      	ldr	r3, [r0, #20]
    2618:	6023      	str	r3, [r4, #0]
			if (firstInterrupt) {
    261a:	b173      	cbz	r3, 263a <EventResponder::runFromInterrupt()+0x36>
				firstInterrupt->_prev = nullptr;
    261c:	619d      	str	r5, [r3, #24]
		if (doit) __enable_irq();
    261e:	b902      	cbnz	r2, 2622 <EventResponder::runFromInterrupt()+0x1e>
    2620:	b662      	cpsie	i
			}
			enableInterrupts(irq);
			first->_triggered = false;
			(*(first->_function))(*first);
    2622:	6883      	ldr	r3, [r0, #8]
			first->_triggered = false;
    2624:	7745      	strb	r5, [r0, #29]
			(*(first->_function))(*first);
    2626:	4798      	blx	r3
		__asm__ volatile("mrs %0, primask\n" : "=r" (primask)::);
    2628:	f3ef 8210 	mrs	r2, PRIMASK
		__disable_irq();
    262c:	b672      	cpsid	i
		EventResponder *first = firstInterrupt;
    262e:	6820      	ldr	r0, [r4, #0]
		if (first) {
    2630:	2800      	cmp	r0, #0
    2632:	d1f0      	bne.n	2616 <EventResponder::runFromInterrupt()+0x12>
		if (doit) __enable_irq();
    2634:	b902      	cbnz	r2, 2638 <EventResponder::runFromInterrupt()+0x34>
    2636:	b662      	cpsie	i
		} else {
			enableInterrupts(irq);
			break;
		}
	}
}
    2638:	bd70      	pop	{r4, r5, r6, pc}
				lastInterrupt = nullptr;
    263a:	6033      	str	r3, [r6, #0]
    263c:	e7ef      	b.n	261e <EventResponder::runFromInterrupt()+0x1a>
    263e:	bf00      	nop
    2640:	1fff1038 	.word	0x1fff1038
    2644:	1fff1034 	.word	0x1fff1034

00002648 <pendablesrvreq_isr>:
	EventResponder::runFromInterrupt();
    2648:	f7ff bfdc 	b.w	2604 <EventResponder::runFromInterrupt()>

0000264c <systick_isr>:

extern "C" volatile uint32_t systick_millis_count;

void systick_isr(void)
{
	systick_millis_count++;
    264c:	4a02      	ldr	r2, [pc, #8]	; (2658 <systick_isr+0xc>)
    264e:	6813      	ldr	r3, [r2, #0]
    2650:	3301      	adds	r3, #1
    2652:	6013      	str	r3, [r2, #0]
}
    2654:	4770      	bx	lr
    2656:	bf00      	nop
    2658:	1fff0d88 	.word	0x1fff0d88

0000265c <serialEvent()>:

#include <Arduino.h>
void serialEvent() __attribute__((weak));
void serialEvent() {
}
    265c:	4770      	bx	lr
    265e:	bf00      	nop

00002660 <usb_init_serialnumber>:
	{'M','T','P'}
};
#endif

void usb_init_serialnumber(void)
{
    2660:	b510      	push	{r4, lr}
    2662:	b084      	sub	sp, #16
	char buf[11];
	uint32_t i, num;

	__disable_irq();
    2664:	b672      	cpsid	i
	FTFL_FSTAT = FTFL_FSTAT_CCIF;
	while (!(FTFL_FSTAT & FTFL_FSTAT_CCIF)) ; // wait
	num = *(uint32_t *)&FTFL_FCCOB7;
#elif defined(HAS_KINETIS_FLASH_FTFE)
	kinetis_hsrun_disable();
	FTFL_FSTAT = FTFL_FSTAT_RDCOLERR | FTFL_FSTAT_ACCERR | FTFL_FSTAT_FPVIOL;
    2666:	4a16      	ldr	r2, [pc, #88]	; (26c0 <usb_init_serialnumber+0x60>)
	*(uint32_t *)&FTFL_FCCOB3 = 0x41070000;
    2668:	4916      	ldr	r1, [pc, #88]	; (26c4 <usb_init_serialnumber+0x64>)
    266a:	6051      	str	r1, [r2, #4]
	FTFL_FSTAT = FTFL_FSTAT_RDCOLERR | FTFL_FSTAT_ACCERR | FTFL_FSTAT_FPVIOL;
    266c:	2070      	movs	r0, #112	; 0x70
	FTFL_FSTAT = FTFL_FSTAT_CCIF;
    266e:	2380      	movs	r3, #128	; 0x80
	FTFL_FSTAT = FTFL_FSTAT_RDCOLERR | FTFL_FSTAT_ACCERR | FTFL_FSTAT_FPVIOL;
    2670:	7010      	strb	r0, [r2, #0]
	FTFL_FSTAT = FTFL_FSTAT_CCIF;
    2672:	7013      	strb	r3, [r2, #0]
	while (!(FTFL_FSTAT & FTFL_FSTAT_CCIF)) ; // wait
    2674:	7813      	ldrb	r3, [r2, #0]
    2676:	061b      	lsls	r3, r3, #24
    2678:	d5fc      	bpl.n	2674 <usb_init_serialnumber+0x14>
	num = *(uint32_t *)&FTFL_FCCOBB;
    267a:	68d0      	ldr	r0, [r2, #12]
	kinetis_hsrun_enable();
#endif
	__enable_irq();
    267c:	b662      	cpsie	i
	// add extra zero to work around OS-X CDC-ACM driver bug
	if (num < 10000000) num = num * 10;
    267e:	4b12      	ldr	r3, [pc, #72]	; (26c8 <usb_init_serialnumber+0x68>)
    2680:	4c12      	ldr	r4, [pc, #72]	; (26cc <usb_init_serialnumber+0x6c>)
    2682:	4298      	cmp	r0, r3
    2684:	bf38      	it	cc
    2686:	eb00 0080 	addcc.w	r0, r0, r0, lsl #2
	ultoa(num, buf, 10);
    268a:	a901      	add	r1, sp, #4
	if (num < 10000000) num = num * 10;
    268c:	bf38      	it	cc
    268e:	0040      	lslcc	r0, r0, #1
	ultoa(num, buf, 10);
    2690:	220a      	movs	r2, #10
    2692:	f7fe fb7d 	bl	d90 <ultoa>
	for (i=0; i<10; i++) {
    2696:	a901      	add	r1, sp, #4
    2698:	4620      	mov	r0, r4
    269a:	2300      	movs	r3, #0
		char c = buf[i];
    269c:	f811 2b01 	ldrb.w	r2, [r1], #1
	for (i=0; i<10; i++) {
    26a0:	3301      	adds	r3, #1
		if (!c) break;
    26a2:	b13a      	cbz	r2, 26b4 <usb_init_serialnumber+0x54>
	for (i=0; i<10; i++) {
    26a4:	2b0a      	cmp	r3, #10
		usb_string_serial_number_default.wString[i] = c;
    26a6:	f820 2f02 	strh.w	r2, [r0, #2]!
	for (i=0; i<10; i++) {
    26aa:	d1f7      	bne.n	269c <usb_init_serialnumber+0x3c>
    26ac:	2316      	movs	r3, #22
	}
	usb_string_serial_number_default.bLength = i * 2 + 2;
    26ae:	7023      	strb	r3, [r4, #0]
}
    26b0:	b004      	add	sp, #16
    26b2:	bd10      	pop	{r4, pc}
	usb_string_serial_number_default.bLength = i * 2 + 2;
    26b4:	005b      	lsls	r3, r3, #1
    26b6:	b2db      	uxtb	r3, r3
    26b8:	7023      	strb	r3, [r4, #0]
}
    26ba:	b004      	add	sp, #16
    26bc:	bd10      	pop	{r4, pc}
    26be:	bf00      	nop
    26c0:	40020000 	.word	0x40020000
    26c4:	41070000 	.word	0x41070000
    26c8:	00989680 	.word	0x00989680
    26cc:	1fff0d14 	.word	0x1fff0d14

000026d0 <memset>:
    26d0:	0783      	lsls	r3, r0, #30
    26d2:	b530      	push	{r4, r5, lr}
    26d4:	d048      	beq.n	2768 <memset+0x98>
    26d6:	1e54      	subs	r4, r2, #1
    26d8:	2a00      	cmp	r2, #0
    26da:	d03f      	beq.n	275c <memset+0x8c>
    26dc:	b2ca      	uxtb	r2, r1
    26de:	4603      	mov	r3, r0
    26e0:	e001      	b.n	26e6 <memset+0x16>
    26e2:	3c01      	subs	r4, #1
    26e4:	d33a      	bcc.n	275c <memset+0x8c>
    26e6:	f803 2b01 	strb.w	r2, [r3], #1
    26ea:	079d      	lsls	r5, r3, #30
    26ec:	d1f9      	bne.n	26e2 <memset+0x12>
    26ee:	2c03      	cmp	r4, #3
    26f0:	d92d      	bls.n	274e <memset+0x7e>
    26f2:	b2cd      	uxtb	r5, r1
    26f4:	ea45 2505 	orr.w	r5, r5, r5, lsl #8
    26f8:	2c0f      	cmp	r4, #15
    26fa:	ea45 4505 	orr.w	r5, r5, r5, lsl #16
    26fe:	d936      	bls.n	276e <memset+0x9e>
    2700:	f1a4 0210 	sub.w	r2, r4, #16
    2704:	f022 0c0f 	bic.w	ip, r2, #15
    2708:	f103 0e20 	add.w	lr, r3, #32
    270c:	44e6      	add	lr, ip
    270e:	ea4f 1c12 	mov.w	ip, r2, lsr #4
    2712:	f103 0210 	add.w	r2, r3, #16
    2716:	e942 5504 	strd	r5, r5, [r2, #-16]
    271a:	e942 5502 	strd	r5, r5, [r2, #-8]
    271e:	3210      	adds	r2, #16
    2720:	4572      	cmp	r2, lr
    2722:	d1f8      	bne.n	2716 <memset+0x46>
    2724:	f10c 0201 	add.w	r2, ip, #1
    2728:	f014 0f0c 	tst.w	r4, #12
    272c:	eb03 1202 	add.w	r2, r3, r2, lsl #4
    2730:	f004 0c0f 	and.w	ip, r4, #15
    2734:	d013      	beq.n	275e <memset+0x8e>
    2736:	f1ac 0304 	sub.w	r3, ip, #4
    273a:	f023 0303 	bic.w	r3, r3, #3
    273e:	3304      	adds	r3, #4
    2740:	4413      	add	r3, r2
    2742:	f842 5b04 	str.w	r5, [r2], #4
    2746:	4293      	cmp	r3, r2
    2748:	d1fb      	bne.n	2742 <memset+0x72>
    274a:	f00c 0403 	and.w	r4, ip, #3
    274e:	b12c      	cbz	r4, 275c <memset+0x8c>
    2750:	b2c9      	uxtb	r1, r1
    2752:	441c      	add	r4, r3
    2754:	f803 1b01 	strb.w	r1, [r3], #1
    2758:	429c      	cmp	r4, r3
    275a:	d1fb      	bne.n	2754 <memset+0x84>
    275c:	bd30      	pop	{r4, r5, pc}
    275e:	4664      	mov	r4, ip
    2760:	4613      	mov	r3, r2
    2762:	2c00      	cmp	r4, #0
    2764:	d1f4      	bne.n	2750 <memset+0x80>
    2766:	e7f9      	b.n	275c <memset+0x8c>
    2768:	4603      	mov	r3, r0
    276a:	4614      	mov	r4, r2
    276c:	e7bf      	b.n	26ee <memset+0x1e>
    276e:	461a      	mov	r2, r3
    2770:	46a4      	mov	ip, r4
    2772:	e7e0      	b.n	2736 <memset+0x66>

00002774 <__libc_init_array>:
    2774:	b570      	push	{r4, r5, r6, lr}
    2776:	4e0d      	ldr	r6, [pc, #52]	; (27ac <__libc_init_array+0x38>)
    2778:	4d0d      	ldr	r5, [pc, #52]	; (27b0 <__libc_init_array+0x3c>)
    277a:	1b76      	subs	r6, r6, r5
    277c:	10b6      	asrs	r6, r6, #2
    277e:	d006      	beq.n	278e <__libc_init_array+0x1a>
    2780:	2400      	movs	r4, #0
    2782:	f855 3b04 	ldr.w	r3, [r5], #4
    2786:	3401      	adds	r4, #1
    2788:	4798      	blx	r3
    278a:	42a6      	cmp	r6, r4
    278c:	d1f9      	bne.n	2782 <__libc_init_array+0xe>
    278e:	4e09      	ldr	r6, [pc, #36]	; (27b4 <__libc_init_array+0x40>)
    2790:	4d09      	ldr	r5, [pc, #36]	; (27b8 <__libc_init_array+0x44>)
    2792:	1b76      	subs	r6, r6, r5
    2794:	f000 fa40 	bl	2c18 <_init>
    2798:	10b6      	asrs	r6, r6, #2
    279a:	d006      	beq.n	27aa <__libc_init_array+0x36>
    279c:	2400      	movs	r4, #0
    279e:	f855 3b04 	ldr.w	r3, [r5], #4
    27a2:	3401      	adds	r4, #1
    27a4:	4798      	blx	r3
    27a6:	42a6      	cmp	r6, r4
    27a8:	d1f9      	bne.n	279e <__libc_init_array+0x2a>
    27aa:	bd70      	pop	{r4, r5, r6, pc}
    27ac:	00002c24 	.word	0x00002c24
    27b0:	00002c24 	.word	0x00002c24
    27b4:	00002c2c 	.word	0x00002c2c
    27b8:	00002c24 	.word	0x00002c24

000027bc <__retarget_lock_acquire_recursive>:
    27bc:	4770      	bx	lr
    27be:	bf00      	nop

000027c0 <__retarget_lock_release_recursive>:
    27c0:	4770      	bx	lr
    27c2:	bf00      	nop

000027c4 <register_fini>:
    27c4:	4b02      	ldr	r3, [pc, #8]	; (27d0 <register_fini+0xc>)
    27c6:	b113      	cbz	r3, 27ce <register_fini+0xa>
    27c8:	4802      	ldr	r0, [pc, #8]	; (27d4 <register_fini+0x10>)
    27ca:	f000 b805 	b.w	27d8 <atexit>
    27ce:	4770      	bx	lr
    27d0:	00000000 	.word	0x00000000
    27d4:	000027e5 	.word	0x000027e5

000027d8 <atexit>:
    27d8:	2300      	movs	r3, #0
    27da:	4601      	mov	r1, r0
    27dc:	461a      	mov	r2, r3
    27de:	4618      	mov	r0, r3
    27e0:	f000 b814 	b.w	280c <__register_exitproc>

000027e4 <__libc_fini_array>:
    27e4:	b538      	push	{r3, r4, r5, lr}
    27e6:	4d07      	ldr	r5, [pc, #28]	; (2804 <__libc_fini_array+0x20>)
    27e8:	4c07      	ldr	r4, [pc, #28]	; (2808 <__libc_fini_array+0x24>)
    27ea:	1b2c      	subs	r4, r5, r4
    27ec:	10a4      	asrs	r4, r4, #2
    27ee:	d005      	beq.n	27fc <__libc_fini_array+0x18>
    27f0:	3c01      	subs	r4, #1
    27f2:	f855 3d04 	ldr.w	r3, [r5, #-4]!
    27f6:	4798      	blx	r3
    27f8:	2c00      	cmp	r4, #0
    27fa:	d1f9      	bne.n	27f0 <__libc_fini_array+0xc>
    27fc:	e8bd 4038 	ldmia.w	sp!, {r3, r4, r5, lr}
    2800:	f000 ba14 	b.w	2c2c <__init_array_end>
	...

0000280c <__register_exitproc>:
    280c:	e92d 47f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, lr}
    2810:	f8df a07c 	ldr.w	sl, [pc, #124]	; 2890 <__register_exitproc+0x84>
    2814:	4606      	mov	r6, r0
    2816:	f8da 0000 	ldr.w	r0, [sl]
    281a:	4698      	mov	r8, r3
    281c:	460f      	mov	r7, r1
    281e:	4691      	mov	r9, r2
    2820:	f7ff ffcc 	bl	27bc <__retarget_lock_acquire_recursive>
    2824:	4b18      	ldr	r3, [pc, #96]	; (2888 <__register_exitproc+0x7c>)
    2826:	681c      	ldr	r4, [r3, #0]
    2828:	b324      	cbz	r4, 2874 <__register_exitproc+0x68>
    282a:	6865      	ldr	r5, [r4, #4]
    282c:	2d1f      	cmp	r5, #31
    282e:	dc24      	bgt.n	287a <__register_exitproc+0x6e>
    2830:	b95e      	cbnz	r6, 284a <__register_exitproc+0x3e>
    2832:	1c6b      	adds	r3, r5, #1
    2834:	3502      	adds	r5, #2
    2836:	f8da 0000 	ldr.w	r0, [sl]
    283a:	6063      	str	r3, [r4, #4]
    283c:	f844 7025 	str.w	r7, [r4, r5, lsl #2]
    2840:	f7ff ffbe 	bl	27c0 <__retarget_lock_release_recursive>
    2844:	2000      	movs	r0, #0
    2846:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
    284a:	eb04 0185 	add.w	r1, r4, r5, lsl #2
    284e:	2301      	movs	r3, #1
    2850:	f8c1 9088 	str.w	r9, [r1, #136]	; 0x88
    2854:	f8d4 2188 	ldr.w	r2, [r4, #392]	; 0x188
    2858:	40ab      	lsls	r3, r5
    285a:	431a      	orrs	r2, r3
    285c:	2e02      	cmp	r6, #2
    285e:	f8c4 2188 	str.w	r2, [r4, #392]	; 0x188
    2862:	f8c1 8108 	str.w	r8, [r1, #264]	; 0x108
    2866:	d1e4      	bne.n	2832 <__register_exitproc+0x26>
    2868:	f8d4 218c 	ldr.w	r2, [r4, #396]	; 0x18c
    286c:	431a      	orrs	r2, r3
    286e:	f8c4 218c 	str.w	r2, [r4, #396]	; 0x18c
    2872:	e7de      	b.n	2832 <__register_exitproc+0x26>
    2874:	4c05      	ldr	r4, [pc, #20]	; (288c <__register_exitproc+0x80>)
    2876:	601c      	str	r4, [r3, #0]
    2878:	e7d7      	b.n	282a <__register_exitproc+0x1e>
    287a:	f8da 0000 	ldr.w	r0, [sl]
    287e:	f7ff ff9f 	bl	27c0 <__retarget_lock_release_recursive>
    2882:	f04f 30ff 	mov.w	r0, #4294967295
    2886:	e7de      	b.n	2846 <__register_exitproc+0x3a>
    2888:	1fff1064 	.word	0x1fff1064
    288c:	1fff1068 	.word	0x1fff1068
    2890:	1fff0d2c 	.word	0x1fff0d2c
    2894:	202d2d2d 	.word	0x202d2d2d
    2898:	20434144 	.word	0x20434144
    289c:	2058554d 	.word	0x2058554d
    28a0:	74736554 	.word	0x74736554
    28a4:	696e4920 	.word	0x696e4920
    28a8:	6c616974 	.word	0x6c616974
    28ac:	64657a69 	.word	0x64657a69
    28b0:	2d2d2d20 	.word	0x2d2d2d20
    28b4:	00000000 	.word	0x00000000
    28b8:	64616552 	.word	0x64616552
    28bc:	20676e69 	.word	0x20676e69
    28c0:	73726966 	.word	0x73726966
    28c4:	20342074 	.word	0x20342074
    28c8:	6e616863 	.word	0x6e616863
    28cc:	736c656e 	.word	0x736c656e
    28d0:	30412820 	.word	0x30412820
    28d4:	2933412d 	.word	0x2933412d
    28d8:	0000002e 	.word	0x0000002e
    28dc:	6d726f46 	.word	0x6d726f46
    28e0:	203a7461 	.word	0x203a7461
    28e4:	67726154 	.word	0x67726154
    28e8:	7c207465 	.word	0x7c207465
    28ec:	61654d20 	.word	0x61654d20
    28f0:	65727573 	.word	0x65727573
    28f4:	207c2064 	.word	0x207c2064
    28f8:	6f727245 	.word	0x6f727245
    28fc:	ffff0072 	.word	0xffff0072
    2900:	002d2d2d 	.word	0x002d2d2d
    2904:	00206843 	.word	0x00206843
    2908:	0000203a 	.word	0x0000203a
    290c:	3a746754 	.word	0x3a746754
    2910:	00000020 	.word	0x00000020
    2914:	4d207c09 	.word	0x4d207c09
    2918:	3a736165 	.word	0x3a736165
    291c:	00000020 	.word	0x00000020
    2920:	45207c09 	.word	0x45207c09
    2924:	203a7272 	.word	0x203a7272
    2928:	ffffff00 	.word	0xffffff00

0000292c <TEST_PINS>:
    292c:	00000012 0000000f 00000010 00000011     ................

0000293c <pin2sc1a>:
    293c:	09080e05 07060c0d 9303040f 0e058f8e     ................
    294c:	0c0d0908 040f0706 ffffffff 8effffff     ................
    295c:	8412118f 91878685 ffffffff ffffffff     ................
    296c:	ff8b8aff ffffffff ffffffff ffffffff     ................
    297c:	97179303 921a8101                       ........

00002984 <digital_pin_to_info_PGM>:
    2984:	43fe0840 4004a040 43fe0844 4004a044     @..C@..@D..CD..@
    2994:	43fe1800 4004c000 43fe0030 40049030     ...C...@0..C0..@
    29a4:	43fe0034 40049034 43fe181c 4004c01c     4..C4..@...C...@
    29b4:	43fe1810 4004c010 43fe1808 4004c008     ...C...@...C...@
    29c4:	43fe180c 4004c00c 43fe100c 4004b00c     ...C...@...C...@
    29d4:	43fe1010 4004b010 43fe1018 4004b018     ...C...@...C...@
    29e4:	43fe101c 4004b01c 43fe1014 4004b014     ...C...@...C...@
    29f4:	43fe1804 4004c004 43fe1000 4004b000     ...C...@...C...@
    2a04:	43fe0800 4004a000 43fe0804 4004a004     ...C...@...C...@
    2a14:	43fe080c 4004a00c 43fe0808 4004a008     ...C...@...C...@
    2a24:	43fe1814 4004c014 43fe1818 4004c018     ...C...@...C...@
    2a34:	43fe1004 4004b004 43fe1008 4004b008     ...C...@...C...@
    2a44:	43fe2068 4004d068 43fe0014 40049014     h .Ch..@...C...@
    2a54:	43fe0038 40049038 43fe003c 4004903c     8..C8..@<..C<..@
    2a64:	43fe0040 40049040 43fe0848 4004a048     @..C@..@H..CH..@
    2a74:	43fe084c 4004a04c 43fe0828 4004a028     L..CL..@(..C(..@
    2a84:	43fe082c 4004a02c 43fe2060 4004d060     ,..C,..@` .C`..@
    2a94:	43fe2064 4004d064 43fe1020 4004b020     d .Cd..@ ..C ..@
    2aa4:	43fe1024 4004b024 43fe1028 4004b028     $..C$..@(..C(..@
    2ab4:	43fe102c 4004b02c 43fe0044 40049044     ,..C,..@D..CD..@
    2ac4:	43fe0070 40049070 43fe0074 40049074     <EMAIL>..@
    2ad4:	43fe0068 40049068 43fe0850 4004a050     <EMAIL>..@
    2ae4:	43fe0858 4004a058 43fe085c 4004a05c     X..CX..@\..C\..@
    2af4:	43fe0854 4004a054 43fe1820 4004c020     T..CT..@ ..C ..@
    2b04:	43fe1824 4004c024 43fe0810 4004a010     $..C$..@...C...@
    2b14:	43fe0814 4004a014 43fe1838 4004c038     ...C...@8..C8..@
    2b24:	43fe1834 4004c034 43fe1830 4004c030     4..C4..@0..C0..@
    2b34:	43fe183c 4004c03c 43fe182c 4004c02c     <..C<..@,..C,..@
    2b44:	43fe2028 4004d028 43fe202c 4004d02c     ( .C(..@, .C,..@
    2b54:	43fe2000 4004d000 43fe2004 4004d004     . .C...@. .C...@
    2b64:	43fe2008 4004d008 43fe200c 4004d00c     . .C...@. .C...@
    2b74:	43fe2010 4004d010 43fe2014 4004d014     . .C...@. .C...@

00002b84 <vtable for usb_serial_class>:
	...
    2b8c:	00001cf1 00001ce9 00001ce5 00001ce1     ................
    2b9c:	00001cdd 00001cd9 00001cd5 00001cd1     ................

00002bac <_serialEvent_default>:
    2bac:	ffffff00                                ....

00002bb0 <usb_descriptor_list>:
    2bb0:	00000100 1fff0cbc 00000012 00000200     ................
    2bc0:	1fff0c24 00000095 04090305 1fff0cec     $...............
    2bd0:	00000000 00000300 1fff0cd0 00000000     ................
    2be0:	04090301 1fff0cd4 00000000 04090302     ................
    2bf0:	1fff0cfc 00000000 04090303 1fff0d14     ................
	...

00002c10 <usb_endpoint_config_table>:
    2c10:	15151915 ffffff19                       ........

00002c18 <_init>:
    2c18:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
    2c1a:	bf00      	nop
    2c1c:	bcf8      	pop	{r3, r4, r5, r6, r7}
    2c1e:	bc08      	pop	{r3}
    2c20:	469e      	mov	lr, r3
    2c22:	4770      	bx	lr

00002c24 <__init_array_start>:
    2c24:	000027c5 	.word	0x000027c5

00002c28 <__frame_dummy_init_array_entry>:
    2c28:	00000435                                5...

Disassembly of section .fini:

00002c2c <_fini>:
    2c2c:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
    2c2e:	bf00      	nop
