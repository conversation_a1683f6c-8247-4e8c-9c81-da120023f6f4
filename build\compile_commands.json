[{"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\sketch\\DacMux.ino.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\sketch\\DacMux.ino.cpp.o"], "file": "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\sketch\\DacMux.ino.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial3.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\HardwareSerial3.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial3.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial5.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\HardwareSerial5.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial5.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\Stream.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\Stream.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\Stream.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\Time.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\Time.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\Time.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\Print.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\Print.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\Print.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial6.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\HardwareSerial6.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial6.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\IPAddress.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\IPAddress.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\IPAddress.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\AudioStream.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\AudioStream.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\AudioStream.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\EventResponder.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\EventResponder.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\EventResponder.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\IntervalTimer.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\IntervalTimer.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\IntervalTimer.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial4.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\HardwareSerial4.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial4.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial2.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\HardwareSerial2.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial2.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\CrashReport.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\CrashReport.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\CrashReport.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial1.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\HardwareSerial1.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial1.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\WMath.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\WMath.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\WMath.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\WString.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\WString.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\WString.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\Tone.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\Tone.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\Tone.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\eeprom.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\eeprom.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\eeprom.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\avr_emulation.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\avr_emulation.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\avr_emulation.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\main.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\main.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\main.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\analog.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\analog.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\analog.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\nonstd.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\nonstd.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\nonstd.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\keylayouts.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\keylayouts.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\keylayouts.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-x", "assembler-with-cpp", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\memcpy-armv7m.S", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\memcpy-armv7m.S.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\memcpy-armv7m.S"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\math_helper.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\math_helper.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\math_helper.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\HardwareSerial.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\HardwareSerial.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-x", "assembler-with-cpp", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\memset.S", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\memset.S.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\memset.S"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\new.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\new.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\new.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\mk20dx128.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\mk20dx128.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\mk20dx128.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\ser_print.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\ser_print.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\ser_print.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial2.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serial2.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial2.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent1.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serialEvent1.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent1.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serialEvent.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent4.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serialEvent4.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent4.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial3.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serial3.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial3.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent2.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serialEvent2.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent2.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\pins_teensy.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\pins_teensy.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\pins_teensy.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial5.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serial5.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial5.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial1.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serial1.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial1.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent5.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serialEvent5.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent5.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEventUSB1.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serialEventUSB1.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEventUSB1.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEventUSB2.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serialEventUSB2.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEventUSB2.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial4.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serial4.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial4.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial6_lpuart.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serial6_lpuart.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial6_lpuart.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial6.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serial6.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serial6.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent6.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serialEvent6.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent6.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_audio.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_audio.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_audio.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\touch.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\touch.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\touch.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent3.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\serialEvent3.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\serialEvent3.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_desc.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_desc.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_desc.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_inst.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_inst.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_inst.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_joystick.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_joystick.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_joystick.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_dev.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_dev.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_dev.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_midi.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_midi.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_midi.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_keyboard.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_keyboard.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_keyboard.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_mouse.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_mouse.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_mouse.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_mtp.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_mtp.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_mtp.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\yield.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\yield.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\yield.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_mem.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_mem.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_mem.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_serial.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_serial.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_serial.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_rawhid.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_rawhid.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_rawhid.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-g++", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-fno-exceptions", "-fpermissive", "-felide-constructors", "-std=gnu++17", "-Wno-error=narrowing", "-fno-rtti", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-ID:\\CodePCB\\Code\\teensy\\DacMux\\build/pch", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_flightsim.cpp", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_flightsim.cpp.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_flightsim.cpp"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_seremu.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_seremu.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_seremu.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_touch.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_touch.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_touch.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_serial2.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_serial2.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_serial2.c"}, {"directory": "d:\\CodePCB\\Code\\teensy\\DacMux", "arguments": ["C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1/arm/bin/arm-none-eabi-gcc", "-c", "-O2", "-g", "-Wall", "-ffunction-sections", "-fdata-sections", "-nostdlib", "-mno-unaligned-access", "-M<PERSON>", "-mthumb", "-mcpu=cortex-m4", "-mfloat-abi=hard", "-mfpu=fpv4-sp-d16", "-fsingle-precision-constant", "-D__MK64FX512__", "-DTEENSYDUINO=159", "-DARDUINO=10607", "-DARDUINO_TEENSY35", "-DF_CPU=120000000", "-DUSB_MIDI_SERIAL", "-DLAYOUT_US_ENGLISH", "-IC:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3", "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_serial3.c", "-o", "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\core\\usb_serial3.c.o"], "file": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3\\usb_serial3.c"}]