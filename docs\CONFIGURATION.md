# DacMux Configuration Guide

## Overview

The DacMux system provides multiple configuration options to adapt the system for different hardware setups, performance requirements, and use cases. This guide covers all configurable parameters and how to modify them.

## Compile-Time Configuration

### Pin Assignments

Pin assignments are defined as static constants in the header files and can be modified before compilation.

#### Multiplexer Control Pins (src/MuxControl.h)
```cpp
// CD4051 Multiplexer Pin Definitions
static constexpr int MUX_INH_PIN = 5;  // Inhibit pin
static constexpr int MUX_S0_PIN = 6;   // Channel select bit 0
static constexpr int MUX_S1_PIN = 7;   // Channel select bit 1  
static constexpr int MUX_S2_PIN = 8;   // Channel select bit 2
```

**Modification Instructions**:
1. Change pin numbers to match your hardware connections
2. Ensure pins are available and not used by other functions
3. Recompile and upload to apply changes

#### Test Framework Pins (src/TestFramework.h)
```cpp
// ADC pins for monitoring first 4 channels
static constexpr int TEST_PINS[4] = {A4, A1, A2, A3};
```

**Modification Instructions**:
1. Connect output channels to available ADC pins
2. Update array with your chosen pin assignments
3. Ensure pins support 12-bit ADC resolution

### Timing Parameters

Critical timing parameters control the precision and speed of the system.

#### State Machine Timing (src/StateMachine.h)
```cpp
// DAC settling time in microseconds
static constexpr unsigned long SETTLE_DURATION_US = 150;

// Sample-and-hold charging time in microseconds  
static constexpr unsigned long SAMPLE_DURATION_US = 66;
```

**Tuning Guidelines**:

**SETTLE_DURATION_US** (DAC Settling Time):
- **Default**: 150μs (conservative for high accuracy)
- **Faster**: 50-100μs (for higher update rates)
- **Slower**: 200-500μs (for very high precision or slow external circuits)
- **Factors**: DAC specifications, external filtering, buffer amplifiers

**SAMPLE_DURATION_US** (S&H Charging Time):
- **Default**: 66μs (typical for standard S&H circuits)
- **Faster**: 10-30μs (for low-impedance S&H circuits)
- **Slower**: 100-200μs (for high-impedance or large capacitor S&H circuits)
- **Factors**: S&H circuit design, hold capacitor size, charging resistor value

#### Test Framework Timing (src/TestFramework.h)
```cpp
// Test report output interval in milliseconds
static constexpr unsigned long REPORT_INTERVAL_MS = 250;
```

**Options**:
- **Fast**: 100ms (more frequent updates, higher serial traffic)
- **Standard**: 250ms (good balance)
- **Slow**: 500-1000ms (less frequent updates, lower overhead)

### Feature Enable/Disable

#### Testing Framework (src/TestFramework.h)
```cpp
// Enable or disable testing functionality
static constexpr bool ENABLE_TESTING = true;
```

**Options**:
- **true**: Enable testing and serial output (development/debugging)
- **false**: Disable testing (production use, better performance)

## Runtime Configuration

### Signal Source Routing

The flexible routing system allows any signal source to be assigned to any output channel at runtime.

#### Default Routing (DacMux.ino setup())
```cpp
// Configure default signal routing assignments
dacControl.assignSourceToOutput(0, DacControl::DAC_SRC_MIDI_CC);  // Ch0: MIDI CC20
dacControl.assignSourceToOutput(1, DacControl::DAC_SRC_LFO);      // Ch1: LFO
dacControl.assignSourceToOutput(2, DacControl::DAC_SRC_1);        // Ch2: Static source 1
dacControl.assignSourceToOutput(3, DacControl::DAC_SRC_2);        // Ch3: Static source 2
// ... continue for channels 4-7
```

#### Available Signal Sources
- **DAC_SRC_MIDI_CC**: MIDI Control Change values (CC 20-27)
- **DAC_SRC_LFO**: Low Frequency Oscillator sine waves
- **DAC_SRC_1**: Static source 1 (stepped pattern)
- **DAC_SRC_2**: Static source 2 (inverted stepped pattern)
- **DAC_SRC_NONE**: Disabled (0V output)

#### Dynamic Routing Changes
```cpp
// Change routing at runtime
dacControl.assignSourceToOutput(channel, source);

// Example: Switch channel 3 from static to MIDI control
dacControl.assignSourceToOutput(3, DacControl::DAC_SRC_MIDI_CC);
```

### LFO Configuration

LFO frequencies are set in the DacControl constructor and can be modified for different modulation rates.

#### LFO Frequency Setup (src/DacControl.cpp)
```cpp
// LFO initial state - {phase, frequency_Hz, value}
lfo[0] = {0, 0.2f, 0};   // 0.2 Hz
lfo[1] = {0, 0.25f, 0};  // 0.25 Hz  
lfo[2] = {0, 0.33f, 0};  // 0.33 Hz
lfo[3] = {0, 0.4f, 0};   // 0.4 Hz
lfo[4] = {0, 0.5f, 0};   // 0.5 Hz
lfo[5] = {0, 0.6f, 0};   // 0.6 Hz
lfo[6] = {0, 0.75f, 0};  // 0.75 Hz
lfo[7] = {0, 1.0f, 0};   // 1.0 Hz
```

**Frequency Ranges**:
- **Very Slow**: 0.01-0.1 Hz (long-term modulation)
- **Slow**: 0.1-1 Hz (typical modulation rates)
- **Medium**: 1-10 Hz (vibrato, tremolo effects)
- **Fast**: 10-50 Hz (audio rate, special effects)

**Modification Instructions**:
1. Edit frequency values in DacControl constructor
2. Recompile and upload
3. All LFOs will use new frequencies after restart

### Static Source Configuration

Static sources provide fixed voltage patterns useful for testing and calibration.

#### Static Source 1 (src/DacControl.cpp)
```cpp
uint16_t DacControl::getDacSrc1Value(uint8_t channel) {
    return 1024 + (channel * 128);  // Stepped pattern: 1024-1920
}
```

#### Static Source 2 (src/DacControl.cpp)
```cpp
uint16_t DacControl::getDacSrc2Value(uint8_t channel) {
    return 2048 - (channel * 128);  // Inverted pattern: 2048-1152
}
```

**Customization Examples**:
```cpp
// Fixed voltage (all channels same)
return 2048;  // Mid-scale voltage

// Linear ramp
return (channel * 512);  // 0V to ~2.5V across channels

// Exponential pattern
return (uint16_t)(1024 * pow(1.2, channel));

// Sine wave pattern (static across channels)
return (uint16_t)(2048 + 1024 * sin(channel * M_PI / 4));
```

## Hardware Configuration

### Sample-and-Hold Circuit Requirements

The system requires external sample-and-hold circuits for each output channel.

#### Basic S&H Circuit
```
DAC Output ──[R]──┬──── Output
                  │
                 [C]
                  │
                 GND
```

**Component Selection**:
- **R (Charging Resistor)**: 1kΩ - 10kΩ
  - Lower values: Faster charging, higher current
  - Higher values: Slower charging, lower current
- **C (Hold Capacitor)**: 100nF - 1μF
  - Smaller values: Faster charging, more droop
  - Larger values: Slower charging, less droop

#### Advanced S&H Circuit
```
DAC Output ──[R]──┬──[Buffer]──── Output
                  │
                 [C]
                  │
                 GND
```

**Buffer Amplifier Benefits**:
- Prevents load-dependent droop
- Provides low output impedance
- Isolates hold capacitor from load

### Power Supply Considerations

#### Voltage Levels
- **3.3V Operation**: Direct connection to Teensy 3.6
- **5V Operation**: Requires level shifting for control signals
- **Dual Supply**: ±5V or ±12V for extended output range

#### Current Requirements
- **Teensy 3.6**: ~100mA typical
- **CD4051**: ~1mA typical
- **S&H Circuits**: Depends on buffer amplifiers and loads

## Performance Optimization

### Update Rate Optimization

The total system update rate is determined by timing parameters:

```
Per-Channel Rate = 1 / (SETTLE_DURATION_US + SAMPLE_DURATION_US)
Total Rate = Per-Channel Rate × 8 channels
```

**Current Settings**:
- SETTLE_DURATION_US = 150μs
- SAMPLE_DURATION_US = 66μs
- Per-Channel Rate = 1/(150+66)μs = 4.6kHz
- Total Rate = 4.6kHz × 8 = 37kHz

**Optimization Strategies**:
1. **Reduce Settling Time**: Faster DAC settling, better S&H circuits
2. **Reduce Sample Time**: Lower impedance S&H circuits
3. **Hardware Improvements**: Faster DAC, better analog design

### Memory Optimization

#### Disable Unused Features
```cpp
// Disable testing for production use
static constexpr bool ENABLE_TESTING = false;
```

#### Optimize LFO Calculations
- Use lookup tables for sine calculations
- Reduce LFO update frequency if not needed
- Simplify waveform calculations

### Accuracy Optimization

#### Improve Settling Time
- Increase SETTLE_DURATION_US for better accuracy
- Add external DAC buffer amplifier
- Improve power supply filtering

#### Calibration
- Measure actual output voltages
- Apply correction factors in software
- Use precision voltage references

## Troubleshooting Configuration Issues

### Common Problems

#### Slow Update Rate
- **Cause**: Timing parameters too conservative
- **Solution**: Reduce SETTLE_DURATION_US and SAMPLE_DURATION_US
- **Test**: Monitor with oscilloscope

#### Inaccurate Outputs
- **Cause**: Insufficient settling time
- **Solution**: Increase SETTLE_DURATION_US
- **Test**: Use built-in test framework

#### MIDI Not Responding
- **Cause**: USB configuration or routing issues
- **Solution**: Check USB MIDI settings, verify routing assignments
- **Test**: Send known CC values and monitor serial output

#### Test Framework Not Working
- **Cause**: Pin connections or ADC configuration
- **Solution**: Verify TEST_PINS connections and enable testing
- **Test**: Check serial output for test data

### Configuration Validation

#### Verify Pin Assignments
1. Check all pin definitions match hardware
2. Ensure no pin conflicts with other functions
3. Test basic GPIO operations

#### Validate Timing Parameters
1. Use oscilloscope to measure actual timing
2. Verify DAC settling with different values
3. Check S&H circuit charging time

#### Test Signal Routing
1. Assign known sources to outputs
2. Measure outputs with multimeter or scope
3. Verify routing changes take effect

## Advanced Configuration

### Custom Signal Sources

Add new signal sources by extending the system:

1. **Add Enum Value**:
```cpp
enum DacDataSource {
    // ... existing sources
    DAC_SRC_CUSTOM,  // New custom source
};
```

2. **Implement Source Function**:
```cpp
static uint16_t getCustomValue(uint8_t channel) {
    // Your custom signal generation code
    return customValue;
}
```

3. **Update Function Table**:
```cpp
sourceFuncs[5] = getCustomValue;  // Add to table
```

### Multiple System Integration

For systems requiring more than 8 channels:

1. **Multiple Teensy Boards**: Each handling 8 channels
2. **MIDI Daisy Chain**: Route MIDI to multiple systems
3. **Synchronized Operation**: Use external clock signals

### External Control Integration

#### SPI/I2C Control
- Add external control interfaces
- Implement command parsing
- Integrate with existing routing system

#### Analog Control Inputs
- Use additional ADC channels
- Map analog inputs to parameters
- Implement real-time parameter control
