// MidiHandler.cpp
// Implements MidiHandler class: encapsulates MIDI receive/parse, mapping to DAC channels, and value storage

#include "MidiHandler.h"

MidiHandler* MidiHandler::instance = nullptr;

MidiHandler::MidiHandler() {
    instance = this;
    for (int i = 0; i < 8; ++i) cc[i] = 0;
}

void MidiHandler::init() {
    instance = this;
    // Set up MIDI handler, if needed
    usbMIDI.setHandleControlChange(staticControlChange);
}

void MidiHandler::update() {
    // Poll MIDI input (non-blocking)
    usbMIDI.read();
}

void MidiHandler::controlChange(byte channel, byte control, byte value) {
    if (control >= 20 && control <= 27) {
        uint8_t idx = control - 20;
        cc[idx] = value;
        // Optionally, update DAC buffer here if needed
    }
}

void MidiHandler::staticControlChange(byte channel, byte control, byte value) {
    if (instance) {
        instance->controlChange(channel, control, value);
    }
}

uint8_t MidiHandler::getCC(uint8_t idx) const {
    return (idx < 8) ? cc[idx] : 0;
}