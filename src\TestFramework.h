#ifndef TESTFRAMEWORK_H
#define TESTFRAMEWORK_H

#include <Arduino.h>

/**
 * @class TestFramework
 * @brief Encapsulates measures, error calculation, test pin IO, and reporting.
 *
 * Usage:
 *   - Create a single instance of TestFramework at the appropriate scope.
 *   - Use public methods to initialize and update the test framework.
 *   - All state and pins are encapsulated.
 */
class TestFramework {
public:
    TestFramework(DacControl& dac);

    // Initialization
    void init();

    // Main update loop
    void update();

    // Run a test cycle
    void runTesting();

    // Print test results
    void printTestResults();

private:
    DacControl& dacControl;

    // Enable or disable testing
    static constexpr bool ENABLE_TESTING = true;

    // Pins to read the first 4 MUX outputs (Y0, Y1, Y2, Y3)
    static constexpr int TEST_PINS[4] = {A4, A1, A2, A3};

    // Test framework state (was TestFrameworkState)
    uint16_t measuredValues[4];
    int16_t errorValues[4];
    unsigned long lastReportTime;

    // Report interval
    static constexpr unsigned long REPORT_INTERVAL_MS = 250; // ms
};

#endif // TESTFRAMEWORK_H