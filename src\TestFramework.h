#ifndef TESTFRAMEWORK_H
#define TESTFRAMEWORK_H

#include <Arduino.h>

// Forward declaration to avoid circular includes
class DacControl;

/**
 * @class TestFramework
 * @brief Real-time accuracy monitoring and validation system for DAC outputs
 *
 * The TestFramework class provides comprehensive testing and monitoring
 * capabilities for the DacMux system. It continuously measures the actual
 * analog output voltages and compares them to the intended DAC values,
 * providing real-time accuracy feedback via serial output.
 *
 * Key Features:
 * - Real-time voltage measurement of first 4 DAC channels
 * - Automatic error calculation (target vs. measured)
 * - Periodic reporting via Serial interface
 * - Non-blocking operation for continuous monitoring
 * - 12-bit ADC resolution matching DAC resolution
 *
 * Hardware Requirements:
 * The test framework requires analog input connections from the first 4
 * DAC output channels to specific Teensy ADC pins:
 * - Channel 0 (Y0) → A4
 * - Channel 1 (Y1) → A1
 * - Channel 2 (Y2) → A2
 * - Channel 3 (Y3) → A3
 *
 * These connections should be made after the sample-and-hold circuits
 * to measure the actual held voltages that represent the DAC outputs.
 *
 * Serial Output Format:
 * The framework outputs test results in a tabular format:
 * ```
 * Target | Measured | Error
 * 2048   | 2045     | -3
 * 1024   | 1027     | 3
 * ```
 *
 * @note Testing can be disabled by setting ENABLE_TESTING to false
 * @note Serial output is at 115200 baud with 2-second connection timeout
 * @note Only the first 4 channels are monitored due to ADC pin limitations
 *
 * Usage Example:
 * @code
 * DacControl dac;
 * TestFramework test(dac);
 * test.init();
 * // In main loop:
 * test.update();
 * @endcode
 */
class TestFramework {
public:
    /**
     * @brief Constructor - establishes reference to DAC control system
     *
     * @param dac Reference to DacControl instance for accessing target values
     */
    TestFramework(DacControl& dac);

    /**
     * @brief Initialize testing framework and serial communication
     *
     * Sets up serial communication at 115200 baud, configures ADC resolution
     * to 12 bits (matching DAC resolution), and displays initialization
     * messages. Includes a 2-second timeout for serial connection to allow
     * operation without a connected computer.
     *
     * @note Only initializes if ENABLE_TESTING is true
     * @note Serial connection timeout prevents blocking when no computer is connected
     */
    void init();

    /**
     * @brief Main update function for continuous testing
     *
     * Non-blocking function that should be called regularly from the main
     * loop. Manages the testing cycle and periodic reporting based on
     * the configured report interval.
     *
     * @note Only active when ENABLE_TESTING is true
     * @note Function returns immediately when testing is disabled
     */
    void update();

    /**
     * @brief Execute one testing cycle
     *
     * Checks if enough time has elapsed since the last report and triggers
     * a new test result printout if the report interval has passed. This
     * function manages the timing for periodic test reporting.
     *
     * @note Called automatically by update() function
     * @note Report frequency controlled by REPORT_INTERVAL_MS
     */
    void runTesting();

    /**
     * @brief Print current test results to serial output
     *
     * Outputs the current test measurements in a formatted table showing
     * target DAC values, measured ADC values, and calculated errors for
     * the first 4 channels. Format is optimized for easy reading and
     * data logging.
     *
     * Output Format:
     * - Target: Intended 12-bit DAC value (0-4095)
     * - Measured: Actual 12-bit ADC reading (0-4095)
     * - Error: Difference (Measured - Target)
     *
     * @note Only outputs data for channels 0-3
     * @note Positive error means measured > target, negative means measured < target
     */
    void printTestResults();

private:
    /**
     * @brief Reference to DAC control system
     *
     * Used to access current target DAC values for comparison with
     * measured values during accuracy testing.
     */
    DacControl& dacControl;

    /**
     * @brief Testing enable/disable flag
     *
     * Compile-time constant that enables or disables all testing functionality.
     * When false, all test functions return immediately without performing
     * any operations, minimizing performance impact.
     *
     * @note Set to false to disable testing and improve performance
     * @note Changing this requires recompilation
     */
    static constexpr bool ENABLE_TESTING = true;

    /**
     * @brief ADC input pin assignments for test measurements
     *
     * Array defining which Teensy ADC pins are connected to the first 4
     * DAC output channels for accuracy monitoring. These should be connected
     * to the output of the sample-and-hold circuits.
     *
     * Pin Mapping:
     * - TEST_PINS[0] = A4 → Channel 0 (Y0)
     * - TEST_PINS[1] = A1 → Channel 1 (Y1)
     * - TEST_PINS[2] = A2 → Channel 2 (Y2)
     * - TEST_PINS[3] = A3 → Channel 3 (Y3)
     */
    static constexpr int TEST_PINS[4] = {A4, A1, A2, A3};

    /**
     * @brief Array storing most recent ADC measurements
     *
     * Contains the latest 12-bit ADC readings from the test input pins.
     * Values are updated by the state machine during the sampling phase
     * and used for error calculation and reporting.
     */
    uint16_t measuredValues[4];

    /**
     * @brief Array storing calculated error values
     *
     * Contains the difference between target and measured values for each
     * monitored channel. Positive values indicate measured > target,
     * negative values indicate measured < target.
     */
    int16_t errorValues[4];

    /**
     * @brief Timestamp of last test report output
     *
     * Used to control the frequency of test result reporting. Prevents
     * overwhelming the serial output with too frequent updates while
     * maintaining regular monitoring feedback.
     */
    unsigned long lastReportTime;

    /**
     * @brief Test report output interval in milliseconds
     *
     * Controls how frequently test results are printed to the serial
     * output. 250ms provides good monitoring feedback without excessive
     * serial traffic.
     *
     * @note Reduce for more frequent updates, increase to reduce serial traffic
     * @note Very short intervals may impact system performance
     */
    static constexpr unsigned long REPORT_INTERVAL_MS = 250;
};

#endif // TESTFRAMEWORK_H