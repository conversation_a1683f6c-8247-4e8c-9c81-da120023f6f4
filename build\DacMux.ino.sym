
D:\CodePCB\Code\teensy\DacMux\build/DacMux.ino.elf:     file format elf32-littlearm

SYMBOL TABLE:
******** l    d  .text	******** .text
00002c2c l    d  .fini	******** .fini
1fff0000 l    d  .usbdescriptortable	******** .usbdescriptortable
1fff0200 l    d  .dmabuffers	******** .dmabuffers
1fff0398 l    d  .usbbuffers	******** .usbbuffers
1fff0c08 l    d  .data	******** .data
1fff0d30 l    d  .bss	******** .bss
******** l    d  .ARM.attributes	******** .ARM.attributes
******** l    d  .comment	******** .comment
******** l    d  .debug_info	******** .debug_info
******** l    d  .debug_abbrev	******** .debug_abbrev
******** l    d  .debug_loclists	******** .debug_loclists
******** l    d  .debug_aranges	******** .debug_aranges
******** l    d  .debug_rnglists	******** .debug_rnglists
******** l    d  .debug_line	******** .debug_line
******** l    d  .debug_str	******** .debug_str
******** l    d  .debug_frame	******** .debug_frame
******** l    d  .debug_line_str	******** .debug_line_str
******** l    df *ABS*	******** mk20dx128.c
00000d80 l     F .text	0000000c startup_default_early_hook
00000d8c l     F .text	******** startup_default_late_hook
******** l    df *ABS*	******** crtstuff.c
00000410 l     F .text	******** register_tm_clones
00000434 l     F .text	******** frame_dummy
1fff0d30 l     O .bss	******** object.0
00002c28 l     O .text	******** __frame_dummy_init_array_entry
******** l    df *ABS*	******** DacMux.ino.cpp
0000292c l     O .text	00000010 TEST_PINS
******** l    df *ABS*	******** Print.cpp
00000784 l     F .text	00000082 Print::printNumber(unsigned long, unsigned char, unsigned char) [clone .part.0]
******** l    df *ABS*	******** analog.c
00000834 l     F .text	000000b8 wait_for_cal
1fff0d80 l     O .bss	******** analogReadBusyADC0
1fff0d81 l     O .bss	******** analogReadBusyADC1
1fff0d82 l     O .bss	******** analog_reference_internal
1fff0d83 l     O .bss	******** analog_right_shift
1fff0d84 l     O .bss	******** calibrating
1fff0c08 l     O .data	******** analog_config_bits
1fff0c09 l     O .data	******** analog_num_average
0000293c l     O .text	00000048 pin2sc1a
******** l    df *ABS*	******** main.cpp
******** l    df *ABS*	******** memcpy-armv7m.S.o
******** l    df *ABS*	******** nonstd.c
******** l    df *ABS*	******** pins_teensy.c
00000dec l     F .text	0000004c digitalWrite.part.0
00000e4c l     F .text	******** startup_default_middle_hook
1fff0c0a l     O .data	******** analog_write_res
******** l    df *ABS*	******** usb_dev.c
1fff0d8c l     O .bss	******** ep0_rx0_buf
1fff0dcc l     O .bss	******** ep0_rx1_buf
1fff0e0c l     O .bss	******** ep0_tx_bdt_bank
1fff0e0d l     O .bss	******** ep0_tx_data_toggle
1fff0e0e l     O .bss	******** ep0_tx_len
1fff0e10 l     O .bss	******** ep0_tx_ptr
1fff0e14 l     O .bss	******** reply_buffer
1fff0e1c l     O .bss	******** rx_first
1fff0e30 l     O .bss	******** rx_last
1fff0e44 l     O .bss	******** setup
1fff0e4c l     O .bss	******** tx_first
1fff0e60 l     O .bss	******** tx_last
1fff0e74 l     O .bss	******** tx_state
1fff0000 l     O .usbdescriptortable	000000c0 table
******** l    df *ABS*	******** usb_inst.cpp
******** l    df *ABS*	******** usb_mem.c
1fff0c1c l     O .data	******** usb_buffer_available
******** l    df *ABS*	******** usb_midi.c
00001d88 l     F .text	******** sysex_byte
1fff0e88 l     O .bss	******** rx_packet
1fff0e8c l     O .bss	******** tx_noautoflush
1fff0e90 l     O .bss	******** tx_packet
******** l    df *ABS*	******** usb_serial.c
1fff1010 l     O .bss	******** rx_packet
1fff1014 l     O .bss	******** transmit_previous_timeout
1fff1015 l     O .bss	******** tx_noautoflush
1fff1018 l     O .bss	******** tx_packet
******** l    df *ABS*	******** yield.cpp
1fff102d l     O .bss	******** yield::running
******** l    df *ABS*	******** EventResponder.cpp
******** l    df *ABS*	******** serialEvent.cpp
******** l    df *ABS*	******** usb_desc.c
1fff0c24 l     O .data	00000095 config_descriptor
1fff0cbc l     O .data	00000012 device_descriptor
******** l    df *ABS*	******** libc_a-memset.o
******** l    df *ABS*	******** libc_a-init.o
******** l    df *ABS*	******** libc_a-lock.o
******** l    df *ABS*	******** libc_a-__call_atexit.o
000027c4 l     F .text	******** register_fini
******** l    df *ABS*	******** libc_a-atexit.o
******** l    df *ABS*	******** libc_a-fini.o
******** l    df *ABS*	******** libc_a-__atexit.o
******** l    df *ABS*	******** crti.o
******** l    df *ABS*	******** crtn.o
******** l    df *ABS*	******** HardwareSerial.cpp
00000d78  w    F .text	00000006 adc0_isr
000022a4 g     F .text	0000002c usb_serial_available
00002514  w    F .text	000000f0 yield
00000b40 g     F .text	0000003c analogWriteDAC1
1fff0cd4 g     O .data	00000018 usb_string_manufacturer_name_default
1fff0eb4 g     O .bss	******** usb_midi_handleProgramChange
00000d34  w    F .text	00000044 hard_fault_isr
00000d78  w    F .text	00000006 dma_ch6_isr
1fff1060 g     O .bss	******** __lock___atexit_recursive_mutex
00000818 g     F .text	0000001c Print::println()
00000d78  w    F .text	00000006 uart0_lon_isr
000013b0 g     F .text	00000084 usb_rx_memory
00000d78  w    F .text	00000006 dma_ch8_isr
1fff0e98 g     O .bss	******** usb_midi_handleAfterTouch
00000458 g     F .text	******** myControlChange(unsigned char, unsigned char, unsigned char)
000008ec g     F .text	000000c8 analog_init
00001434 g     F .text	00000088 usb_tx
00000d78  w    F .text	00000006 portcd_isr
1fff0ecc g     O .bss	******** usb_midi_handleSysExComplete
1fff0d60 g     O .bss	******** errorValues
00000d78  w    F .text	00000006 can1_rx_warn_isr
00002c30 g       .fini	******** __exidx_end
00000d78  w    F .text	00000006 dma_error_isr
1fff100c g     O .bss	******** usb_midi_msg_type
1fff103c g     O .bss	******** EventResponder::runningFromYield
00000d78  w    F .text	00000006 i2c0_isr
1fff0d2c g     O .data	******** __atexit_recursive_mutex
00000d78  w    F .text	00000006 portd_isr
00000d78  w    F .text	00000006 enet_error_isr
1fff1038 g     O .bss	******** EventResponder::firstInterrupt
00000d78  w    F .text	00000006 tpm1_isr
00001ce8  w    F .text	******** usb_serial_class::write(unsigned char const*, unsigned int)
00002984 g     O .text	00000200 digital_pin_to_info_PGM
00002c30 g       .fini	******** _etext
1fff0d30 g       .bss	******** _sbss
1fff0ee0 g     O .bss	******** usb_midi_handleVelocityChange
00000d78  w    F .text	00000006 porte_isr
0000070c g     F .text	00000078 loop
00000d78  w    F .text	00000006 portb_isr
00001dc8 g     F .text	00000038 usb_midi_flush_output
00000d78  w    F .text	00000006 spi1_isr
00000d78  w    F .text	00000006 uart3_status_isr
00000d78  w    F .text	00000006 mcm_isr
00001e00 g     F .text	0000042c usb_midi_read
1fff0ed4 g     O .bss	******** usb_midi_handleSystemReset
1fff1034 g     O .bss	******** EventResponder::lastInterrupt
00000d78  w    F .text	00000006 uart1_status_isr
1fff100a g     O .bss	******** usb_midi_msg_sysex_len
00000b8c g     F .text	000001a6 memcpy
00000d78  w    F .text	00000006 randnum_isr
1fff0eb8 g     O .bss	******** usb_midi_handleRealTimeSystem
1fff0d30 g     O .data	******** .hidden __TMC_END__
1fff0d88 g     O .bss	******** systick_millis_count
00000d34  w    F .text	00000044 bus_fault_isr
00000d78  w    F .text	00000006 watchdog_isr
00000d78  w    F .text	00000006 i2c1_isr
1fff0e79 g     O .bss	******** usb_configuration
00000d78  w    F .text	00000006 dma_ch11_isr
00000d78  w    F .text	00000006 i2c2_isr
00000f14 g     F .text	00000308 analogWrite
1fff0cd4  w    O .data	00000018 usb_string_manufacturer_name
1fff0e7c g     O .bss	0000000a usb_rx_byte_count_data
00000d78  w    F .text	00000006 pit1_isr
00000d78  w    F .text	00000006 dma_ch4_isr
00000d78  w    F .text	00000006 software_isr
1fff0d4c g     O .bss	******** currentState
00000d78  w    F .text	00000006 dma_ch7_isr
00001c34 g     F .text	0000009c usb_init
00000d78  w    F .text	00000006 lptmr_isr
00000e38 g     F .text	******** rtc_set
1fff0d30 g       .bss	******** __bss_start__
1fff0c08 g       .data	******** _sdata
1fff0eac g     O .bss	******** usb_midi_handleNoteOn
00000198 g     F .text	000001d0 ResetHandler
0000222c g     F .text	00000048 usb_serial_getchar
00000d78  w    F .text	00000006 can1_bus_off_isr
00000d78  w    F .text	00000006 ftm2_isr
1fff101c g     O .bss	******** usb_cdc_line_coding
00001238 g     F .text	00000054 digitalWrite
1fff1040 g     O .bss	******** EventResponder::lastYield
00000d78  w    F .text	00000006 uart5_status_isr
00000d78  w    F .text	00000006 lpuart0_status_isr
00002c10 g     O .text	******** usb_endpoint_config_table
00000d78  w    F .text	00000006 dma_ch9_isr
00000d78  w    F .text	00000006 pit2_isr
0000128c g     F .text	00000078 pinMode
00001d38 g     F .text	00000050 usb_free
00000d78  w    F .text	00000006 i2c3_isr
1fff0ed0 g     O .bss	******** usb_midi_handleSysExPartial
00002c30 g       .fini	******** __exidx_start
00000d78  w    F .text	00000006 pit0_isr
1fff0ee6 g     O .bss	******** usb_midi_msg_data1
1fff1030 g     O .bss	******** EventResponder::firstYield
00000d78  w    F .text	00000006 can1_error_isr
00002774 g     F .text	00000048 __libc_init_array
1fff11f8 g       .bss	******** __bss_end
00000d78  w    F .text	00000006 can0_wakeup_isr
00000d78  w    F .text	00000006 flash_cmd_isr
00001cd0  w    F .text	******** usb_serial_class::clear()
00000d78  w    F .text	00000006 uart2_status_isr
1fff1024 g     O .bss	******** usb_cdc_line_rtsdtr
000024cc g     F .text	00000048 usb_serial_flush_callback
00002c18 g     F .text	******** _init
00000d78  w    F .text	00000006 svcall_isr
00000d78  w    F .text	00000006 dma_ch15_isr
00000d78  w    F .text	00000006 uart1_error_isr
000027e4 g     F .text	00000028 __libc_fini_array
00000d78  w    F .text	00000006 usbhs_phy_isr
1fff11f8 g       .bss	******** _ebss
00000d78 g     F .text	00000006 unused_isr
00000d78  w    F .text	00000006 spi0_isr
00000d78  w    F .text	00000006 dma_ch3_isr
00000d78  w    F .text	00000006 flash_error_isr
00000d78  w    F .text	00000006 uart5_error_isr
00000d78  w    F .text	00000006 rtc_seconds_isr
00000d78  w    F .text	00000006 pdb_isr
00000d34  w    F .text	00000044 usage_fault_isr
00000d78  w    F .text	00000006 dac1_isr
******** g     O .text	00000198 _VectorsFlash
00000d78  w    F .text	00000006 dma_ch14_isr
1fff1028 g     O .bss	******** usb_cdc_line_rtsdtr_millis
0000264c g     F .text	00000010 systick_isr
00000d78  w    F .text	00000006 rtc_alarm_isr
000027c0 g     F .text	******** __retarget_lock_release_recursive
00000d78  w    F .text	00000006 dma_ch2_isr
1fff0c0c g     O .data	00000010 Serial
00000d78  w    F .text	00000006 ftm1_isr
00001cf0  w    F .text	00000006 usb_serial_class::write(unsigned char)
00000d80  w    F .text	0000000c startup_early_hook
00000d78  w    F .text	00000006 dma_ch13_isr
1fff0d6c g     O .bss	******** measuredValues
00000d78  w    F .text	00000006 uart2_error_isr
00002274 g     F .text	00000030 usb_serial_peekchar
1fff11f8 g       .bss	******** __bss_end__
1fff0d14 g     O .data	00000016 usb_string_serial_number_default
00000d34 g     F .text	00000044 fault_isr
1fff0ec8 g     O .bss	******** usb_midi_handleStop
00000d78  w    F .text	00000006 usb_charge_isr
1fff0d74 g     O .bss	******** midiCCValues
1fff1068 g     O .bss	00000190 __atexit0
00001ce0  w    F .text	******** usb_serial_class::flush()
00000d78  w    F .text	00000006 cmt_isr
00000d78  w    F .text	00000006 usbhs_isr
1fff0ea4 g     O .bss	******** usb_midi_handleControlChange
00001cd8  w    F .text	******** usb_serial_class::read()
00000d78  w    F .text	00000006 ftm3_isr
00000d78  w    F .text	00000006 tsi0_isr
00000d78  w    F .text	00000006 spi2_isr
1fff0d50 g     O .bss	00000010 dacValues
0000265c  w    F .text	******** serialEvent()
1fff0e7a g     O .bss	******** usb_reboot_timer
0000280c g     F .text	00000088 __register_exitproc
000022d0 g     F .text	00000038 usb_serial_flush_input
00002410 g     F .text	0000001a usb_serial_putchar
0000001f g       *ABS*	******** _teensy_model_identifier
000009b4 g     F .text	0000006c analogReadRes
1fff1044 g     O .bss	00000018 HardwareSerial::s_serials_with_serial_events
00000d78  w    F .text	00000006 can0_bus_off_isr
1fff0e86 g     O .bss	******** usb_rx_memory_needed
000014bc g     F .text	00000778 usb_isr
1fff0ea8 g     O .bss	******** usb_midi_handleNoteOff
00002b84  w    O .text	00000028 vtable for usb_serial_class
00000d78  w    F .text	00000006 uart3_error_isr
00000d78  w    F .text	00000006 porta_isr
1fff0398 g     O .usbbuffers	00000870 usb_buffer_memory
00000d78  w    F .text	00000006 low_voltage_isr
1fff1064 g     O .bss	******** __atexit
00000d78  w    F .text	00000006 can0_error_isr
00000e50 g     F .text	000000c4 _init_Teensyduino_internal_
00002474 g     F .text	00000058 usb_serial_flush_output
00000d78  w    F .text	00000006 dma_ch12_isr
00000d78  w    F .text	00000006 can1_wakeup_isr
1fff0200 g     O .dmabuffers	00000198 _VectorsRam
00000d78  w    F .text	00000006 pit3_isr
1fff0c20 g     O .data	******** yield_active_check_flags
00000d78  w    F .text	00000006 enet_rx_isr
000027bc g     F .text	******** __retarget_lock_acquire_recursive
00000d78  w    F .text	00000006 portc_isr
000026d0 g     F .text	000000a4 memset
00000b7c g     F .text	00000010 main
00000d34  w    F .text	00000044 memmanage_fault_isr
00002660 g     F .text	00000070 usb_init_serialnumber
00002c2c g       .text	******** __init_array_end
1fff105c g     O .bss	******** HardwareSerial::s_count_serials_with_serial_events
00000d78  w    F .text	00000006 debugmonitor_isr
1fff0ebc g     O .bss	******** usb_midi_handleSongPosition
1fff0ea0 g     O .bss	******** usb_midi_handleContinue
1fff0ee7 g     O .bss	******** usb_midi_msg_data2
6869e23a g       *ABS*	******** __rtc_localtime
00001304 g     F .text	******** micros
00000d78  w    F .text	00000006 cmp1_isr
00000d78  w    F .text	00000006 ftm0_isr
1fff0cd0 g     O .data	******** string0
00002308 g     F .text	00000108 usb_serial_write
00000d8c  w    F .text	******** startup_late_hook
00001cdc  w    F .text	******** usb_serial_class::available()
00002bac  w    O .text	******** _serialEvent_default
1fff0cec g     O .data	0000000e usb_string_midi_port1_default
1fff0ee4 g     O .bss	******** usb_midi_msg_cable
1fff0cfc  w    O .data	00000018 usb_string_product_name
00000d78  w    F .text	00000006 tpm0_isr
00002c2c g     F .fini	******** _fini
00000d78  w    F .text	00000006 i2s0_rx_isr
00000d78  w    F .text	00000006 uart4_error_isr
00000808 g     F .text	00000010 Print::print(long)
000027d8 g     F .text	0000000c atexit
0000121c g     F .text	0000001c analogWriteRes
00000d78  w    F .text	00000006 can0_message_isr
00000d78  w    F .text	00000006 can1_message_isr
00000d78  w    F .text	00000006 nmi_isr
1fff0edc g     O .bss	******** usb_midi_handleTuneRequest
00000660 g     F .text	000000ac printTestResults()
00002c24 g       .text	******** __preinit_array_end
00000d78  w    F .text	00000006 sdhc_isr
1fff0ee5 g     O .bss	******** usb_midi_msg_channel
0000046c g     F .text	00000114 setup
00002bb0 g     O .text	00000060 usb_descriptor_list
00001344 g     F .text	******** usb_rx
00000d78  w    F .text	00000006 dma_ch10_isr
1fff0ec4 g     O .bss	******** usb_midi_handleStart
00000d78  w    F .text	00000006 uart0_error_isr
00001cf8 g     F .text	******** usb_malloc
00000d78  w    F .text	00000006 i2s0_isr
2002fff8 g       .bss	******** _estack
0000242c g     F .text	00000048 usb_serial_write_buffer_free
00000d78  w    F .text	00000006 enet_timer_isr
1fff0d48 g     O .bss	******** currentChannel
1fff0d30 g       .data	******** _edata
00000d78  w    F .text	00000006 i2s0_tx_isr
00000d78  w    F .text	00000006 adc1_isr
1fff0cec  w    O .data	0000000e usb_string_midi_port1
00001ce4  w    F .text	******** usb_serial_class::availableForWrite()
00000d78  w    F .text	00000006 cmp0_isr
1fff0e9c g     O .bss	******** usb_midi_handleClock
00000d90 g     F .text	0000005c ultoa
00002604 g     F .text	00000044 EventResponder::runFromInterrupt()
00001cd4  w    F .text	******** usb_serial_class::peek()
1fff102c g     O .bss	******** usb_cdc_transmit_flush_timer
1fff0eb0 g     O .bss	******** usb_midi_handlePitchChange
00000d78  w    F .text	00000006 pit_isr
00000b04 g     F .text	0000003c analogWriteDAC0
1fff0ed8 g     O .bss	******** usb_midi_handleTimeCodeQuarterFrame
00000d78  w    F .text	00000006 dac0_isr
00002c24 g       .text	******** __init_array_start
00000d78  w    F .text	00000006 can1_tx_warn_isr
00000a20 g     F .text	000000e4 analogRead
1fff0ee8 g     O .bss	00000122 usb_midi_msg_sysex
00000d78  w    F .text	00000006 uart0_status_isr
00000d78  w    F .text	00000006 mcg_isr
1fff0d68 g     O .bss	******** lastReportTime
00000400 g     O .text	00000010 flashconfigbytes
1fff0cfc g     O .data	00000018 usb_string_product_name_default
00000d78  w    F .text	00000006 dma_ch1_isr
00001384 g     F .text	0000002c usb_tx_packet_count
00000e4c  w    F .text	******** startup_middle_hook
00000d78  w    F .text	00000006 dma_ch5_isr
00000d78  w    F .text	00000006 can0_rx_warn_isr
1fff0d7c g     O .bss	******** stateChangeTime
00000d78  w    F .text	00000006 can0_tx_warn_isr
00000d78  w    F .text	00000006 uart4_status_isr
00002c24 g       .text	******** __preinit_array_start
00000580 g     F .text	000000e0 refreshDacOutputs()
1fff0ec0 g     O .bss	******** usb_midi_handleSongSelect
00000d78  w    F .text	00000006 cmp2_isr
00002648 g     F .text	******** pendablesrvreq_isr
00000d78  w    F .text	00000006 wakeup_isr
00000d78  w    F .text	00000006 cmp3_isr
1fff0e94 g     O .bss	******** usb_midi_handleActiveSensing
00000d78  w    F .text	00000006 tpm2_isr
00000d78  w    F .text	00000006 dma_ch0_isr
1fff0d14  w    O .data	00000016 usb_string_serial_number
00000d78  w    F .text	00000006 enet_tx_isr


