#line 1 "D:\\CodePCB\\Code\\teensy\\DacMux\\.vscode\\c_cpp_properties.json"
{
    "version": 4,
    "configurations": [
        {
            "name": "Arduino",
            "compilerPath": "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1\\arm\\bin\\arm-none-eabi-g++",
            "compilerArgs": [
                "-Wall",
                "-ffunction-sections",
                "-fdata-sections",
                "-nostdlib",
                "-mno-unaligned-access"
            ],
            "intelliSenseMode": "gcc-x64",
            "includePath": [
                "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\pch",
                "C:\\Users\\<USER>\\AppData\\Local\\Arduino15\\packages\\teensy\\hardware\\avr\\1.59.0\\cores\\teensy3",
                "c:\\users\\<USER>\\appdata\\local\\arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1\\arm\\arm-none-eabi\\include\\c++\\11.3.1",
                "c:\\users\\<USER>\\appdata\\local\\arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1\\arm\\arm-none-eabi\\include\\c++\\11.3.1\\arm-none-eabi",
                "c:\\users\\<USER>\\appdata\\local\\arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1\\arm\\arm-none-eabi\\include\\c++\\11.3.1\\backward",
                "c:\\users\\<USER>\\appdata\\local\\arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1\\arm\\lib\\gcc\\arm-none-eabi\\11.3.1\\include",
                "c:\\users\\<USER>\\appdata\\local\\arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1\\arm\\lib\\gcc\\arm-none-eabi\\11.3.1\\include-fixed",
                "c:\\users\\<USER>\\appdata\\local\\arduino15\\packages\\teensy\\tools\\teensy-compile\\11.3.1\\arm\\arm-none-eabi\\include"
            ],
            "forcedInclude": [
                "D:\\CodePCB\\Code\\teensy\\DacMux\\build\\pch\\Arduino.h"
            ],
            "cStandard": "c11",
            "cppStandard": "c++17",
            "defines": [
                "__MK64FX512__",
                "TEENSYDUINO=159",
                "ARDUINO=10607",
                "ARDUINO_TEENSY35",
                "F_CPU=120000000",
                "USB_MIDI_SERIAL",
                "LAYOUT_US_ENGLISH",
                "__DBL_MIN_EXP__=(-1021)",
                "__HQ_FBIT__=15",
                "__cpp_attributes=200809L",
                "__cpp_nontype_template_parameter_auto=201606L",
                "__UINT_LEAST16_MAX__=0xffff",
                "__ARM_SIZEOF_WCHAR_T=4",
                "__ATOMIC_ACQUIRE=2",
                "__SFRACT_IBIT__=0",
                "__FLT_MIN__=1.1754943508222875e-38F",
                "__GCC_IEC_559_COMPLEX=0",
                "__cpp_aggregate_nsdmi=201304L",
                "__UFRACT_MAX__=0XFFFFP-16UR",
                "__UINT_LEAST8_TYPE__=unsigned char",
                "__DQ_FBIT__=63",
                "__INTMAX_C(c)=c ## LL",
                "__ULFRACT_FBIT__=32",
                "__CHAR_BIT__=8",
                "__USQ_IBIT__=0",
                "__UINT8_MAX__=0xff",
                "__ACCUM_FBIT__=15",
                "__WINT_MAX__=0xffffffffU",
                "__FLT32_MIN_EXP__=(-125)",
                "__cpp_static_assert=201411L",
                "__USFRACT_FBIT__=8",
                "__ORDER_LITTLE_ENDIAN__=1234",
                "__SIZE_MAX__=0xffffffffU",
                "__ARM_ARCH_ISA_ARM=1",
                "__WCHAR_MAX__=0xffffffffU",
                "__LACCUM_IBIT__=32",
                "__DBL_DENORM_MIN__=double(4.9406564584124654e-324L)",
                "__GCC_ATOMIC_CHAR_LOCK_FREE=1",
                "__GCC_IEC_559=0",
                "__FLT32X_DECIMAL_DIG__=17",
                "__FLT_EVAL_METHOD__=0",
                "__TQ_IBIT__=0",
                "__cpp_binary_literals=201304L",
                "__LLACCUM_MAX__=0X7FFFFFFFFFFFFFFFP-31LLK",
                "__FLT64_DECIMAL_DIG__=17",
                "__cpp_noexcept_function_type=201510L",
                "__GCC_ATOMIC_CHAR32_T_LOCK_FREE=1",
                "__cpp_variadic_templates=200704L",
                "__UINT_FAST64_MAX__=0xffffffffffffffffULL",
                "__SIG_ATOMIC_TYPE__=int",
                "__DBL_MIN_10_EXP__=(-307)",
                "__FINITE_MATH_ONLY__=0",
                "__ARMEL__=1",
                "__cpp_variable_templates=201304L",
                "__FLT32X_MAX_EXP__=1024",
                "__LFRACT_IBIT__=0",
                "__GNUC_PATCHLEVEL__=1",
                "__FLT32_HAS_DENORM__=1",
                "__LFRACT_MAX__=0X7FFFFFFFP-31LR",
                "__USA_FBIT__=16",
                "__UINT_FAST8_MAX__=0xffffffffU",
                "__cpp_rvalue_reference=200610L",
                "__cpp_nested_namespace_definitions=201411L",
                "__ARM_ARCH_4T__=1",
                "__INT8_C(c)=c",
                "__INT_LEAST8_WIDTH__=8",
                "__cpp_variadic_using=201611L",
                "__UINT_LEAST64_MAX__=0xffffffffffffffffULL",
                "__INT_LEAST8_MAX__=0x7f",
                "__SA_FBIT__=15",
                "__cpp_capture_star_this=201603L",
                "__SHRT_MAX__=0x7fff",
                "__LDBL_MAX__=1.7976931348623157e+308L",
                "__FRACT_MAX__=0X7FFFP-15R",
                "__cpp_if_constexpr=201606L",
                "__LDBL_IS_IEC_60559__=2",
                "__UFRACT_FBIT__=16",
                "__UFRACT_MIN__=0.0UR",
                "__UINT_LEAST8_MAX__=0xff",
                "__GCC_ATOMIC_BOOL_LOCK_FREE=1",
                "__UINTMAX_TYPE__=long long unsigned int",
                "__LLFRACT_EPSILON__=0x1P-63LLR",
                "__FLT_EVAL_METHOD_TS_18661_3__=0",
                "__CHAR_UNSIGNED__=1",
                "__UINT32_MAX__=0xffffffffUL",
                "__GXX_EXPERIMENTAL_CXX0X__=1",
                "__ULFRACT_MAX__=0XFFFFFFFFP-32ULR",
                "__TA_IBIT__=64",
                "__LDBL_MAX_EXP__=1024",
                "__WINT_MIN__=0U",
                "__FLT32X_IS_IEC_60559__=2",
                "__INT_LEAST16_WIDTH__=16",
                "__ULLFRACT_MIN__=0.0ULLR",
                "__SCHAR_MAX__=0x7f",
                "__WCHAR_MIN__=0U",
                "__INT64_C(c)=c ## LL",
                "__GCC_ATOMIC_POINTER_LOCK_FREE=1",
                "__LLACCUM_MIN__=(-0X1P31LLK-0X1P31LLK)",
                "__SIZEOF_INT__=4",
                "__FLT32X_MANT_DIG__=53",
                "__GCC_ATOMIC_CHAR16_T_LOCK_FREE=1",
                "__USACCUM_IBIT__=8",
                "__cpp_aligned_new=201606L",
                "__USER_LABEL_PREFIX__",
                "__FLT32_MAX_10_EXP__=38",
                "__STDC_HOSTED__=1",
                "__LFRACT_MIN__=(-0.5LR-0.5LR)",
                "__HA_IBIT__=8",
                "__cpp_decltype_auto=201304L",
                "__DBL_DIG__=15",
                "__FLT32_DIG__=6",
                "__FLT_EPSILON__=1.1920928955078125e-7F",
                "__APCS_32__=1",
                "__GXX_WEAK__=1",
                "__SHRT_WIDTH__=16",
                "__FLT32_IS_IEC_60559__=2",
                "__USFRACT_IBIT__=0",
                "__LDBL_MIN__=2.2250738585072014e-308L",
                "__DBL_IS_IEC_60559__=2",
                "__FRACT_MIN__=(-0.5R-0.5R)",
                "__cpp_threadsafe_static_init=200806L",
                "__DA_IBIT__=32",
                "__ARM_SIZEOF_MINIMAL_ENUM=1",
                "__FLT32X_HAS_INFINITY__=1",
                "__INT32_MAX__=0x7fffffffL",
                "__UQQ_FBIT__=8",
                "__INT_WIDTH__=32",
                "__SIZEOF_LONG__=4",
                "__UACCUM_MAX__=0XFFFFFFFFP-16UK",
                "__UINT16_C(c)=c",
                "__DECIMAL_DIG__=17",
                "__LFRACT_EPSILON__=0x1P-31LR",
                "__FLT64_EPSILON__=2.2204460492503131e-16F64",
                "__ULFRACT_MIN__=0.0ULR",
                "__INT16_MAX__=0x7fff",
                "__FLT64_MIN_EXP__=(-1021)",
                "__LDBL_HAS_QUIET_NAN__=1",
                "__ULACCUM_IBIT__=32",
                "__FLT64_MANT_DIG__=53",
                "__UACCUM_EPSILON__=0x1P-16UK",
                "__GNUC__=11",
                "__ULLACCUM_MAX__=0XFFFFFFFFFFFFFFFFP-32ULLK",
                "__GXX_RTTI=1",
                "__HQ_IBIT__=0",
                "__FLT_HAS_DENORM__=1",
                "__SIZEOF_LONG_DOUBLE__=8",
                "__SA_IBIT__=16",
                "__BIGGEST_ALIGNMENT__=8",
                "__STDC_UTF_16__=1",
                "__FLT64_MAX_10_EXP__=308",
                "__GNUC_STDC_INLINE__=1",
                "__DQ_IBIT__=0",
                "__cpp_delegating_constructors=200604L",
                "__FLT32_HAS_INFINITY__=1",
                "__DBL_MAX__=double(1.7976931348623157e+308L)",
                "__ULFRACT_IBIT__=0",
                "__cpp_raw_strings=200710L",
                "__INT_FAST32_MAX__=0x7fffffff",
                "__DBL_HAS_INFINITY__=1",
                "__cpp_deduction_guides=201703L",
                "__HAVE_SPECULATION_SAFE_VALUE=1",
                "__cpp_fold_expressions=201603L",
                "__ACCUM_IBIT__=16",
                "__THUMB_INTERWORK__=1",
                "__INTPTR_WIDTH__=32",
                "__UINT_LEAST32_MAX__=0xffffffffUL",
                "__ULLACCUM_IBIT__=32",
                "__LACCUM_MAX__=0X7FFFFFFFFFFFFFFFP-31LK",
                "__FLT32X_HAS_DENORM__=1",
                "__INT_FAST16_TYPE__=int",
                "__LDBL_HAS_DENORM__=1",
                "__cplusplus=201703L",
                "__cpp_ref_qualifiers=200710L",
                "__INT_LEAST32_MAX__=0x7fffffffL",
                "__ARM_PCS=1",
                "__ACCUM_MAX__=0X7FFFFFFFP-15K",
                "__DEPRECATED=1",
                "__cpp_rvalue_references=200610L",
                "__DBL_MAX_EXP__=1024",
                "__USACCUM_EPSILON__=0x1P-8UHK",
                "__WCHAR_WIDTH__=32",
                "__FLT32_MAX__=3.4028234663852886e+38F32",
                "__GCC_ATOMIC_LONG_LOCK_FREE=1",
                "__SFRACT_MAX__=0X7FP-7HR",
                "__FRACT_IBIT__=0",
                "__PTRDIFF_MAX__=0x7fffffff",
                "__UACCUM_MIN__=0.0UK",
                "__UACCUM_IBIT__=16",
                "__FLT32_HAS_QUIET_NAN__=1",
                "__GNUG__=11",
                "__LONG_LONG_MAX__=0x7fffffffffffffffLL",
                "__ULACCUM_MAX__=0XFFFFFFFFFFFFFFFFP-32ULK",
                "__cpp_nsdmi=200809L",
                "__SIZEOF_WINT_T__=4",
                "__LONG_LONG_WIDTH__=64",
                "__cpp_initializer_lists=200806L",
                "__FLT32_MAX_EXP__=128",
                "__ULLACCUM_MIN__=0.0ULLK",
                "__cpp_hex_float=201603L",
                "__GXX_ABI_VERSION=1016",
                "__UTA_FBIT__=64",
                "__FLT_MIN_EXP__=(-125)",
                "__UFRACT_IBIT__=0",
                "__cpp_enumerator_attributes=201411L",
                "__cpp_lambdas=200907L",
                "__INT_FAST64_TYPE__=long long int",
                "__FLT64_DENORM_MIN__=4.9406564584124654e-324F64",
                "__DBL_MIN__=double(2.2250738585072014e-308L)",
                "__SIZEOF_POINTER__=4",
                "__SIZE_TYPE__=unsigned int",
                "__DBL_HAS_QUIET_NAN__=1",
                "__FLT32X_EPSILON__=2.2204460492503131e-16F32x",
                "__LACCUM_MIN__=(-0X1P31LK-0X1P31LK)",
                "__FRACT_FBIT__=15",
                "__ULLACCUM_FBIT__=32",
                "__GXX_TYPEINFO_EQUALITY_INLINE=0",
                "__FLT64_MIN_10_EXP__=(-307)",
                "__ULLFRACT_EPSILON__=0x1P-64ULLR",
                "__USES_INITFINI__=1",
                "__REGISTER_PREFIX__",
                "__UINT16_MAX__=0xffff",
                "__ACCUM_MIN__=(-0X1P15K-0X1P15K)",
                "__SQ_IBIT__=0",
                "__FLT32_MIN__=1.1754943508222875e-38F32",
                "__UINT8_TYPE__=unsigned char",
                "__UHA_FBIT__=8",
                "__FLT_DIG__=6",
                "__NO_INLINE__=1",
                "__SFRACT_MIN__=(-0.5HR-0.5HR)",
                "__UTQ_FBIT__=128",
                "__DEC_EVAL_METHOD__=2",
                "__FLT_MANT_DIG__=24",
                "__LDBL_DECIMAL_DIG__=17",
                "__VERSION__=\"11.3.1 20220712\"",
                "__UINT64_C(c)=c ## ULL",
                "__ULLFRACT_FBIT__=64",
                "__cpp_unicode_characters=201411L",
                "__SOFTFP__=1",
                "__FRACT_EPSILON__=0x1P-15R",
                "__ULACCUM_MIN__=0.0ULK",
                "__UDA_FBIT__=32",
                "__LLACCUM_EPSILON__=0x1P-31LLK",
                "__GCC_ATOMIC_INT_LOCK_FREE=1",
                "__FLOAT_WORD_ORDER__=__ORDER_LITTLE_ENDIAN__",
                "__USFRACT_MIN__=0.0UHR",
                "__FLT32_MANT_DIG__=24",
                "__cpp_aggregate_bases=201603L",
                "__UQQ_IBIT__=0",
                "__USFRACT_MAX__=0XFFP-8UHR",
                "__SCHAR_WIDTH__=8",
                "__INT32_C(c)=c ## L",
                "__ORDER_PDP_ENDIAN__=3412",
                "__UHQ_FBIT__=16",
                "__LLACCUM_FBIT__=31",
                "__INT_FAST32_TYPE__=int",
                "__UINT_LEAST16_TYPE__=short unsigned int",
                "__DBL_HAS_DENORM__=1",
                "__cpp_rtti=199711L",
                "__UINT64_MAX__=0xffffffffffffffffULL",
                "__FLT_IS_IEC_60559__=2",
                "__UDQ_FBIT__=64",
                "__GNUC_WIDE_EXECUTION_CHARSET_NAME=\"UTF-32LE\"",
                "__INT8_TYPE__=signed char",
                "__cpp_digit_separators=201309L",
                "__ELF__=1",
                "__GCC_ASM_FLAG_OUTPUTS__=1",
                "__SACCUM_EPSILON__=0x1P-7HK",
                "__ULFRACT_EPSILON__=0x1P-32ULR",
                "__LLFRACT_FBIT__=63",
                "__FLT_RADIX__=2",
                "__INT_LEAST16_TYPE__=short int",
                "__LDBL_EPSILON__=2.2204460492503131e-16L",
                "__UINTMAX_C(c)=c ## ULL",
                "__SACCUM_MAX__=0X7FFFP-7HK",
                "__FLT32X_MIN__=2.2250738585072014e-308F32x",
                "__SIG_ATOMIC_MAX__=0x7fffffff",
                "__UACCUM_FBIT__=16",
                "__GCC_ATOMIC_WCHAR_T_LOCK_FREE=1",
                "__VFP_FP__=1",
                "__SIZEOF_PTRDIFF_T__=4",
                "__LACCUM_EPSILON__=0x1P-31LK",
                "__LDBL_DIG__=15",
                "__FLT64_IS_IEC_60559__=2",
                "__FLT32X_MIN_EXP__=(-1021)",
                "__INT_FAST16_MAX__=0x7fffffff",
                "__FLT64_DIG__=15",
                "__UINT_FAST32_MAX__=0xffffffffU",
                "__UINT_LEAST64_TYPE__=long long unsigned int",
                "__SFRACT_EPSILON__=0x1P-7HR",
                "__FLT_HAS_QUIET_NAN__=1",
                "__FLT_MAX_10_EXP__=38",
                "__LONG_MAX__=0x7fffffffL",
                "__SIZEOF_SIZE_T__=4",
                "__FLT_HAS_INFINITY__=1",
                "__GNUC_EXECUTION_CHARSET_NAME=\"UTF-8\"",
                "__cpp_unicode_literals=200710L",
                "__UINT_FAST16_TYPE__=unsigned int",
                "__ARM_32BIT_STATE=1",
                "__INT_FAST32_WIDTH__=32",
                "__CHAR16_TYPE__=short unsigned int",
                "__PRAGMA_REDEFINE_EXTNAME=1",
                "__SIZE_WIDTH__=32",
                "__INT_LEAST16_MAX__=0x7fff",
                "__INT64_MAX__=0x7fffffffffffffffLL",
                "__SACCUM_FBIT__=7",
                "__FLT32_DENORM_MIN__=1.4012984643248171e-45F32",
                "__SIG_ATOMIC_WIDTH__=32",
                "__INT_LEAST64_TYPE__=long long int",
                "__INT16_TYPE__=short int",
                "__INT_LEAST8_TYPE__=signed char",
                "__cpp_structured_bindings=201606L",
                "__SQ_FBIT__=31",
                "__ARM_ARCH_ISA_THUMB=1",
                "__INT_FAST8_MAX__=0x7fffffff",
                "__ARM_ARCH=4",
                "__INTPTR_MAX__=0x7fffffff",
                "__cpp_sized_deallocation=201309L",
                "__cpp_guaranteed_copy_elision=201606L",
                "__QQ_FBIT__=7",
                "__UTA_IBIT__=64",
                "__FLT64_HAS_QUIET_NAN__=1",
                "__FLT32_MIN_10_EXP__=(-37)",
                "__EXCEPTIONS=1",
                "__PTRDIFF_WIDTH__=32",
                "__LDBL_MANT_DIG__=53",
                "__SFRACT_FBIT__=7",
                "__cpp_range_based_for=201603L",
                "__SACCUM_MIN__=(-0X1P7HK-0X1P7HK)",
                "__FLT64_HAS_INFINITY__=1",
                "__STDCPP_DEFAULT_NEW_ALIGNMENT__=8",
                "__SIG_ATOMIC_MIN__=(-__SIG_ATOMIC_MAX__ - 1)",
                "__cpp_nontype_template_args=201411L",
                "__cpp_return_type_deduction=201304L",
                "__INTPTR_TYPE__=int",
                "__UINT16_TYPE__=short unsigned int",
                "__WCHAR_TYPE__=unsigned int",
                "__SIZEOF_FLOAT__=4",
                "__TQ_FBIT__=127",
                "__USQ_FBIT__=32",
                "__UINTPTR_MAX__=0xffffffffU",
                "__INT_FAST64_WIDTH__=64",
                "__cpp_decltype=200707L",
                "__FLT32_DECIMAL_DIG__=9",
                "__INT_FAST64_MAX__=0x7fffffffffffffffLL",
                "__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1",
                "__FLT_NORM_MAX__=3.4028234663852886e+38F",
                "__UINT_FAST64_TYPE__=long long unsigned int",
                "__cpp_inline_variables=201606L",
                "__INT_MAX__=0x7fffffff",
                "__LACCUM_FBIT__=31",
                "__USACCUM_MIN__=0.0UHK",
                "__UHA_IBIT__=8",
                "__INT64_TYPE__=long long int",
                "__FLT_MAX_EXP__=128",
                "__UTQ_IBIT__=0",
                "__DBL_MANT_DIG__=53",
                "__cpp_inheriting_constructors=201511L",
                "__INT_LEAST64_MAX__=0x7fffffffffffffffLL",
                "__WINT_TYPE__=unsigned int",
                "__UINT_LEAST32_TYPE__=long unsigned int",
                "__SIZEOF_SHORT__=2",
                "__ULLFRACT_IBIT__=0",
                "__FLT32_NORM_MAX__=3.4028234663852886e+38F32",
                "__LDBL_MIN_EXP__=(-1021)",
                "__arm__=1",
                "__FLT64_MAX__=1.7976931348623157e+308F64",
                "__UDA_IBIT__=32",
                "__WINT_WIDTH__=32",
                "__cpp_template_auto=201606L",
                "__INT_LEAST64_WIDTH__=64",
                "__FLT32X_MAX_10_EXP__=308",
                "__LFRACT_FBIT__=31",
                "__WCHAR_UNSIGNED__=1",
                "__LDBL_MAX_10_EXP__=308",
                "__ATOMIC_RELAXED=0",
                "__DBL_EPSILON__=double(2.2204460492503131e-16L)",
                "__UINT8_C(c)=c",
                "__FLT64_MAX_EXP__=1024",
                "__INT_LEAST32_TYPE__=long int",
                "__SIZEOF_WCHAR_T__=4",
                "__LLFRACT_MAX__=0X7FFFFFFFFFFFFFFFP-63LLR",
                "__FLT64_NORM_MAX__=1.7976931348623157e+308F64",
                "__INTMAX_MAX__=0x7fffffffffffffffLL",
                "__INT_FAST8_TYPE__=int",
                "__cpp_namespace_attributes=201411L",
                "__ULLACCUM_EPSILON__=0x1P-32ULLK",
                "__USACCUM_MAX__=0XFFFFP-8UHK",
                "__LDBL_HAS_INFINITY__=1",
                "__UHQ_IBIT__=0",
                "__ARM_FEATURE_COPROC=1",
                "__LLACCUM_IBIT__=32",
                "__FLT64_HAS_DENORM__=1",
                "__FLT32_EPSILON__=1.1920928955078125e-7F32",
                "__DBL_DECIMAL_DIG__=17",
                "__STDC_UTF_32__=1",
                "__INT_FAST8_WIDTH__=32",
                "__FLT32X_MAX__=1.7976931348623157e+308F32x",
                "__TA_FBIT__=63",
                "__DBL_NORM_MAX__=double(1.7976931348623157e+308L)",
                "__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__",
                "__UDQ_IBIT__=0",
                "__INTMAX_WIDTH__=64",
                "__ORDER_BIG_ENDIAN__=4321",
                "__cpp_runtime_arrays=198712L",
                "__UINT64_TYPE__=long long unsigned int",
                "__ACCUM_EPSILON__=0x1P-15K",
                "__UINT32_C(c)=c ## UL",
                "__cpp_alias_templates=200704L",
                "__FLT_DENORM_MIN__=1.4012984643248171e-45F",
                "__LLFRACT_IBIT__=0",
                "__INT8_MAX__=0x7f",
                "__LONG_WIDTH__=32",
                "__UINT_FAST32_TYPE__=unsigned int",
                "__FLT32X_NORM_MAX__=1.7976931348623157e+308F32x",
                "__CHAR32_TYPE__=long unsigned int",
                "__FLT_MAX__=3.4028234663852886e+38F",
                "__cpp_constexpr=201603L",
                "__USACCUM_FBIT__=8",
                "__INT32_TYPE__=long int",
                "__SIZEOF_DOUBLE__=8",
                "__cpp_exceptions=199711L",
                "__FLT_MIN_10_EXP__=(-37)",
                "__UFRACT_EPSILON__=0x1P-16UR",
                "__FLT64_MIN__=2.2250738585072014e-308F64",
                "__INT_LEAST32_WIDTH__=32",
                "__INTMAX_TYPE__=long long int",
                "__FLT32X_HAS_QUIET_NAN__=1",
                "__ATOMIC_CONSUME=1",
                "__GNUC_MINOR__=3",
                "__INT_FAST16_WIDTH__=32",
                "__UINTMAX_MAX__=0xffffffffffffffffULL",
                "__FLT32X_DENORM_MIN__=4.9406564584124654e-324F32x",
                "__HA_FBIT__=7",
                "__cpp_template_template_args=201611L",
                "__DBL_MAX_10_EXP__=308",
                "__LDBL_DENORM_MIN__=4.9406564584124654e-324L",
                "__INT16_C(c)=c",
                "__STDC__=1",
                "__FLT32X_DIG__=15",
                "__PTRDIFF_TYPE__=int",
                "__LLFRACT_MIN__=(-0.5LLR-0.5LLR)",
                "__ATOMIC_SEQ_CST=5",
                "__DA_FBIT__=31",
                "__UINT32_TYPE__=long unsigned int",
                "__FLT32X_MIN_10_EXP__=(-307)",
                "__UINTPTR_TYPE__=unsigned int",
                "__USA_IBIT__=16",
                "__ARM_EABI__=1",
                "__LDBL_MIN_10_EXP__=(-307)",
                "__cpp_generic_lambdas=201304L",
                "__SIZEOF_LONG_LONG__=8",
                "__ULACCUM_EPSILON__=0x1P-32ULK",
                "__cpp_user_defined_literals=200809L",
                "__SACCUM_IBIT__=8",
                "__GCC_ATOMIC_LLONG_LOCK_FREE=1",
                "__FLT_DECIMAL_DIG__=9",
                "__UINT_FAST16_MAX__=0xffffffffU",
                "__LDBL_NORM_MAX__=1.7976931348623157e+308L",
                "__GCC_ATOMIC_SHORT_LOCK_FREE=1",
                "__ULLFRACT_MAX__=0XFFFFFFFFFFFFFFFFP-64ULLR",
                "__UINT_FAST8_TYPE__=unsigned int",
                "__USFRACT_EPSILON__=0x1P-8UHR",
                "__ULACCUM_FBIT__=32",
                "__QQ_IBIT__=0",
                "__cpp_init_captures=201304L",
                "__ATOMIC_ACQ_REL=4",
                "__ATOMIC_RELEASE=3",
                "USBCON"
            ]
        }
    ]
}