# DacMux System Architecture

## Overview

The DacMux system is a modular, object-oriented design that provides 8-channel analog output through a single DAC and multiplexer. The architecture emphasizes clean separation of concerns, real-time performance, and flexible signal routing.

## System Block Diagram

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│ MIDI Input  │───▶│ MidiHandler  │───▶│ DacControl  │
└─────────────┘    └──────────────┘    └─────────────┘
                                              │
┌─────────────┐    ┌──────────────┐          │
│ LFO Engine  │───▶│ DacControl   │◀─────────┘
└─────────────┘    └──────────────┘
                          │
                          ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ StateMachine │───▶│ MuxControl  │
                   └──────────────┘    └─────────────┘
                          │                   │
                          ▼                   ▼
                   ┌──────────────┐    ┌─────────────┐
                   │TestFramework │    │ CD4051 MUX  │
                   └──────────────┘    └─────────────┘
                          │                   │
                          ▼                   ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ Serial Out   │    │ 8 Channels  │
                   └──────────────┘    └─────────────┘
```

## Core Components

### 1. DacControl
**Purpose**: Central signal routing and DAC management
**Responsibilities**:
- Signal source routing (MIDI CC, LFO, Static sources)
- Double-buffered DAC value management
- LFO engine with 8 independent sine wave generators
- MIDI CC to DAC value scaling
- Source function dispatch table

**Key Features**:
- Flexible routing: any source to any output channel
- 12-bit resolution (0-4095) matching Teensy 3.6 DAC
- Non-blocking LFO updates with configurable frequencies
- Singleton pattern for static function access

### 2. MuxControl
**Purpose**: CD4051 multiplexer hardware interface
**Responsibilities**:
- 3-bit channel selection (S0, S1, S2)
- Inhibit pin control for sample-and-hold timing
- Pin configuration and initialization

**Key Features**:
- Direct hardware abstraction for CD4051
- Static pin definitions for compile-time optimization
- Clean interface for state machine control

### 3. StateMachine
**Purpose**: Precision timing controller for DAC/MUX operations
**Responsibilities**:
- Channel sequencing (0-7 continuous cycle)
- DAC settling time management (150μs)
- Sample-and-hold timing control (66μs)
- Non-blocking state transitions

**Key Features**:
- Microsecond-precision timing using micros()
- Three-state operation: IDLE → SETTLING → SAMPLING
- Optimized for ~4.6kHz per-channel update rate
- Integration with sample-and-hold circuits

### 4. MidiHandler
**Purpose**: USB MIDI input processing and CC mapping
**Responsibilities**:
- USB MIDI message reception
- Control Change filtering (CC 20-27)
- CC value storage and retrieval
- Static callback function management

**Key Features**:
- Real-time MIDI processing with minimal latency
- Automatic CC to channel mapping (CC20→Ch0, CC21→Ch1, etc.)
- Singleton pattern for USB callback integration
- Non-blocking operation

### 5. TestFramework
**Purpose**: Real-time accuracy monitoring and validation
**Responsibilities**:
- ADC measurement of first 4 output channels
- Error calculation (target vs. measured)
- Periodic serial reporting
- Test enable/disable control

**Key Features**:
- 12-bit ADC resolution matching DAC
- Configurable reporting interval (250ms default)
- Non-blocking operation
- Hardware test pin mapping

## Data Flow Architecture

### Signal Path
1. **Input Sources**:
   - MIDI CC values (0-127) → scaled to DAC range
   - LFO sine waves → calculated in real-time
   - Static sources → fixed patterns or values

2. **Routing Layer**:
   - Function pointer table for source dispatch
   - Per-channel source assignment
   - Dynamic routing changes at runtime

3. **Output Processing**:
   - Double-buffered DAC values
   - State machine timing control
   - Hardware multiplexer switching

4. **Feedback Loop**:
   - ADC measurement of outputs
   - Error calculation and reporting
   - Performance monitoring

### Timing Architecture

The system uses a carefully orchestrated timing sequence:

```
Channel N Processing:
├── IDLE State (immediate)
│   ├── Select channel N on multiplexer
│   ├── Set DAC to target voltage
│   └── Transition to SETTLING
├── SETTLING State (150μs)
│   ├── Wait for DAC output to stabilize
│   ├── Account for external circuit settling
│   └── Transition to SAMPLING
└── SAMPLING State (66μs)
    ├── Enable multiplexer (INH = LOW)
    ├── Allow S&H capacitor to charge
    ├── Optional: Read ADC for testing
    ├── Disable multiplexer (INH = HIGH)
    └── Advance to next channel
```

## Memory Architecture

### Static Memory Usage
- **Code**: ~8KB (estimated)
- **Global Variables**: ~200 bytes
- **Stack**: Standard Arduino stack usage

### Dynamic Memory Usage
- **DAC Buffers**: 32 bytes (2 × 8 channels × 2 bytes)
- **LFO State**: 96 bytes (8 × 12 bytes per LFO)
- **MIDI CC Storage**: 8 bytes
- **Test Data**: 20 bytes

### Memory Optimization
- Compile-time constants for pin definitions
- Static function tables to avoid virtual function overhead
- Minimal dynamic allocation
- Efficient data structures

## Performance Characteristics

### Timing Performance
- **Total Update Rate**: ~37kHz (all 8 channels)
- **Per-Channel Rate**: ~4.6kHz
- **MIDI Latency**: <1ms typical
- **LFO Update Rate**: Main loop frequency dependent

### Resource Utilization
- **CPU Usage**: ~15-20% at full operation
- **Memory Usage**: <1KB RAM
- **Flash Usage**: ~8KB program memory

### Accuracy Specifications
- **DAC Resolution**: 12-bit (0.024% of full scale)
- **Typical Error**: ±3 LSB with proper S&H circuits
- **Temperature Stability**: Dependent on external components

## Extensibility Points

### Adding New Signal Sources
1. Add new enum value to `DacDataSource`
2. Implement static source function
3. Add function to `sourceFuncs` table
4. Update routing assignments as needed

### Modifying Timing Parameters
- Adjust `SETTLE_DURATION_US` for different DAC/circuit requirements
- Modify `SAMPLE_DURATION_US` for different S&H circuits
- Change `REPORT_INTERVAL_MS` for different test frequencies

### Hardware Adaptations
- Pin definitions in header files for easy modification
- Modular design supports different multiplexer ICs
- Test framework can be disabled for production use

## Design Patterns Used

### Singleton Pattern
- **DacControl**: For static function table access
- **MidiHandler**: For USB MIDI callback integration

### Strategy Pattern
- **Signal Sources**: Function pointer table for source selection
- **Routing System**: Dynamic source-to-output assignment

### State Machine Pattern
- **StateMachine**: Explicit state management for timing control
- **Clear state transitions**: IDLE → SETTLING → SAMPLING

### Observer Pattern
- **TestFramework**: Monitors DAC outputs and reports status
- **Serial Output**: Provides feedback on system performance

## Thread Safety and Concurrency

### Interrupt Safety
- MIDI callbacks execute in interrupt context
- Minimal processing in interrupt handlers
- Data structures designed for atomic updates

### Main Loop Coordination
- All components designed for single-threaded operation
- Non-blocking functions throughout
- Careful timing coordination between components

## Error Handling

### Hardware Errors
- Graceful degradation when test connections missing
- Safe defaults for all configuration parameters
- Bounds checking on all array accesses

### Software Errors
- Null pointer checks in static functions
- Range validation for channel numbers
- Safe fallback values for invalid inputs

## Future Enhancement Opportunities

### Performance Improvements
- DMA-based DAC updates for higher throughput
- Interrupt-driven state machine for more precise timing
- Optimized LFO calculations using lookup tables

### Feature Additions
- Additional waveform types (triangle, sawtooth, square)
- MIDI note-to-voltage conversion
- Envelope generators
- More sophisticated routing matrix

### Hardware Expansions
- Support for multiple DACs
- Higher resolution DACs (16-bit)
- Additional multiplexer channels
- External clock synchronization
