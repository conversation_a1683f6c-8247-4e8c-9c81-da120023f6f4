// TestFramework.cpp
// Implements TestFramework class: encapsulates measures, error calculation, test pin IO, and reporting

#include "TestFramework.h"
#include "DacControl.h"
#include "StateMachine.h"

TestFramework::TestFramework(DacControl& dac)
    : dacControl(dac), measuredValues{0}, errorValues{0}, lastReportTime(0) {}

void TestFramework::init() {
    if (ENABLE_TESTING) {
        Serial.begin(115200);
        while (!Serial && millis() < 2000) { ; }
        analogReadResolution(12);
        Serial.println("--- DAC MUX Test Initialized ---");
        Serial.println("Reading first 4 channels (A0-A3).");
        Serial.println("Format: Target | Measured | Error");
    }
}

void TestFramework::update() {
    if (ENABLE_TESTING) {
        runTesting();
    }
}

void TestFramework::runTesting() {
    unsigned long currentTime = millis();
    if (currentTime - lastReportTime >= REPORT_INTERVAL_MS) {
        printTestResults();
        lastReportTime = currentTime;
    }
}

void TestFramework::printTestResults() {
    for (int i = 0; i < 4; i++) {
        Serial.print(dacControl.getCurrentDacValues()[i]);
        Serial.print(" | ");
        Serial.print(measuredValues[i]);
        Serial.print(" | ");
        Serial.println(errorValues[i]);
    }
}