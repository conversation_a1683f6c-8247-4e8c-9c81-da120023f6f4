# DacMux - Teensy 3.6 DAC Multiplexer Control System

A modular and routable DAC (Digital-to-Analog Converter) multiplexer control system for Teensy 3.6, designed for precision analog output generation with MIDI control capabilities.

## Overview

DacMux is a sophisticated control system that combines:
- **8-channel DAC output** through a CD4051 multiplexer
- **MIDI control interface** for real-time parameter control
- **Built-in LFO engine** for modulation sources
- **Modular routing system** for flexible signal assignment
- **Real-time testing framework** for accuracy verification
- **Non-blocking state machine** for precise timing control

The system is designed for applications requiring multiple synchronized analog outputs, such as modular synthesizers, control voltage generators, or precision instrumentation.

## Hardware Requirements

### Primary Components
- **Teensy 3.6** microcontroller
- **CD4051** 8-channel analog multiplexer/demultiplexer
- **Sample & Hold circuitry** for each output channel
- **Power supply** (3.3V/5V depending on configuration)

### Pin Configuration

#### Teensy 3.6 Pin Assignments
```
DAC Output:     A21 (Teensy built-in 12-bit DAC)
MUX Control:    
  - S0: Pin 6
  - S1: Pin 7  
  - S2: Pin 8
  - INH: Pin 5 (Inhibit/Enable)

Test Inputs:    A1, A2, A3, A4 (first 4 channels monitoring)
```

#### CD4051 Multiplexer Connections
```
CD4051 Pin    | Connection
------------- | -----------
S0 (Pin 11)   | Teensy Pin 6
S1 (Pin 10)   | Teensy Pin 7
S2 (Pin 9)    | Teensy Pin 8
INH (Pin 6)   | Teensy Pin 5
X (Pin 3)     | Teensy A21 (DAC output)
Y0-Y7         | Output channels 0-7
VCC (Pin 16)  | 3.3V or 5V
GND (Pin 8)   | Ground
```

## Software Dependencies

### Required Libraries
- **Arduino IDE** with Teensy support
- **Teensyduino** add-on package
- **USB MIDI** library (included with Teensyduino)

### Development Environment Setup
1. Install Arduino IDE (1.8.x or 2.x)
2. Install Teensyduino from PJRC website
3. Select "Teensy 3.6" as board type
4. Set USB Type to "MIDI" for MIDI functionality

## Installation & Setup

### 1. Hardware Assembly
1. Connect CD4051 multiplexer according to pin configuration above
2. Wire sample & hold circuits to Y0-Y7 outputs
3. Connect power supply (ensure proper voltage levels)
4. Connect test monitoring inputs to A1-A4 (optional)

### 2. Software Installation
```bash
# Clone or download the project
git clone <repository-url>
cd DacMux

# Open DacMux.ino in Arduino IDE
# Select Tools > Board > Teensy 3.6
# Select Tools > USB Type > MIDI
# Upload to Teensy
```

### 3. Initial Configuration
The system initializes with default routing:
- Channels 0,4: MIDI CC control
- Channels 1,5: LFO modulation  
- Channels 2,6: Static source 1
- Channels 3,7: Static source 2

## Usage Examples

### Basic Operation
```cpp
// The system runs automatically once uploaded
// MIDI CC 20-27 control channels 0-7 respectively
// LFO frequencies are preset but can be modified in code
```

### MIDI Control
- **MIDI Channel**: Any (system responds to all channels)
- **Control Change Messages**: CC 20-27 map to DAC channels 0-7
- **Value Range**: 0-127 (automatically scaled to 0-4095 for 12-bit DAC)

### Runtime Source Assignment
```cpp
// Assign MIDI CC to channel 0
dacControl.assignSourceToOutput(0, DacControl::DAC_SRC_MIDI_CC);

// Assign LFO to channel 1  
dacControl.assignSourceToOutput(1, DacControl::DAC_SRC_LFO);

// Available sources:
// - DAC_SRC_MIDI_CC: MIDI Control Change values
// - DAC_SRC_LFO: Built-in LFO engine
// - DAC_SRC_1: Static source 1
// - DAC_SRC_2: Static source 2
// - DAC_SRC_NONE: No output (0V)
```

### LFO Configuration
```cpp
// LFO frequencies are set in DacControl constructor
// Default frequencies: 0.2Hz, 0.25Hz, 0.33Hz, 0.4Hz, 0.5Hz, 0.6Hz, 0.75Hz, 1.0Hz
// Modify in src/DacControl.cpp constructor for custom frequencies
```

## System Architecture

### Core Components

1. **DacControl**: Manages DAC output, value scaling, routing, and LFO engine
2. **MuxControl**: Handles CD4051 multiplexer channel selection and control
3. **MidiHandler**: Processes MIDI input and maps CC values to channels
4. **StateMachine**: Coordinates timing for DAC settling and sampling
5. **TestFramework**: Provides real-time accuracy monitoring and reporting

### Data Flow
```
MIDI Input → MidiHandler → DacControl → StateMachine → MuxControl → DAC Output
                ↓              ↓            ↓
            LFO Engine → Value Routing → TestFramework
```

## Performance Characteristics

- **DAC Resolution**: 12-bit (0-4095 range)
- **Update Rate**: ~8kHz per channel (all 8 channels)
- **Settling Time**: 50μs (configurable)
- **Sample Time**: 10μs (configurable)
- **MIDI Latency**: <1ms typical
- **LFO Range**: 0.1Hz - 10Hz (configurable)

## Testing & Monitoring

### Built-in Test Framework
The system includes real-time monitoring of the first 4 channels:
- Compares target vs. measured values
- Reports errors via Serial output
- Updates every 250ms
- Requires test connections to A1-A4

### Serial Output Format
```
Target | Measured | Error
2048   | 2045     | -3
1024   | 1027     | 3
```

### Enabling/Disabling Tests
Modify `ENABLE_TESTING` in `src/TestFramework.h`:
```cpp
static constexpr bool ENABLE_TESTING = true;  // Enable
static constexpr bool ENABLE_TESTING = false; // Disable
```

## Troubleshooting

### Common Issues

**No DAC Output**
- Check power supply connections
- Verify Teensy 3.6 is selected as board type
- Ensure A21 is connected to CD4051 X input

**MIDI Not Responding**
- Set USB Type to "MIDI" in Arduino IDE
- Check MIDI device connection
- Verify CC numbers 20-27 are being sent

**Inaccurate Output Values**
- Check sample & hold circuit design
- Verify settling/sample timing constants
- Monitor test framework output for systematic errors

**Compilation Errors**
- Ensure Teensyduino is properly installed
- Check all source files are in src/ directory
- Verify Arduino IDE board selection

### Timing Adjustments
Modify timing constants in `src/StateMachine.cpp`:
```cpp
static constexpr unsigned long SETTLE_DURATION_US = 50;  // DAC settling time
static constexpr unsigned long SAMPLE_DURATION_US = 10;  // Sample & hold time
```

### Debug Output
Enable serial debugging by connecting to Serial Monitor at 115200 baud when testing is enabled.

## Configuration Options

### Compile-Time Options
- `ENABLE_TESTING`: Enable/disable test framework
- `REPORT_INTERVAL_MS`: Test reporting frequency
- `SETTLE_DURATION_US`: DAC settling time
- `SAMPLE_DURATION_US`: Sample & hold duration

### Runtime Configuration
- Source-to-output routing assignments
- MIDI CC mappings (modify in MidiHandler)
- LFO frequencies (modify in DacControl constructor)

## Known Limitations

- Maximum 8 output channels (CD4051 limitation)
- Single DAC shared across all channels (time-multiplexed)
- LFO frequencies are compile-time configured
- Test framework limited to first 4 channels
- MIDI input is USB-only (no DIN MIDI)

## Contributing

When modifying the code:
1. Maintain the modular class structure
2. Update inline documentation for public methods
3. Test changes with the built-in test framework
4. Follow existing naming conventions
5. Update this README for significant changes

## License

[Add your license information here]

## Version History

- **v1.0**: Initial modular implementation with MIDI, LFO, and testing support
