// Teensy 3.6 DAC Multiplexer Control - Modular & Routable Version

#include "DacMux.h"

DacControl dacControl;
MuxControl muxControl;
StateMachine stateMachine(muxControl, dacControl);
MidiHandler midiHandler;
TestFramework testFramework(dacControl);

void setup() {
    muxControl.init();
    dacControl.init();
    midiHandler.init();
    stateMachine.init();
    testFramework.init();
    dacControl.lfoEngineInit();

    // Example: assign sources dynamically (can be changed at runtime)
    dacControl.assignSourceToOutput(0, DacControl::DAC_SRC_MIDI_CC);
    dacControl.assignSourceToOutput(1, DacControl::DAC_SRC_LFO);
    dacControl.assignSourceToOutput(2, DacControl::DAC_SRC_1);
    dacControl.assignSourceToOutput(3, DacControl::DAC_SRC_2);
    dacControl.assignSourceToOutput(4, DacControl::DAC_SRC_MIDI_CC);
    dacControl.assignSourceToOutput(5, DacControl::DAC_SRC_LFO);
    dacControl.assignSourceToOutput(6, DacControl::DAC_SRC_1);
    dacControl.assignSourceToOutput(7, DacControl::DAC_SRC_2);
}

void loop() {
    // Update LFO engine (if used)
    dacControl.lfoEngineUpdate();

    // Update DAC outputs from assigned sources
    for (uint8_t i = 0; i < 8; i++) {
        DacControl::DacDataSource src = dacControl.getOutputSource(i);
        uint16_t value = dacControl.getSourceFunc(src)(i);
        dacControl.setDacValue(i, value);
    }

    // Run state machine for DAC/MUX output
    stateMachine.update();

    // Run testing framework (if enabled)
    testFramework.update();

    // Poll MIDI input (non-blocking)
    midiHandler.update();
}
