/**
 * @file DacMux.ino
 * @brief Teensy 3.6 DAC Multiplexer Control - Modular & Routable Version
 *
 * This is the main Arduino sketch file for the DacMux system. It coordinates
 * all subsystems including DAC control, multiplexer management, MIDI handling,
 * state machine timing, and testing framework.
 *
 * The system provides 8 channels of analog output through a single DAC and
 * CD4051 multiplexer, with flexible routing between different signal sources
 * including MIDI CC values, LFO generators, and static sources.
 *
 * <AUTHOR> Name]
 * @version 1.0
 * @date 2024
 */

#include "DacMux.h"

// Global system component instances
DacControl dacControl;                                    ///< Manages DAC output and signal routing
MuxControl muxControl;                                    ///< Controls CD4051 multiplexer channel selection
StateMachine stateMachine(muxControl, dacControl);       ///< Coordinates timing and channel sequencing
MidiHandler midiHandler;                                  ///< Processes MIDI input and CC mapping
TestFramework testFramework(dacControl);                 ///< Provides real-time accuracy monitoring

/**
 * @brief Arduino setup function - initializes all system components
 *
 * Initializes all subsystems in the correct order and sets up default
 * signal routing assignments. The initialization order is important to
 * ensure proper dependency resolution between components.
 */
void setup() {
    // Initialize hardware interfaces first
    muxControl.init();          // Configure multiplexer control pins
    dacControl.init();          // Set up DAC resolution and buffers
    midiHandler.init();         // Configure MIDI input handlers
    stateMachine.init();        // Initialize timing state machine
    testFramework.init();       // Set up testing and serial output

   // --- DEMO: Base-Frequency-Synced LFOs ---
   // Set a new base frequency for all LFOs and print resulting frequencies
   dacControl.setLfoBaseFrequency(2.0f); // Example: set base to 2 Hz

   Serial.begin(115200);
   delay(100); // Wait for Serial to initialize
   Serial.println("LFO frequencies after setLfoBaseFrequency(2.0):");
   for (int i = 0; i < 8; ++i) {
       Serial.print("LFO[");
       Serial.print(i);
       Serial.print("] freq = ");
       Serial.println(dacControl.lfo[i].freq, 4);
   }
   // --- END DEMO ---
    dacControl.lfoEngineInit(); // Initialize LFO oscillators

    // Configure default signal routing assignments
    // These can be changed at runtime using assignSourceToOutput()
    dacControl.assignSourceToOutput(0, DacControl::DAC_SRC_MIDI_CC);     // Ch0: MIDI CC20
    dacControl.assignSourceToOutput(1, DacControl::DAC_SRC_LFO);         // Ch1: LFO
    dacControl.assignSourceToOutput(2, DacControl::DAC_SRC_SAMPLE_HOLD); // Ch2: Sample-and-hold
    dacControl.assignSourceToOutput(3, DacControl::DAC_SRC_2);           // Ch3: Static source 2
    dacControl.assignSourceToOutput(4, DacControl::DAC_SRC_MIDI_CC);     // Ch4: MIDI CC24
    dacControl.assignSourceToOutput(5, DacControl::DAC_SRC_LFO);         // Ch5: LFO
    dacControl.assignSourceToOutput(6, DacControl::DAC_SRC_SAMPLE_HOLD); // Ch6: Sample-and-hold
    dacControl.assignSourceToOutput(7, DacControl::DAC_SRC_2);           // Ch7: Static source 2

    // Configure sample-and-hold functionality
    dacControl.setSampleFrequency(0.5f);        // 0.5Hz sampling rate
    dacControl.enableFrequencyTrigger(true);    // Enable automatic sampling
    dacControl.enableExternalTrigger(true);     // Enable hardware trigger on pin 10
}

/**
 * @brief Arduino main loop - runs continuously to update all system components
 *
 * The main loop coordinates all system updates in a non-blocking manner:
 * 1. Updates LFO engine for modulation sources
 * 2. Retrieves values from assigned sources and updates DAC buffers
 * 3. Runs the state machine for precise timing control
 * 4. Updates the testing framework for accuracy monitoring
 * 5. Polls MIDI input for real-time control
 *
 * All operations are designed to be non-blocking to maintain consistent
 * timing and responsiveness.
 */
void loop() {
    // Update LFO engine to generate modulation signals
    dacControl.lfoEngineUpdate();

    // Update DAC output values from their assigned sources
    // This implements the flexible routing system where each channel
    // can be assigned to different signal sources
    for (uint8_t i = 0; i < 8; i++) {
        DacControl::DacDataSource src = dacControl.getOutputSource(i);
        uint16_t value = dacControl.getSourceFunc(src)(i);
        dacControl.setDacValue(i, value);
    }

    // Run the state machine to coordinate DAC output timing
    // This handles channel sequencing, settling delays, and sample timing
    stateMachine.update();

    // Update testing framework for real-time accuracy monitoring
    // Only active when ENABLE_TESTING is true
    testFramework.update();

    // Poll MIDI input for control change messages
    // Non-blocking operation that updates internal CC values
    midiHandler.update();

    // Example: Manual sample-and-hold triggering based on conditions
    // Uncomment the following lines to add manual triggering logic:
    /*
    static unsigned long lastManualTrigger = 0;
    if (millis() - lastManualTrigger > 3000) {  // Trigger every 3 seconds
        dacControl.triggerSample();
        lastManualTrigger = millis();
    }
    */
}
