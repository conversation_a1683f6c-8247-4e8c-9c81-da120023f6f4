/**
 * @file DacMux.h
 * @brief Main header file for the DacMux system - includes all subsystem headers
 *
 * This is the primary include file for the DacMux project. It provides a single
 * point of inclusion for all system components and serves as the main interface
 * for the modular DAC multiplexer control system.
 *
 * The DacMux system provides:
 * - 8-channel analog output through single DAC and CD4051 multiplexer
 * - Flexible signal routing between multiple sources and outputs
 * - MIDI Control Change input for real-time parameter control
 * - Built-in LFO engine for modulation sources
 * - Precision timing control with sample-and-hold integration
 * - Real-time accuracy monitoring and testing framework
 *
 * System Architecture:
 * The system is built using a modular design with clear separation of concerns:
 * - DacControl: Manages DAC output, signal routing, and LFO generation
 * - MuxControl: Controls CD4051 multiplexer channel selection
 * - MidiHandler: Processes USB MIDI input and Control Change mapping
 * - StateMachine: Coordinates precise timing for DAC/MUX operations
 * - TestFramework: Provides real-time accuracy monitoring
 *
 * Hardware Platform:
 * - Teensy 3.6 microcontroller (12-bit DAC, USB MIDI support)
 * - CD4051 8-channel analog multiplexer
 * - Sample-and-hold circuits for each output channel
 * - Optional test connections for accuracy monitoring
 *
 * <AUTHOR> Name]
 * @version 1.0
 * @date 2024
 * @copyright [Your Copyright]
 */

#ifndef DACMUX_H
#define DACMUX_H

// Include all subsystem headers in dependency order
// Forward declarations in individual headers prevent circular dependencies

#include "MuxControl.h"      ///< CD4051 multiplexer control interface
#include "DacControl.h"      ///< DAC output and signal routing system
#include "MidiHandler.h"     ///< USB MIDI input processing
#include "StateMachine.h"    ///< Precision timing controller
#include "TestFramework.h"   ///< Real-time accuracy monitoring

/**
 * @defgroup GlobalConfig Global Configuration Constants
 * @brief System-wide configuration parameters and constants
 * @{
 */

// Add any global configuration macros or typedefs here
// Examples:
// #define DACMUX_VERSION_MAJOR 1
// #define DACMUX_VERSION_MINOR 0
// #define DACMUX_MAX_CHANNELS 8
// typedef uint16_t dac_value_t;

/** @} */ // end of GlobalConfig group

#endif // DACMUX_H