#ifndef DACCONTROL_H
#define DACCONTROL_H

#include <Arduino.h>

/**
 * @class DacControl
 * @brief Encapsulates all DAC output logic, value scaling/translation, routing, and LFO engine state.
 *
 * Usage:
 *   - Create a single instance of DacControl at the appropriate scope.
 *   - Use public methods to initialize, set values, assign sources, and update LFOs.
 *   - All state and buffers are encapsulated; no global variables are exposed.
 */
class DacControl {
public:
    // Unified DAC data source enum for per-output routing
    enum DacDataSource {
        DAC_SRC_MIDI_CC,
        DAC_SRC_LFO,
        DAC_SRC_1,
        DAC_SRC_2,
        DAC_SRC_NONE
    };

    typedef uint16_t (*DacSourceFunc)(uint8_t channel);

    DacControl();

    // Initialization
    void init();

    // Buffer management
    void swapBuffers();

    // LFO engine
    void lfoEngineInit();
    void lfoEngineUpdate();

    // DAC value API
    void setDacValue(uint8_t channel, uint16_t value);
    uint16_t scaleMidiToDac(uint8_t value);

    // Assignment API
    void assignSourceToOutput(uint8_t output, DacDataSource src);

    // Routing accessors (for main loop usage)
    DacDataSource getOutputSource(uint8_t channel) const;
    DacSourceFunc getSourceFunc(DacDataSource src) const;
    uint16_t* getCurrentDacValues();
    uint16_t* getNextDacValues();

private:
    // DAC subsystem state (was DacState)
    DacDataSource outputSource[8];
    DacSourceFunc sourceFuncs[5];
    uint16_t* currentDacValues;
    uint16_t* nextDacValues;

    // Double-buffered storage
    uint16_t dacBufferA[8];
    uint16_t dacBufferB[8];

    // LFO engine state (was LfoState lfo[8])
    struct LfoState {
        float phase;
        float freq;
        float value;
    };
    LfoState lfo[8];

    // Singleton pointer for static access (needed for static function pointer table)
    static DacControl* instance;

    // Source getter implementations
    static uint16_t getMidiCCValue(uint8_t channel);
    static uint16_t getLfoValue(uint8_t channel);
    static uint16_t getDacSrc1Value(uint8_t channel);
    static uint16_t getDacSrc2Value(uint8_t channel);
    static uint16_t getNoneValue(uint8_t channel);
};

#endif // DACCONTROL_H