#ifndef DACCONTROL_H
#define DACCONTROL_H

#include <Arduino.h>

/**
 * @class DacControl
 * @brief Comprehensive DAC output control system with flexible signal routing
 *
 * The DacControl class manages all aspects of DAC output including:
 * - 12-bit DAC value generation and scaling
 * - Flexible signal source routing system
 * - Built-in LFO (Low Frequency Oscillator) engine
 * - Double-buffered output for smooth transitions
 * - MIDI CC to DAC value conversion
 *
 * The system supports multiple signal sources that can be dynamically assigned
 * to any of the 8 output channels:
 * - MIDI Control Change values (CC 20-27)
 * - LFO-generated waveforms with configurable frequencies
 * - Static sources for fixed voltage outputs
 *
 * @note This class uses a singleton pattern for static function pointer access
 * @note All DAC values are 12-bit (0-4095 range) to match Teensy 3.6 DAC resolution
 *
 * Usage Example:
 * @code
 * DacControl dac;
 * dac.init();
 * dac.lfoEngineInit();
 * dac.assignSourceToOutput(0, DacControl::DAC_SRC_MIDI_CC);
 * dac.assignSourceToOutput(1, DacControl::DAC_SRC_LFO);
 * @endcode
 */
class DacControl {
public:
    /**
     * @enum DacDataSource
     * @brief Enumeration of available signal sources for DAC output routing
     *
     * Each DAC output channel can be assigned to one of these sources.
     * The routing can be changed dynamically at runtime.
     */
    enum DacDataSource {
        DAC_SRC_MIDI_CC,    ///< MIDI Control Change values (CC 20-27 → channels 0-7)
        DAC_SRC_LFO,        ///< Low Frequency Oscillator output (sine wave)
        DAC_SRC_1,          ///< Static source 1 (configurable fixed value)
        DAC_SRC_2,          ///< Static source 2 (configurable fixed value)
        DAC_SRC_SAMPLE_HOLD,///< Sample-and-hold of LFO values with frequency/external triggering
        DAC_SRC_NONE        ///< No output (0V, used for disabled channels)
    };

    /**
     * @typedef DacSourceFunc
     * @brief Function pointer type for source value retrieval functions
     *
     * Each signal source has an associated function that returns the current
     * 12-bit DAC value for a given channel.
     *
     * @param channel Channel number (0-7)
     * @return 12-bit DAC value (0-4095)
     */
    typedef uint16_t (*DacSourceFunc)(uint8_t channel);

    /**
     * @brief Constructor - initializes default routing and LFO parameters
     *
     * Sets up default signal source assignments and initializes the LFO
     * engine with predefined frequencies for each channel.
     */
    DacControl();

    /**
     * @brief Initialize DAC subsystem and configure hardware
     *
     * Configures the Teensy DAC for 12-bit resolution, initializes double
     * buffers, and sets up the singleton pointer for static access.
     * Must be called before using any other DAC functions.
     */
    void init();

    /**
     * @brief Swap double buffers for smooth output transitions
     *
     * Exchanges current and next DAC value buffers to enable smooth
     * transitions between output values without glitches.
     */
    void swapBuffers();

    /**
     * @brief Initialize the LFO (Low Frequency Oscillator) engine
     *
     * Sets up initial phase and frequency values for all 8 LFO channels.
     * Each channel has a different frequency to create varied modulation.
     */
    void lfoEngineInit();
    /**
     * @brief Set the base frequency for all LFO channels and synchronize their frequencies.
     *
     * Each LFO channel's frequency is set to baseHz * lfoRatio[n], where lfoRatio[n] is a per-channel ratio.
     * This enables musical or polyrhythmic relationships between LFOs. Ratios default to the original hardcoded
     * frequencies (see implementation), but may be made configurable.
     *
     * @param baseHz The master/base frequency in Hz.
     */
    void setLfoBaseFrequency(float baseHz);

    // Sample-and-Hold Configuration Methods

    /**
     * @brief Set the sample frequency for frequency-based sample-and-hold triggering
     *
     * Configures the automatic sampling rate for the sample-and-hold functionality.
     * When enabled, the system will automatically sample LFO values at this frequency.
     *
     * @param frequency Sample frequency in Hz (range: 0.1Hz to 20Hz)
     * @note Frequency is clamped to valid range if outside bounds
     */
    void setSampleFrequency(float frequency);

    /**
     * @brief Enable or disable frequency-based sample-and-hold triggering
     *
     * @param enable true to enable frequency-based triggering, false to disable
     * @note When disabled, only external triggering (if enabled) will cause sampling
     */
    void enableFrequencyTrigger(bool enable);

    /**
     * @brief Enable or disable external hardware trigger for sample-and-hold
     *
     * @param enable true to enable external triggering, false to disable
     * @note External trigger uses rising edge detection on SAMPLE_TRIG_PIN
     */
    void enableExternalTrigger(bool enable);

    /**
     * @brief Manually trigger a sample-and-hold operation
     *
     * Forces an immediate sampling of all LFO values for the sample-and-hold source.
     * This can be called programmatically regardless of trigger mode settings.
     */
    void triggerSample();

    /**
     * @brief Get the current sample frequency setting
     *
     * @return Current sample frequency in Hz
     */
    float getSampleFrequency() const;

    /**
     * @brief Update LFO engine - generates new waveform values
     *
     * Calculates new sine wave values for all LFO channels based on their
     * individual frequencies and current phase positions. Should be called
     * regularly from the main loop for smooth modulation.
     */
    void lfoEngineUpdate();

    /**
     * @brief Set DAC output value for a specific channel
     *
     * Updates the next buffer with a new 12-bit DAC value. The value will
     * be output when the state machine processes this channel.
     *
     * @param channel Channel number (0-7)
     * @param value 12-bit DAC value (0-4095)
     */
    void setDacValue(uint8_t channel, uint16_t value);

    /**
     * @brief Convert MIDI CC value to 12-bit DAC value
     *
     * Scales a MIDI Control Change value (0-127) to the full 12-bit DAC
     * range (0-4095) using linear interpolation.
     *
     * @param value MIDI CC value (0-127)
     * @return Scaled 12-bit DAC value (0-4095)
     */
    uint16_t scaleMidiToDac(uint8_t value);

    /**
     * @brief Assign a signal source to a specific output channel
     *
     * Configures the routing system to connect a signal source to an output
     * channel. This can be changed dynamically at runtime to create flexible
     * signal routing configurations.
     *
     * @param output Output channel number (0-7)
     * @param src Signal source to assign
     */
    void assignSourceToOutput(uint8_t output, DacDataSource src);

    /**
     * @brief Get the currently assigned source for an output channel
     *
     * @param channel Channel number (0-7)
     * @return Currently assigned signal source
     */
    DacDataSource getOutputSource(uint8_t channel) const;

    /**
     * @brief Get the function pointer for a specific signal source
     *
     * Returns the function that retrieves values from the specified source.
     * Used by the main loop to get current values for output.
     *
     * @param src Signal source type
     * @return Function pointer to source value retrieval function
     */
    DacSourceFunc getSourceFunc(DacDataSource src) const;

    /**
     * @brief Get pointer to current DAC value buffer
     *
     * Returns the buffer currently being used for DAC output. Used by
     * the state machine to read values for hardware output.
     *
     * @return Pointer to current 8-element DAC value array
     */
    uint16_t* getCurrentDacValues();

    /**
     * @brief Get pointer to next DAC value buffer
     *
     * Returns the buffer being prepared for the next output cycle.
     * Used internally for double-buffered operation.
     *
     * @return Pointer to next 8-element DAC value array
     */
    uint16_t* getNextDacValues();

private:
    /**
     * @brief Signal source routing configuration
     *
     * Array mapping each output channel (0-7) to its assigned signal source.
     * This enables dynamic routing where any channel can use any source.
     */
    DacDataSource outputSource[8];

    /**
     * @brief Function pointer table for signal source retrieval
     *
     * Array of function pointers corresponding to each DacDataSource enum.
     * Enables efficient dispatch to the appropriate source function.
     */
    DacSourceFunc sourceFuncs[6];

    /**
     * @brief Pointer to current output buffer
     *
     * Points to the buffer currently being used by the state machine
     * for DAC output. Alternates between dacBufferA and dacBufferB.
     */
    uint16_t* currentDacValues;

    /**
     * @brief Pointer to next output buffer
     *
     * Points to the buffer being prepared for the next output cycle.
     * Enables smooth transitions without output glitches.
     */
    uint16_t* nextDacValues;

    /**
     * @brief Double-buffered DAC value storage - Buffer A
     *
     * One of two buffers used for double-buffered DAC output.
     * Contains 12-bit values (0-4095) for all 8 channels.
     */
    uint16_t dacBufferA[8];

    /**
     * @brief Double-buffered DAC value storage - Buffer B
     *
     * Second buffer for double-buffered operation. Alternates with
     * dacBufferA to provide smooth output transitions.
     */
    uint16_t dacBufferB[8];

    /**
     * @struct LfoState
     * @brief State information for a single LFO channel
     *
     * Contains all parameters needed to generate a continuous sine wave
     * with configurable frequency and maintained phase continuity.
     */
    struct LfoState {
        float phase;    ///< Current phase position (0.0 to 2π)
        float freq;     ///< Frequency in Hz (determines oscillation rate)
        float value;    ///< Current output value (cached for efficiency)
    };

    /**
     * @brief LFO state array for all 8 channels
     *
     * Each channel has independent LFO parameters allowing for
     * complex modulation patterns and polyrhythmic effects.
     */
    LfoState lfo[8];

   /**
    * @brief Per-channel LFO frequency ratios for base-frequency synchronization.
    *
    * When setLfoBaseFrequency() is called, each LFO's frequency is updated as:
    *   lfo[n].freq = baseHz * lfoRatio[n]
    * Default ratios correspond to the original LFO frequencies (see constructor).
    */
   float lfoRatio[8];

   // Sample-and-Hold System Constants and State

   /**
    * @brief Hardware trigger pin for external sample-and-hold triggering
    *
    * This pin is configured as an input with interrupt on rising edge.
    * When triggered, it sets the trigSample flag to initiate sampling.
    */
   static constexpr int SAMPLE_TRIG_PIN = 10;

   /**
    * @brief Minimum allowed sample frequency in Hz
    */
   static constexpr float MIN_SAMPLE_FREQ = 0.1f;

   /**
    * @brief Maximum allowed sample frequency in Hz
    */
   static constexpr float MAX_SAMPLE_FREQ = 20.0f;

   /**
    * @brief Sample-and-hold configuration and state
    */
   struct SampleHoldState {
       uint16_t heldValues[8];        ///< Currently held values for each channel
       float sampleFrequency;         ///< Frequency for automatic sampling (Hz)
       unsigned long lastSampleTime;  ///< Timestamp of last sample (microseconds)
       bool frequencyTriggerEnabled;  ///< Enable frequency-based triggering
       bool externalTriggerEnabled;   ///< Enable external hardware triggering
       volatile bool trigSample;      ///< Flag set by interrupt or manual trigger
   } sampleHold;

    /**
     * @brief Singleton instance pointer
     *
     * Required for static function access in the source function table.
     * Enables the function pointer system while maintaining encapsulation.
     */
    static DacControl* instance;

    // Static source value retrieval functions
    // These functions provide the interface between the routing system
    // and the actual signal sources. They use the singleton pointer
    // to access instance data.

    /**
     * @brief Retrieve MIDI CC value for specified channel
     * @param channel Channel number (0-7, maps to CC 20-27)
     * @return 12-bit DAC value scaled from MIDI CC input
     */
    static uint16_t getMidiCCValue(uint8_t channel);

    /**
     * @brief Retrieve LFO value for specified channel
     * @param channel Channel number (0-7)
     * @return 12-bit DAC value from LFO sine wave generator
     */
    static uint16_t getLfoValue(uint8_t channel);

    /**
     * @brief Retrieve static source 1 value
     * @param channel Channel number (unused, same value for all channels)
     * @return 12-bit DAC value from static source 1
     */
    static uint16_t getDacSrc1Value(uint8_t channel);

    /**
     * @brief Retrieve static source 2 value
     * @param channel Channel number (unused, same value for all channels)
     * @return 12-bit DAC value from static source 2
     */
    static uint16_t getDacSrc2Value(uint8_t channel);

    /**
     * @brief Retrieve sample-and-hold value for specified channel
     * @param channel Channel number (0-7)
     * @return 12-bit DAC value from sample-and-hold buffer
     */
    static uint16_t getSampleHoldValue(uint8_t channel);

    /**
     * @brief Retrieve null/disabled value
     * @param channel Channel number (unused)
     * @return 0 (represents 0V output for disabled channels)
     */
    static uint16_t getNoneValue(uint8_t channel);

    /**
     * @brief Interrupt service routine for external sample trigger
     *
     * This static function is called when a rising edge is detected on
     * SAMPLE_TRIG_PIN. It sets the trigSample flag to initiate sampling.
     */
    static void sampleTriggerISR();
};

#endif // DACCONTROL_H