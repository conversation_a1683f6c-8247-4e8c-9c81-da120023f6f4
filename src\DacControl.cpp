// DacControl.cpp
// Implements DacControl class: encapsulates DAC routing, double-buffering, and LFO engine

#include "DacControl.h"
#include "MidiHandler.h"
#include <math.h>

/**
 * DacControl class implementation
 */

DacControl* DacControl::instance = nullptr;

DacControl::DacControl() {
    instance = this; // Set singleton pointer

    // Initialize routing and function pointers
    outputSource[0] = DAC_SRC_MIDI_CC;
    outputSource[1] = DAC_SRC_MIDI_CC;
    outputSource[2] = DAC_SRC_LFO;
    outputSource[3] = DAC_SRC_LFO;
    outputSource[4] = DAC_SRC_1;
    outputSource[5] = DAC_SRC_2;
    outputSource[6] = DAC_SRC_MIDI_CC;
    outputSource[7] = DAC_SRC_LFO;

    sourceFuncs[0] = getMidiCCValue;
    sourceFuncs[1] = getLfoValue;
    sourceFuncs[2] = getDacSrc1Value;
    sourceFuncs[3] = getDacSrc2Value;
    sourceFuncs[4] = getNoneValue;

    currentDacValues = dacBufferA;
    nextDacValues = dacBufferB;

    // LFO initial state
    lfo[0] = {0, 0.2f, 0}; lfo[1] = {0, 0.25f, 0}; lfo[2] = {0, 0.33f, 0}; lfo[3] = {0, 0.4f, 0};
    lfo[4] = {0, 0.5f, 0}; lfo[5] = {0, 0.6f, 0}; lfo[6] = {0, 0.75f, 0}; lfo[7] = {0, 1.0f, 0};
}

void DacControl::init() {
    instance = this; // Ensure singleton pointer is set
    analogWriteResolution(12);
    for (int i = 0; i < 8; i++) {
        dacBufferA[i] = 0;
        dacBufferB[i] = 0;
        lfo[i].phase = 0;
        lfo[i].value = 0;
        // freq remains unchanged (initialized in constructor)
    }
}

void DacControl::swapBuffers() {
    uint16_t* tmp = currentDacValues;
    currentDacValues = nextDacValues;
    nextDacValues = tmp;
}

void DacControl::setDacValue(uint8_t channel, uint16_t value) {
    if (channel < 8) {
        nextDacValues[channel] = value;
    }
}

uint16_t DacControl::scaleMidiToDac(uint8_t value) {
    return (uint16_t)(((uint32_t)value * 4095) / 127);
}

void DacControl::lfoEngineInit() {
    for (int i = 0; i < 8; i++) {
        lfo[i].phase = 0;
        lfo[i].value = 0;
        // freq remains unchanged
    }
}

void DacControl::lfoEngineUpdate() {
    static unsigned long lastMicros = 0;
    unsigned long now = micros();
    float dt = (now - lastMicros) / 1e6f;
    lastMicros = now;

    for (int i = 0; i < 8; i++) {
        lfo[i].phase += lfo[i].freq * dt;
        if (lfo[i].phase > 1.0f) lfo[i].phase -= 1.0f;
        // Triangle LFO: range 0-4095, linear ramp up/down, no DC offset
        // Formula: triangle = 1.0 - fabsf(2.0 * phase - 1.0), phase in [0,1)
        float triangle = 1.0f - fabsf(2.0f * lfo[i].phase - 1.0f);
        lfo[i].value = triangle * 4095.0f;
    }
}

// Routing API
void DacControl::assignSourceToOutput(uint8_t output, DacDataSource src) {
    if (output < 8 && src < 5) {
        outputSource[output] = src;
    }
}

DacControl::DacDataSource DacControl::getOutputSource(uint8_t channel) const {
    return (channel < 8) ? outputSource[channel] : DAC_SRC_NONE;
}

DacControl::DacSourceFunc DacControl::getSourceFunc(DacDataSource src) const {
    return (src < 5) ? sourceFuncs[src] : getNoneValue;
}

uint16_t* DacControl::getCurrentDacValues() {
    return currentDacValues;
}
uint16_t* DacControl::getNextDacValues() {
    return nextDacValues;
}

// Source getter implementations
uint16_t DacControl::getMidiCCValue(uint8_t channel) {
    extern MidiCCState midiCC;
    if (channel < 8) return ((uint16_t)midiCC.cc[channel] * 32);
    return 0;
}
uint16_t DacControl::getLfoValue(uint8_t channel) {
    if (instance && channel < 8) return (uint16_t)instance->lfo[channel].value;
    return 0;
}
uint16_t DacControl::getDacSrc1Value(uint8_t channel) {
    // Placeholder: returns a fixed value or pattern
    return 1024 + (channel * 128);
}
uint16_t DacControl::getDacSrc2Value(uint8_t channel) {
    // Placeholder: returns a fixed value or pattern
    return 2048 - (channel * 128);
}
uint16_t DacControl::getNoneValue(uint8_t) {
    return 0;
}