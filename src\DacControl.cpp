/**
 * @file DacControl.cpp
 * @brief Implementation of DacControl class - DAC routing, double-buffering, and LFO engine
 *
 * This file implements the core DAC control functionality including:
 * - Signal source routing and management
 * - Double-buffered DAC output for smooth transitions
 * - LFO (Low Frequency Oscillator) engine with sine wave generation
 * - MIDI CC to DAC value scaling
 * - Static source functions for flexible signal routing
 *
 * The implementation uses a singleton pattern to enable static function
 * access while maintaining object-oriented encapsulation.
 */

#include "DacControl.h"
#include "MidiHandler.h"
#include <math.h>

// Static singleton pointer for function table access
DacControl* DacControl::instance = nullptr;

DacControl::DacControl() {
    instance = this; // Set singleton pointer

    // Initialize routing and function pointers
    outputSource[0] = DAC_SRC_MIDI_CC;
    outputSource[1] = DAC_SRC_MIDI_CC;
    outputSource[2] = DAC_SRC_LFO;
    outputSource[3] = DAC_SRC_LFO;
    outputSource[4] = DAC_SRC_1;
    outputSource[5] = DAC_SRC_2;
    outputSource[6] = DAC_SRC_MIDI_CC;
    outputSource[7] = DAC_SRC_LFO;

    sourceFuncs[0] = getMidiCCValue;
    sourceFuncs[1] = getLfoValue;
    sourceFuncs[2] = getDacSrc1Value;
    sourceFuncs[3] = getDacSrc2Value;
    sourceFuncs[4] = getNoneValue;

    currentDacValues = dacBufferA;
    nextDacValues = dacBufferB;

    // LFO initial state
    lfo[0] = {0, 0.2f, 0}; lfo[1] = {0, 0.25f, 0}; lfo[2] = {0, 0.33f, 0}; lfo[3] = {0, 0.4f, 0};
    lfo[4] = {0, 0.5f, 0}; lfo[5] = {0, 0.6f, 0}; lfo[6] = {0, 0.75f, 0}; lfo[7] = {0, 1.0f, 0};

    // LFO frequency ratios for base-frequency sync (default: matches above frequencies at base=1.0Hz)
    lfoRatio[0] = 0.2f;
    lfoRatio[1] = 0.25f;
    lfoRatio[2] = 0.33f;
    lfoRatio[3] = 0.4f;
    lfoRatio[4] = 0.5f;
    lfoRatio[5] = 0.6f;
    lfoRatio[6] = 0.75f;
    lfoRatio[7] = 1.0f;
}
/**
 * @brief Set the base frequency for all LFO channels and synchronize their frequencies.
 *
 * Each LFO channel's frequency is set to baseHz * lfoRatio[n], where lfoRatio[n] is a per-channel ratio.
 * This enables musical or polyrhythmic relationships between LFOs.
 *
 * @param baseHz The master/base frequency in Hz.
 */
void DacControl::setLfoBaseFrequency(float baseHz) {
    for (int n = 0; n < 8; ++n) {
        this->lfo[n].freq = baseHz * this->lfoRatio[n];
    }
}

void DacControl::init() {
    instance = this; // Ensure singleton pointer is set
    analogWriteResolution(12);
    for (int i = 0; i < 8; i++) {
        dacBufferA[i] = 0;
        dacBufferB[i] = 0;
        lfo[i].phase = 0;
        lfo[i].value = 0;
        // freq remains unchanged (initialized in constructor)
    }
}

void DacControl::swapBuffers() {
    uint16_t* tmp = currentDacValues;
    currentDacValues = nextDacValues;
    nextDacValues = tmp;
}

void DacControl::setDacValue(uint8_t channel, uint16_t value) {
    if (channel < 8) {
        nextDacValues[channel] = value;
    }
}

uint16_t DacControl::scaleMidiToDac(uint8_t value) {
    return (uint16_t)(((uint32_t)value * 4095) / 127);
}

void DacControl::lfoEngineInit() {
    for (int i = 0; i < 8; i++) {
        lfo[i].phase = 0;
        lfo[i].value = 0;
        // freq remains unchanged
    }
}

void DacControl::lfoEngineUpdate() {
    static unsigned long lastMicros = 0;
    unsigned long now = micros();
    float dt = (now - lastMicros) / 1e6f;
    lastMicros = now;

    for (int i = 0; i < 8; i++) {
        lfo[i].phase += lfo[i].freq * dt;
        if (lfo[i].phase > 1.0f) lfo[i].phase -= 1.0f;
        // Triangle LFO: range 0-4095, linear ramp up/down, no DC offset
        // Formula: triangle = 1.0 - fabsf(2.0 * phase - 1.0), phase in [0,1)
        float triangle = 1.0f - fabsf(2.0f * lfo[i].phase - 1.0f);
        lfo[i].value = triangle * 4095.0f;
    }
}

// Routing API
void DacControl::assignSourceToOutput(uint8_t output, DacDataSource src) {
    if (output < 8 && src < 5) {
        outputSource[output] = src;
    }
}

DacControl::DacDataSource DacControl::getOutputSource(uint8_t channel) const {
    return (channel < 8) ? outputSource[channel] : DAC_SRC_NONE;
}

DacControl::DacSourceFunc DacControl::getSourceFunc(DacDataSource src) const {
    return (src < 5) ? sourceFuncs[src] : getNoneValue;
}

uint16_t* DacControl::getCurrentDacValues() {
    return currentDacValues;
}
uint16_t* DacControl::getNextDacValues() {
    return nextDacValues;
}

/**
 * @brief Static source getter implementations
 *
 * These functions provide the interface between the routing system and
 * actual signal sources. They use various methods to access source data
 * while maintaining the static function signature required by the routing table.
 */

/**
 * @brief Get MIDI Control Change value for specified channel
 *
 * Retrieves the current MIDI CC value and scales it from MIDI range (0-127)
 * to a portion of the DAC range. Uses external MidiHandler instance access.
 *
 * @param channel DAC channel number (0-7, maps to CC 20-27)
 * @return Scaled DAC value from MIDI CC input
 * @note Currently uses simplified scaling - could be enhanced with full range scaling
 */
uint16_t DacControl::getMidiCCValue(uint8_t channel) {
    // Access global MidiHandler instance (declared in main sketch)
    extern MidiHandler midiHandler;
    if (channel < 8) {
        uint8_t ccValue = midiHandler.getCC(channel);
        // Scale MIDI CC (0-127) to DAC range (0-4095)
        // Using simplified scaling for now: CC * 32 ≈ 0-4064
        return ((uint16_t)ccValue * 32);
    }
    return 0;
}
/**
 * @brief Get LFO-generated value for specified channel
 *
 * Returns the current sine wave value from the LFO engine for the specified
 * channel. Each channel has its own LFO with independent frequency and phase.
 *
 * @param channel LFO channel number (0-7)
 * @return Current LFO value as 12-bit DAC value (0-4095)
 * @note Returns 0 if instance is null or channel is out of range
 */
uint16_t DacControl::getLfoValue(uint8_t channel) {
    if (instance && channel < 8) return (uint16_t)instance->lfo[channel].value;
    return 0;
}

/**
 * @brief Get static source 1 value
 *
 * Returns a fixed voltage pattern for static source 1. This implementation
 * provides a stepped voltage pattern that increases with channel number,
 * useful for testing and calibration purposes.
 *
 * @param channel Channel number (0-7, affects output value)
 * @return Static DAC value with channel-dependent offset
 * @note Pattern: 1024 + (channel * 128), range 1024-1920
 */
uint16_t DacControl::getDacSrc1Value(uint8_t channel) {
    // Static voltage pattern: base voltage + channel offset
    return 1024 + (channel * 128);
}

/**
 * @brief Get static source 2 value
 *
 * Returns a fixed voltage pattern for static source 2. This implementation
 * provides a stepped voltage pattern that decreases with channel number,
 * creating a complementary pattern to static source 1.
 *
 * @param channel Channel number (0-7, affects output value)
 * @return Static DAC value with channel-dependent offset
 * @note Pattern: 2048 - (channel * 128), range 2048-1152
 */
uint16_t DacControl::getDacSrc2Value(uint8_t channel) {
    // Static voltage pattern: base voltage - channel offset
    return 2048 - (channel * 128);
}

/**
 * @brief Get null/disabled source value
 *
 * Returns zero for disabled channels, representing 0V output.
 * Used when a channel is assigned to DAC_SRC_NONE.
 *
 * @param channel Channel number (unused)
 * @return Always returns 0 (0V output)
 */
uint16_t DacControl::getNoneValue(uint8_t) {
    return 0;
}
/**
 * @brief Set the base frequency for all LFO channels and synchronize their frequencies.
 *
 * Each LFO channel's frequency is set to baseHz * lfoRatio[n], where lfoRatio[n] is a per-channel ratio.
 * This enables musical or polyrhythmic relationships between LFOs.
 *
 * @param baseHz The master/base frequency in Hz.
 */
void DacControl::setLfoBaseFrequency(float baseHz) {
    for (int n = 0; n < 8; ++n) {
        lfo[n].freq = baseHz * lfoRatio[n];
    }
}