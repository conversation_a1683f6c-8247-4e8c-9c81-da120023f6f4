// StateMachine.cpp
// Implements StateMachine class: encapsulates DAC/MUX output logic, timing, and channel sequencing

#include "StateMachine.h"
#include "MuxControl.h"
#include "DacControl.h"
#include "TestFramework.h"

StateMachine::StateMachine(MuxControl& mux, DacControl& dac)
    : muxControl(mux), dacControl(dac), currentState(IDLE), currentChannel(0), stateChangeTime(0) {}

void StateMachine::init() {
    currentState = IDLE;
    currentChannel = 0;
    stateChangeTime = 0;
}

void StateMachine::update() {
    refreshDacOutputs();
}

void StateMachine::refreshDacOutputs() {
    unsigned long currentTime = micros();

    switch (currentState)
    {
        case IDLE:
            // 1. Select channel and set the DAC voltage
            muxControl.selectChannel(currentChannel);
            // Output the value from the current DAC buffer only
            analogWrite(A21, dacControl.getCurrentDacValues()[currentChannel]);
            // Optionally, update the next buffer for the next frame elsewhere
            // 2. Transition to SETTLING state
            stateChangeTime = currentTime;
            currentState = SETTLING;
            break;

        case SETTLING:
            // 3. Wait for DAC output to settle
            if (currentTime - stateChangeTime >= SETTLE_DURATION_US)
            {
                // 4. DAC is stable, now enable the MUX to start sampling
                digitalWrite(MuxControl::inhPin(), LOW);

                // 5. Transition to SAMPLING state
                stateChangeTime = currentTime;
                currentState = SAMPLING;
            }
            break;

        case SAMPLING:
            // 6. Wait for the S&H capacitor to charge
            if (currentTime - stateChangeTime >= SAMPLE_DURATION_US)
            {
                // 7. Hold the voltage by disabling the MUX
                digitalWrite(MuxControl::inhPin(), HIGH);

                // 8. Synchronized Read (handled by TestFramework if enabled)
                // Advance to next channel
                currentChannel = (currentChannel + 1) % 8;
                currentState = IDLE;
            }
            break;
    }
}