#ifndef MUXCONTROL_H
#define MUXCONTROL_H

#include <Arduino.h>

/**
 * @class MuxControl
 * @brief Encapsulates CD4051 multiplexer pin control, channel selection, and related helpers.
 *
 * Usage:
 *   - Create a single instance of MuxControl at the appropriate scope.
 *   - Use public methods to initialize and select channels.
 *   - All pin assignments and logic are encapsulated.
 */
class MuxControl {
public:
    MuxControl();

    // Initialization
    void init();

    // Channel selection
    void selectChannel(uint8_t channel);

private:
    // Pin definitions for the CD4051 Multiplexer
    static constexpr int MUX_INH_PIN = 5; // Inhibit pin
    static constexpr int MUX_S0_PIN = 6;
    static constexpr int MUX_S1_PIN = 7;
    static constexpr int MUX_S2_PIN = 8;
public:
    static int inhPin() { return MUX_INH_PIN; }
};

#endif // MUXCONTROL_H