#ifndef MUXCONTROL_H
#define MUXCONTROL_H

#include <Arduino.h>

/**
 * @class MuxControl
 * @brief CD4051 8-channel analog multiplexer control interface
 *
 * The MuxControl class provides a clean interface for controlling a CD4051
 * 8-channel analog multiplexer/demultiplexer. This IC allows a single analog
 * signal (from the Teensy DAC) to be routed to one of 8 output channels.
 *
 * The CD4051 uses 3 select lines (S0, S1, S2) to choose which of the 8 channels
 * (Y0-Y7) is connected to the common input/output (X). An inhibit pin (INH)
 * can disable all connections when high.
 *
 * Pin Configuration:
 * - S0, S1, S2: Binary channel select (000 = Y0, 001 = Y1, ..., 111 = Y7)
 * - INH: Inhibit/Enable control (LOW = enabled, HIGH = disabled)
 * - X: Common analog input/output (connected to Teensy DAC output)
 * - Y0-Y7: Individual channel outputs
 *
 * The multiplexer is used in conjunction with sample-and-hold circuits on each
 * output to maintain voltage levels when switching between channels.
 *
 * @note The CD4051 can operate with supply voltages from 3V to 20V
 * @note Channel switching time is typically <1μs
 * @note On-resistance is typically 80-120Ω depending on supply voltage
 *
 * Usage Example:
 * @code
 * MuxControl mux;
 * mux.init();
 * mux.selectChannel(3);  // Connect X to Y3
 * digitalWrite(mux.inhPin(), LOW);  // Enable output
 * @endcode
 */
class MuxControl {
public:
    /**
     * @brief Constructor - no initialization required
     *
     * Pin definitions are compile-time constants, so no runtime
     * initialization is needed in the constructor.
     */
    MuxControl();

    /**
     * @brief Initialize multiplexer control pins
     *
     * Configures all control pins as outputs and sets the inhibit pin
     * to HIGH (disabled) as a safe default state. Must be called before
     * using any other multiplexer functions.
     *
     * Pin Configuration:
     * - MUX_S0_PIN (6): Output, channel select bit 0
     * - MUX_S1_PIN (7): Output, channel select bit 1
     * - MUX_S2_PIN (8): Output, channel select bit 2
     * - MUX_INH_PIN (5): Output, inhibit control (starts HIGH/disabled)
     */
    void init();

    /**
     * @brief Select active multiplexer channel
     *
     * Sets the S0, S1, S2 pins to select which of the 8 channels (Y0-Y7)
     * is connected to the common X pin. The channel selection is immediate
     * but the analog signal may take time to settle.
     *
     * Channel Mapping:
     * - 0 (000): Y0
     * - 1 (001): Y1
     * - 2 (010): Y2
     * - 3 (011): Y3
     * - 4 (100): Y4
     * - 5 (101): Y5
     * - 6 (110): Y6
     * - 7 (111): Y7
     *
     * @param channel Channel number (0-7)
     * @note Values >7 will be masked to 3 bits (channel & 0x07)
     * @note This function only sets the channel select pins, not the inhibit pin
     */
    void selectChannel(uint8_t channel);

private:
    /**
     * @brief CD4051 Multiplexer Pin Definitions
     *
     * These pins control the CD4051 multiplexer IC:
     * - Select pins (S0-S2) determine which channel is active
     * - Inhibit pin (INH) enables/disables all connections
     */
    static constexpr int MUX_INH_PIN = 5;  ///< Inhibit pin (LOW=enabled, HIGH=disabled)
    static constexpr int MUX_S0_PIN = 6;   ///< Channel select bit 0 (LSB)
    static constexpr int MUX_S1_PIN = 7;   ///< Channel select bit 1
    static constexpr int MUX_S2_PIN = 8;   ///< Channel select bit 2 (MSB)

public:
    /**
     * @brief Get inhibit pin number for external access
     *
     * Provides access to the inhibit pin number for use by other classes
     * (particularly the StateMachine) that need to control the enable/disable
     * state of the multiplexer.
     *
     * @return Pin number for the multiplexer inhibit control
     */
    static int inhPin() { return MUX_INH_PIN; }
};

#endif // MUXCONTROL_H