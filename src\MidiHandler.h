#ifndef MIDIHANDLER_H
#define MIDIHANDLER_H

#include <Arduino.h>

/**
 * @class MidiHandler
 * @brief Encapsulates MIDI receive/parse, mapping to DAC channels, and value storage.
 *
 * Usage:
 *   - Create a single instance of Midi<PERSON>and<PERSON> at the appropriate scope.
 *   - Use public methods to initialize and update MIDI state.
 *   - All state and handlers are encapsulated.
 */
class MidiHandler {
public:
    MidiHandler();

    // Initialization
    void init();

    // Main update loop
    void update();

    // MIDI Control Change handler for CC 20-27
    void controlChange(byte channel, byte control, byte value);

    // Accessor for CC values
    uint8_t getCC(uint8_t idx) const;

private:
    // MIDI CC state (was MidiCCState)
    uint8_t cc[8];

    // Singleton pointer for static access
    static MidiHandler* instance;

    // Static MIDI handler trampoline
    static void staticControlChange(byte channel, byte control, byte value);
};

#endif // MIDIHANDLER_H