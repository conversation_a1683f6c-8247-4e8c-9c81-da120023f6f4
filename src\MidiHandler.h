#ifndef MIDIHANDLER_H
#define MIDIHANDLER_H

#include <Arduino.h>

/**
 * @class MidiHandler
 * @brief USB MIDI input processing and Control Change mapping system
 *
 * The MidiHandler class provides a complete MIDI input processing system
 * specifically designed for real-time control of the DacMux system. It
 * handles USB MIDI input, processes Control Change messages, and maintains
 * current CC values for use by the DAC routing system.
 *
 * MIDI Control Change Mapping:
 * - CC 20 → DAC Channel 0
 * - CC 21 → DAC Channel 1
 * - CC 22 → DAC Channel 2
 * - CC 23 → DAC Channel 3
 * - CC 24 → DAC Channel 4
 * - CC 25 → DAC Channel 5
 * - CC 26 → DAC Channel 6
 * - CC 27 → DAC Channel 7
 *
 * The system responds to MIDI messages on all channels and stores the most
 * recent value for each Control Change number. Values are automatically
 * scaled from MIDI range (0-127) to DAC range (0-4095) when used.
 *
 * @note Requires Teensy USB Type to be set to "MIDI" in Arduino IDE
 * @note Uses <PERSON><PERSON>'s built-in usbMIDI library for USB MIDI communication
 * @note Only Control Change messages are processed; other MIDI messages are ignored
 *
 * Usage Example:
 * @code
 * MidiHandler midi;
 * midi.init();
 * // In main loop:
 * midi.update();
 * uint8_t ccValue = midi.getCC(0);  // Get CC20 value for channel 0
 * @endcode
 */
class MidiHandler {
public:
    /**
     * @brief Constructor - initializes CC value storage and singleton pointer
     *
     * Sets all Control Change values to 0 and establishes the singleton
     * pointer needed for static callback function access.
     */
    MidiHandler();

    /**
     * @brief Initialize MIDI input system
     *
     * Sets up the USB MIDI input handler and registers the Control Change
     * callback function. Must be called before processing MIDI input.
     *
     * @note Requires Teensy USB Type to be configured as "MIDI"
     * @note The Teensy will appear as a MIDI device to the host computer
     */
    void init();

    /**
     * @brief Update MIDI input processing
     *
     * Polls for incoming USB MIDI messages and processes them. This function
     * is non-blocking and should be called regularly from the main loop to
     * ensure responsive MIDI input handling.
     *
     * @note This function must be called frequently for real-time MIDI response
     * @note Processing time is minimal when no MIDI data is present
     */
    void update();

    /**
     * @brief Process incoming MIDI Control Change messages
     *
     * Handles Control Change messages in the range CC 20-27, mapping them
     * to DAC channels 0-7 respectively. Messages outside this range are
     * ignored. The function stores the raw MIDI value (0-127) for later
     * scaling when used by the DAC system.
     *
     * @param channel MIDI channel (1-16, currently ignored - responds to all channels)
     * @param control Control Change number (20-27 are processed)
     * @param value Control Change value (0-127)
     *
     * @note MIDI channel parameter is currently ignored (omni mode)
     * @note Only CC 20-27 are processed; other CC numbers are ignored
     * @note Values are stored as received and scaled when retrieved
     */
    void controlChange(byte channel, byte control, byte value);

    /**
     * @brief Get current Control Change value for a DAC channel
     *
     * Returns the most recently received MIDI Control Change value for
     * the specified DAC channel. Values are returned in MIDI range (0-127)
     * and should be scaled to DAC range (0-4095) when used.
     *
     * Channel to CC Mapping:
     * - Channel 0 → CC 20
     * - Channel 1 → CC 21
     * - Channel 2 → CC 22
     * - Channel 3 → CC 23
     * - Channel 4 → CC 24
     * - Channel 5 → CC 25
     * - Channel 6 → CC 26
     * - Channel 7 → CC 27
     *
     * @param idx DAC channel index (0-7)
     * @return MIDI CC value (0-127), or 0 if idx is out of range
     *
     * @note Values are in MIDI range and need scaling for DAC use
     * @note Invalid channel indices return 0
     */
    uint8_t getCC(uint8_t idx) const;

private:
    /**
     * @brief Control Change value storage array
     *
     * Stores the most recent MIDI Control Change values for CC 20-27.
     * Array index corresponds to DAC channel (0-7), values are in
     * MIDI range (0-127).
     */
    uint8_t cc[8];

    /**
     * @brief Singleton instance pointer
     *
     * Required for static callback function access. The USB MIDI library
     * requires static callback functions, but we need access to instance
     * data, so we use this singleton pattern.
     */
    static MidiHandler* instance;

    /**
     * @brief Static callback function for USB MIDI Control Change messages
     *
     * This static function is registered with the USB MIDI library and
     * forwards Control Change messages to the instance method. Required
     * because the USB MIDI library expects static callback functions.
     *
     * @param channel MIDI channel (1-16)
     * @param control Control Change number
     * @param value Control Change value (0-127)
     */
    static void staticControlChange(byte channel, byte control, byte value);
};

#endif // MIDIHANDLER_H